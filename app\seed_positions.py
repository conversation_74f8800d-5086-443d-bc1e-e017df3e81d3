from typing import Optional

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.models import User, Position


async def seed_default_position(session: AsyncSession) -> None:
    """
    Idempotently ensure a default IT Administrator Position exists and is linked to the admin user.

    Behavior:
    - Create Position(code="IT_ADMIN") if missing with:
        title="IT Administrator"
        code="IT_ADMIN"
        description="Administrative IT position"
        level=1
        is_active=True
        department_id: copied from the admin user's department_id
        branch_id: copied from the admin user's branch_id (may be None)
    - Link the admin user (role='admin' preferred; fallback username='admin') to this position if not already linked.
    - Safe to run multiple times (no duplicates; no relinking if already set).
    """

    # 1) Find the admin user (prefer role='admin', fallback to username='admin')
    admin_user: Optional[User] = None

    result = await session.execute(
        select(User).where(User.role == "admin").limit(1)
    )
    admin_user = result.scalar_one_or_none()

    if admin_user is None:
        result = await session.execute(
            select(User).where(User.username == "admin").limit(1)
        )
        admin_user = result.scalar_one_or_none()

    if admin_user is None:
        # No admin user yet; nothing to seed/link safely
        return

    # 2) Ensure Position with code="IT_ADMIN" exists
    result = await session.execute(
        select(Position).where(Position.code == "IT_ADMIN").limit(1)
    )
    position = result.scalar_one_or_none()

    if position is None:
        position = Position(
            title="IT Administrator",
            code="IT_ADMIN",
            description="Administrative IT position",
            level=1,
            is_active=True,
            department_id=admin_user.department_id,  # may be None if admin has no department
            branch_id=admin_user.branch_id,          # may be None if admin has no branch
        )
        session.add(position)
        # Flush to get the new position.id without committing the entire transaction
        await session.flush()

    # 3) Link admin user to position if not already linked
    if getattr(admin_user, "position_id", None) is None:
        admin_user.position_id = position.id  # type: ignore[assignment]
        await session.flush()