# Railway Production Environment Variables
# Add these to Railway dashboard Variables section

# Database (Railway PostgreSQL)
DATABASE_URL=postgresql+asyncpg://postgres:<EMAIL>:47060/railway

# MinIO (Railway Bucket)
MINIO_ENDPOINT=${CONSOLE_MINIO_SERVER}
MINIO_ACCESS_KEY=${USERNAME}
MINIO_SECRET_KEY=${PASSWORD}
MINIO_BUCKET_NAME=lc-workflow-files
MINIO_SECURE=true

# Security
SECRET_KEY=bVFPW_9Y6supHRuA49bKh7Wq4OSSg03l2Z50wy4NWnQ
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# CORS - Include both production and development origins
CORS_ORIGINS=https://your-app.railway.app,http://localhost:3000,http://127.0.0.1:3000

# Server
HOST=0.0.0.0
PORT=8000
DEBUG=false
