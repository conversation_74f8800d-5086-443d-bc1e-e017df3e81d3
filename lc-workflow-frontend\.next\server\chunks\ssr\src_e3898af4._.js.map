{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/backend/lc-workflow-frontend/src/hooks/useBranches.ts"], "sourcesContent": ["import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';\nimport { apiClient } from '@/lib/api';\nimport { Branch, PaginatedResponse } from '@/types/models';\nimport toast from 'react-hot-toast';\n\n// Branch query keys\nexport const branchKeys = {\n  all: ['branches'] as const,\n  lists: () => [...branchKeys.all, 'list'] as const,\n  list: (filters: any) => [...branchKeys.lists(), filters] as const,\n  details: () => [...branchKeys.all, 'detail'] as const,\n  detail: (id: string) => [...branchKeys.details(), id] as const,\n};\n\n// Branch hooks\nexport const useBranches = (filters: {\n  page?: number;\n  size?: number;\n  search?: string;\n  is_active?: boolean;\n} = {}) => {\n  return useQuery({\n    queryKey: branchKeys.list(filters),\n    queryFn: () => apiClient.get<PaginatedResponse<Branch>>('/branches', {\n      params: filters,\n    }),\n    staleTime: 5 * 60 * 1000, // 5 minutes\n  });\n};\n\nexport const useActiveBranches = () => {\n  return useQuery({\n    queryKey: branchKeys.list({ is_active: true }),\n    queryFn: () => apiClient.get<Branch[]>('/branches/active'),\n    staleTime: 5 * 60 * 1000,\n  });\n};\n\nexport const useBranch = (id: string) => {\n  return useQuery({\n    queryKey: branchKeys.detail(id),\n    queryFn: () => apiClient.get<Branch>(`/branches/${id}`),\n    staleTime: 5 * 60 * 1000,\n    enabled: !!id && id !== 'undefined' && /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/.test(id),\n  });\n};\n\nexport const useCreateBranch = () => {\n  const queryClient = useQueryClient();\n\n  return useMutation({\n    mutationFn: (data: {\n      name: string;\n      code: string;\n      address: string;\n      phone_number?: string;\n      email?: string;\n      manager_id?: string;\n      latitude?: number;\n      longitude?: number;\n    }) => apiClient.post<Branch>('/branches', data),\n    onSuccess: () => {\n      queryClient.invalidateQueries({ queryKey: branchKeys.lists() });\n      toast.success('Branch created successfully!');\n    },\n    onError: (error: any) => {\n      const message = error.response?.data?.detail || 'Failed to create branch';\n      toast.error(message);\n    },\n  });\n};\n\nexport const useUpdateBranch = (id: string) => {\n  const queryClient = useQueryClient();\n\n  return useMutation({\n    mutationFn: (data: {\n      name?: string;\n      code?: string;\n      address?: string;\n      phone_number?: string;\n      email?: string;\n      manager_id?: string;\n      latitude?: number;\n      longitude?: number;\n      is_active?: boolean;\n    }) => {\n      if (!id || id === 'undefined' || !/^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/.test(id)) {\n        throw new Error('Invalid branch ID format');\n      }\n      return apiClient.patch<Branch>(`/branches/${id}`, data);\n    },\n    onSuccess: (updatedBranch) => {\n      queryClient.setQueryData(branchKeys.detail(id), updatedBranch);\n      queryClient.invalidateQueries({ queryKey: branchKeys.lists() });\n      toast.success('Branch updated successfully!');\n    },\n    onError: (error: any) => {\n      const message = error.response?.data?.detail || 'Failed to update branch';\n      toast.error(message);\n    },\n  });\n};\n\nexport const useDeleteBranch = () => {\n  const queryClient = useQueryClient();\n\n  return useMutation({\n    mutationFn: (id: string) => {\n      if (!id || id === 'undefined' || !/^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/.test(id)) {\n        throw new Error('Invalid branch ID format');\n      }\n      return apiClient.delete(`/branches/${id}`);\n    },\n    onSuccess: () => {\n      queryClient.invalidateQueries({ queryKey: branchKeys.lists() });\n      toast.success('Branch deleted successfully!');\n    },\n    onError: (error: any) => {\n      const message = error.response?.data?.detail || 'Failed to delete branch';\n      toast.error(message);\n    },\n  });\n};"], "names": [], "mappings": ";;;;;;;;;AAAA;AAAA;AAAA;AACA;AAEA;;;;AAGO,MAAM,aAAa;IACxB,KAAK;QAAC;KAAW;IACjB,OAAO,IAAM;eAAI,WAAW,GAAG;YAAE;SAAO;IACxC,MAAM,CAAC,UAAiB;eAAI,WAAW,KAAK;YAAI;SAAQ;IACxD,SAAS,IAAM;eAAI,WAAW,GAAG;YAAE;SAAS;IAC5C,QAAQ,CAAC,KAAe;eAAI,WAAW,OAAO;YAAI;SAAG;AACvD;AAGO,MAAM,cAAc,CAAC,UAKxB,CAAC,CAAC;IACJ,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,WAAW,IAAI,CAAC;QAC1B,SAAS,IAAM,iHAAA,CAAA,YAAS,CAAC,GAAG,CAA4B,aAAa;gBACnE,QAAQ;YACV;QACA,WAAW,IAAI,KAAK;IACtB;AACF;AAEO,MAAM,oBAAoB;IAC/B,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,WAAW,IAAI,CAAC;YAAE,WAAW;QAAK;QAC5C,SAAS,IAAM,iHAAA,CAAA,YAAS,CAAC,GAAG,CAAW;QACvC,WAAW,IAAI,KAAK;IACtB;AACF;AAEO,MAAM,YAAY,CAAC;IACxB,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,WAAW,MAAM,CAAC;QAC5B,SAAS,IAAM,iHAAA,CAAA,YAAS,CAAC,GAAG,CAAS,CAAC,UAAU,EAAE,IAAI;QACtD,WAAW,IAAI,KAAK;QACpB,SAAS,CAAC,CAAC,MAAM,OAAO,eAAe,gFAAgF,IAAI,CAAC;IAC9H;AACF;AAEO,MAAM,kBAAkB;IAC7B,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC,OASP,iHAAA,CAAA,YAAS,CAAC,IAAI,CAAS,aAAa;QAC1C,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU,WAAW,KAAK;YAAG;YAC7D,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QAChB;QACA,SAAS,CAAC;YACR,MAAM,UAAU,MAAM,QAAQ,EAAE,MAAM,UAAU;YAChD,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd;IACF;AACF;AAEO,MAAM,kBAAkB,CAAC;IAC9B,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC;YAWX,IAAI,CAAC,MAAM,OAAO,eAAe,CAAC,gFAAgF,IAAI,CAAC,KAAK;gBAC1H,MAAM,IAAI,MAAM;YAClB;YACA,OAAO,iHAAA,CAAA,YAAS,CAAC,KAAK,CAAS,CAAC,UAAU,EAAE,IAAI,EAAE;QACpD;QACA,WAAW,CAAC;YACV,YAAY,YAAY,CAAC,WAAW,MAAM,CAAC,KAAK;YAChD,YAAY,iBAAiB,CAAC;gBAAE,UAAU,WAAW,KAAK;YAAG;YAC7D,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QAChB;QACA,SAAS,CAAC;YACR,MAAM,UAAU,MAAM,QAAQ,EAAE,MAAM,UAAU;YAChD,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd;IACF;AACF;AAEO,MAAM,kBAAkB;IAC7B,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC;YACX,IAAI,CAAC,MAAM,OAAO,eAAe,CAAC,gFAAgF,IAAI,CAAC,KAAK;gBAC1H,MAAM,IAAI,MAAM;YAClB;YACA,OAAO,iHAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,UAAU,EAAE,IAAI;QAC3C;QACA,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU,WAAW,KAAK;YAAG;YAC7D,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QAChB;QACA,SAAS,CAAC;YACR,MAAM,UAAU,MAAM,QAAQ,EAAE,MAAM,UAAU;YAChD,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd;IACF;AACF", "debugId": null}}, {"offset": {"line": 132, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/backend/lc-workflow-frontend/src/components/layout/Sidebar.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { \n  HomeIcon, \n  DocumentTextIcon, \n  UsersIcon, \n  BuildingOfficeIcon, \n  MapPinIcon, \n  Cog6ToothIcon,\n  FolderIcon,\n  ChevronDownIcon,\n  ChevronRightIcon,\n  XMarkIcon,\n  Bars3Icon,\n} from '@heroicons/react/24/outline';\nimport { useAuthContext } from '@/providers/AuthProvider';\n\ninterface NavItem {\n  name: string;\n  href: string;\n  icon: React.ComponentType<{ className?: string }>;\n  requiredRoles?: string[];\n}\n\nconst navigation: NavItem[] = [\n  { name: 'Dashboard', href: '/dashboard', icon: HomeIcon },\n  { name: 'Applications', href: '/applications', icon: DocumentTextIcon },\n  { name: 'Files', href: '/files', icon: FolderIcon },\n];\n\nconst adminNavigation: NavItem[] = [\n  { name: 'Users', href: '/users', icon: UsersIcon, requiredRoles: ['admin', 'manager'] },\n  { name: 'Departments', href: '/departments', icon: BuildingOfficeIcon, requiredRoles: ['admin', 'manager'] },\n  { name: 'Branches', href: '/branches', icon: MapPinIcon, requiredRoles: ['admin', 'manager'] },\n  { name: 'Settings', href: '/settings', icon: Cog6ToothIcon, requiredRoles: ['admin'] },\n];\n\ninterface SidebarProps {\n  isOpen: boolean;\n  onClose: () => void;\n}\n\nexport function Sidebar({ isOpen, onClose }: SidebarProps) {\n  const pathname = usePathname();\n  const { user, isAdmin, isManager } = useAuthContext();\n\n  const hasRequiredRole = (requiredRoles?: string[]) => {\n    if (!requiredRoles) return true;\n    return isAdmin || (isManager && requiredRoles.includes('manager'));\n  };\n\n  const allNavigation = [...navigation, ...adminNavigation];\n\n  const NavLink = ({ item }: { item: NavItem }) => {\n    const isActive = pathname === item.href;\n    \n    if (!hasRequiredRole(item.requiredRoles)) return null;\n\n    return (\n      <Link\n        href={item.href}\n        className={`group flex items-center gap-x-3 rounded-md p-2 text-sm font-semibold leading-6 transition-all duration-200 ${\n          isActive\n            ? 'bg-blue-600 text-white'\n            : 'text-gray-700 hover:bg-gray-50 hover:text-blue-600'\n        }`}\n        onClick={onClose}\n      >\n        <item.icon className=\"h-5 w-5 shrink-0\" />\n        {item.name}\n      </Link>\n    );\n  };\n\n  return (\n    <>\n      {/* Mobile sidebar */}\n      <div\n        className={`fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:hidden ${\n          isOpen ? 'translate-x-0' : '-translate-x-full'\n        }`}\n      >\n        <div className=\"flex h-16 items-center justify-between px-4\">\n          <div className=\"text-xl font-bold text-gray-900\">LC Workflow</div>\n          <button\n            type=\"button\"\n            className=\"-m-2.5 p-2.5 text-gray-700\"\n            onClick={onClose}\n          >\n            <XMarkIcon className=\"h-6 w-6\" />\n          </button>\n        </div>\n        <nav className=\"flex flex-1 flex-col gap-y-2 p-4\">\n          {allNavigation.map((item) => (\n            <NavLink key={item.name} item={item} />\n          ))}\n        </nav>\n      </div>\n\n      {/* Desktop sidebar */}\n      <div className=\"hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-64 lg:flex-col\">\n        <div className=\"flex grow flex-col gap-y-5 overflow-y-auto bg-white px-6 ring-1 ring-gray-200\">\n          <div className=\"flex h-16 shrink-0 items-center\">\n            <div className=\"text-xl font-bold text-gray-900\">LC Workflow</div>\n          </div>\n          <nav className=\"flex flex-1 flex-col gap-y-2\">\n            {allNavigation.map((item) => (\n              <NavLink key={item.name} item={item} />\n            ))}\n          </nav>\n          \n          {/* User info */}\n          {user && (\n            <div className=\"mb-4\">\n              <div className=\"flex items-center gap-x-3 rounded-md p-2 text-sm font-medium text-gray-700\">\n                <div className=\"h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center\">\n                  <span className=\"text-sm font-medium text-gray-600\">\n                    {user.first_name.charAt(0)}{user.last_name.charAt(0)}\n                  </span>\n                </div>\n                <div>\n                  <div className=\"font-medium\">{user.first_name} {user.last_name}</div>\n                  <div className=\"text-xs text-gray-500\">{user.role}</div>\n                </div>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </>\n  );\n}"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AAlBA;;;;;;AA2BA,MAAM,aAAwB;IAC5B;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,+MAAA,CAAA,WAAQ;IAAC;IACxD;QAAE,MAAM;QAAgB,MAAM;QAAiB,MAAM,+NAAA,CAAA,mBAAgB;IAAC;IACtE;QAAE,MAAM;QAAS,MAAM;QAAU,MAAM,mNAAA,CAAA,aAAU;IAAC;CACnD;AAED,MAAM,kBAA6B;IACjC;QAAE,MAAM;QAAS,MAAM;QAAU,MAAM,iNAAA,CAAA,YAAS;QAAE,eAAe;YAAC;YAAS;SAAU;IAAC;IACtF;QAAE,MAAM;QAAe,MAAM;QAAgB,MAAM,mOAAA,CAAA,qBAAkB;QAAE,eAAe;YAAC;YAAS;SAAU;IAAC;IAC3G;QAAE,MAAM;QAAY,MAAM;QAAa,MAAM,mNAAA,CAAA,aAAU;QAAE,eAAe;YAAC;YAAS;SAAU;IAAC;IAC7F;QAAE,MAAM;QAAY,MAAM;QAAa,MAAM,yNAAA,CAAA,gBAAa;QAAE,eAAe;YAAC;SAAQ;IAAC;CACtF;AAOM,SAAS,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAgB;IACvD,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IAElD,MAAM,kBAAkB,CAAC;QACvB,IAAI,CAAC,eAAe,OAAO;QAC3B,OAAO,WAAY,aAAa,cAAc,QAAQ,CAAC;IACzD;IAEA,MAAM,gBAAgB;WAAI;WAAe;KAAgB;IAEzD,MAAM,UAAU,CAAC,EAAE,IAAI,EAAqB;QAC1C,MAAM,WAAW,aAAa,KAAK,IAAI;QAEvC,IAAI,CAAC,gBAAgB,KAAK,aAAa,GAAG,OAAO;QAEjD,qBACE,8OAAC,4JAAA,CAAA,UAAI;YACH,MAAM,KAAK,IAAI;YACf,WAAW,CAAC,2GAA2G,EACrH,WACI,2BACA,sDACJ;YACF,SAAS;;8BAET,8OAAC,KAAK,IAAI;oBAAC,WAAU;;;;;;gBACpB,KAAK,IAAI;;;;;;;IAGhB;IAEA,qBACE;;0BAEE,8OAAC;gBACC,WAAW,CAAC,sHAAsH,EAChI,SAAS,kBAAkB,qBAC3B;;kCAEF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAkC;;;;;;0CACjD,8OAAC;gCACC,MAAK;gCACL,WAAU;gCACV,SAAS;0CAET,cAAA,8OAAC,iNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAGzB,8OAAC;wBAAI,WAAU;kCACZ,cAAc,GAAG,CAAC,CAAC,qBAClB,8OAAC;gCAAwB,MAAM;+BAAjB,KAAK,IAAI;;;;;;;;;;;;;;;;0BAM7B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CAAkC;;;;;;;;;;;sCAEnD,8OAAC;4BAAI,WAAU;sCACZ,cAAc,GAAG,CAAC,CAAC,qBAClB,8OAAC;oCAAwB,MAAM;mCAAjB,KAAK,IAAI;;;;;;;;;;wBAK1B,sBACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;;gDACb,KAAK,UAAU,CAAC,MAAM,CAAC;gDAAI,KAAK,SAAS,CAAC,MAAM,CAAC;;;;;;;;;;;;kDAGtD,8OAAC;;0DACC,8OAAC;gDAAI,WAAU;;oDAAe,KAAK,UAAU;oDAAC;oDAAE,KAAK,SAAS;;;;;;;0DAC9D,8OAAC;gDAAI,WAAU;0DAAyB,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASnE", "debugId": null}}, {"offset": {"line": 413, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/backend/lc-workflow-frontend/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useRef, useEffect } from 'react';\r\nimport { Bars3Icon, BellIcon, UserCircleIcon, ArrowRightOnRectangleIcon, Cog6ToothIcon } from '@heroicons/react/24/outline';\r\nimport { useLogout } from '@/hooks/useAuth';\r\nimport { useAuthContext } from '@/providers/AuthProvider';\r\nimport Link from 'next/link';\r\n\r\ninterface HeaderProps {\r\n  onMenuClick: () => void;\r\n}\r\n\r\nexport function Header({ onMenuClick }: HeaderProps) {\r\n  const logout = useLogout();\r\n  const { user } = useAuthContext();\r\n  const [profileDropdownOpen, setProfileDropdownOpen] = useState(false);\r\n  const dropdownRef = useRef<HTMLDivElement>(null);\r\n\r\n  // Close dropdown when clicking outside\r\n  useEffect(() => {\r\n    function handleClickOutside(event: MouseEvent) {\r\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\r\n        setProfileDropdownOpen(false);\r\n      }\r\n    }\r\n\r\n    document.addEventListener('mousedown', handleClickOutside);\r\n    return () => {\r\n      document.removeEventListener('mousedown', handleClickOutside);\r\n    };\r\n  }, []);\r\n\r\n  return (\r\n    <div className=\"sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 bg-white px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8\">\r\n      <button\r\n        type=\"button\"\r\n        className=\"-m-2.5 p-2.5 text-gray-700 lg:hidden\"\r\n        onClick={onMenuClick}\r\n      >\r\n        <span className=\"sr-only\">Open sidebar</span>\r\n        <Bars3Icon className=\"h-6 w-6\" aria-hidden=\"true\" />\r\n      </button>\r\n\r\n      {/* Separator */}\r\n      <div className=\"h-6 w-px bg-gray-900/10 lg:hidden\" aria-hidden=\"true\" />\r\n\r\n      <div className=\"flex flex-1 gap-x-4 self-stretch lg:gap-x-6\">\r\n        <div className=\"flex flex-1\" />\r\n\r\n        <div className=\"flex items-center gap-x-4 lg:gap-x-6\">\r\n          <button\r\n            type=\"button\"\r\n            className=\"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500\"\r\n          >\r\n            <span className=\"sr-only\">View notifications</span>\r\n            <BellIcon className=\"h-6 w-6\" aria-hidden=\"true\" />\r\n          </button>\r\n\r\n          {/* Separator */}\r\n          <div className=\"hidden lg:block lg:h-6 lg:w-px lg:bg-gray-900/10\" aria-hidden=\"true\" />\r\n\r\n          {/* Profile dropdown */}\r\n          <div className=\"relative\">\r\n            <button\r\n              type=\"button\"\r\n              className=\"-m-1.5 flex items-center p-1.5\"\r\n              id=\"user-menu-button\"\r\n              aria-expanded=\"false\"\r\n              aria-haspopup=\"true\"\r\n              onClick={() => setProfileDropdownOpen(!profileDropdownOpen)}\r\n            >\r\n              <span className=\"sr-only\">Open user menu</span>\r\n              <div className=\"h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center\">\r\n                <span className=\"text-sm font-medium text-gray-600\">\r\n                  {user?.first_name?.charAt(0)}{user?.last_name?.charAt(0)}\r\n                </span>\r\n              </div>\r\n              <span className=\"hidden lg:flex lg:items-center\">\r\n                <span className=\"ml-4 text-sm leading-6 text-gray-900\" aria-hidden=\"true\">\r\n                  <span className=\"font-semibold\">{user?.first_name} {user?.last_name}</span>\r\n                  {user?.position?.title ? (\r\n                    <span className=\"ml-2 text-gray-500 truncate max-w-[10rem]\">\r\n                      • {user?.position?.title}\r\n                    </span>\r\n                  ) : null}\r\n                </span>\r\n              </span>\r\n            </button>\r\n\r\n            {/* Dropdown menu */}\r\n            {profileDropdownOpen && (\r\n              <div\r\n                className=\"absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none\"\r\n                role=\"menu\"\r\n                aria-orientation=\"vertical\"\r\n                aria-labelledby=\"user-menu-button\"\r\n                ref={dropdownRef}\r\n              >\r\n                <div className=\"px-4 py-3\">\r\n                  <p className=\"text-sm\">Signed in as</p>\r\n                  <p className=\"truncate text-sm font-medium text-gray-900\">{user?.email}</p>\r\n                </div>\r\n                <div className=\"border-t border-gray-100\"></div>\r\n                <Link\r\n                  href=\"/profile\"\r\n                  className=\"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\r\n                  role=\"menuitem\"\r\n                  onClick={() => setProfileDropdownOpen(false)}\r\n                >\r\n                  <UserCircleIcon className=\"mr-3 h-5 w-5 text-gray-400\" aria-hidden=\"true\" />\r\n                  Your Profile\r\n                </Link>\r\n                <Link\r\n                  href=\"/settings\"\r\n                  className=\"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\r\n                  role=\"menuitem\"\r\n                  onClick={() => setProfileDropdownOpen(false)}\r\n                >\r\n                  <Cog6ToothIcon className=\"mr-3 h-5 w-5 text-gray-400\" aria-hidden=\"true\" />\r\n                  Settings\r\n                </Link>\r\n                <div className=\"border-t border-gray-100\"></div>\r\n                <button\r\n                  type=\"button\"\r\n                  className=\"flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\r\n                  role=\"menuitem\"\r\n                  onClick={() => {\r\n                    setProfileDropdownOpen(false);\r\n                    logout.mutate();\r\n                  }}\r\n                >\r\n                  <ArrowRightOnRectangleIcon className=\"mr-3 h-5 w-5 text-gray-400\" aria-hidden=\"true\" />\r\n                  Sign out\r\n                </button>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AANA;;;;;;;AAYO,SAAS,OAAO,EAAE,WAAW,EAAe;IACjD,MAAM,SAAS,CAAA,GAAA,uHAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IAC9B,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE3C,uCAAuC;IACvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS,mBAAmB,KAAiB;YAC3C,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;gBAC9E,uBAAuB;YACzB;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;QAC5C;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBACC,MAAK;gBACL,WAAU;gBACV,SAAS;;kCAET,8OAAC;wBAAK,WAAU;kCAAU;;;;;;kCAC1B,8OAAC,iNAAA,CAAA,YAAS;wBAAC,WAAU;wBAAU,eAAY;;;;;;;;;;;;0BAI7C,8OAAC;gBAAI,WAAU;gBAAoC,eAAY;;;;;;0BAE/D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCAEf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,MAAK;gCACL,WAAU;;kDAEV,8OAAC;wCAAK,WAAU;kDAAU;;;;;;kDAC1B,8OAAC,+MAAA,CAAA,WAAQ;wCAAC,WAAU;wCAAU,eAAY;;;;;;;;;;;;0CAI5C,8OAAC;gCAAI,WAAU;gCAAmD,eAAY;;;;;;0CAG9E,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,WAAU;wCACV,IAAG;wCACH,iBAAc;wCACd,iBAAc;wCACd,SAAS,IAAM,uBAAuB,CAAC;;0DAEvC,8OAAC;gDAAK,WAAU;0DAAU;;;;;;0DAC1B,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;;wDACb,MAAM,YAAY,OAAO;wDAAI,MAAM,WAAW,OAAO;;;;;;;;;;;;0DAG1D,8OAAC;gDAAK,WAAU;0DACd,cAAA,8OAAC;oDAAK,WAAU;oDAAuC,eAAY;;sEACjE,8OAAC;4DAAK,WAAU;;gEAAiB,MAAM;gEAAW;gEAAE,MAAM;;;;;;;wDACzD,MAAM,UAAU,sBACf,8OAAC;4DAAK,WAAU;;gEAA4C;gEACvD,MAAM,UAAU;;;;;;mEAEnB;;;;;;;;;;;;;;;;;;oCAMT,qCACC,8OAAC;wCACC,WAAU;wCACV,MAAK;wCACL,oBAAiB;wCACjB,mBAAgB;wCAChB,KAAK;;0DAEL,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAU;;;;;;kEACvB,8OAAC;wDAAE,WAAU;kEAA8C,MAAM;;;;;;;;;;;;0DAEnE,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,MAAK;gDACL,SAAS,IAAM,uBAAuB;;kEAEtC,8OAAC,2NAAA,CAAA,iBAAc;wDAAC,WAAU;wDAA6B,eAAY;;;;;;oDAAS;;;;;;;0DAG9E,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,MAAK;gDACL,SAAS,IAAM,uBAAuB;;kEAEtC,8OAAC,yNAAA,CAAA,gBAAa;wDAAC,WAAU;wDAA6B,eAAY;;;;;;oDAAS;;;;;;;0DAG7E,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDACC,MAAK;gDACL,WAAU;gDACV,MAAK;gDACL,SAAS;oDACP,uBAAuB;oDACvB,OAAO,MAAM;gDACf;;kEAEA,8OAAC,iPAAA,CAAA,4BAAyB;wDAAC,WAAU;wDAA6B,eAAY;;;;;;oDAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUzG", "debugId": null}}, {"offset": {"line": 765, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/backend/lc-workflow-frontend/src/components/layout/Layout.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Sidebar } from './Sidebar';\nimport { Header } from './Header';\n\ninterface LayoutProps {\n  children: React.ReactNode;\n}\n\nexport function Layout({ children }: LayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n\n  return (\n    <>\n      <div className=\"min-h-full\">\n        <Sidebar \n          isOpen={sidebarOpen} \n          onClose={() => setSidebarOpen(false)} \n        />\n        \n        <div className=\"lg:pl-64\">\n          <Header onMenuClick={() => setSidebarOpen(true)} />\n          \n          <main className=\"py-10\">\n            <div className=\"px-4 sm:px-6 lg:px-8\">\n              {children}\n            </div>\n          </main>\n        </div>\n      </div>\n    </>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAUO,SAAS,OAAO,EAAE,QAAQ,EAAe;IAC9C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,qBACE;kBACE,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,uIAAA,CAAA,UAAO;oBACN,QAAQ;oBACR,SAAS,IAAM,eAAe;;;;;;8BAGhC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,sIAAA,CAAA,SAAM;4BAAC,aAAa,IAAM,eAAe;;;;;;sCAE1C,8OAAC;4BAAK,WAAU;sCACd,cAAA,8OAAC;gCAAI,WAAU;0CACZ;;;;;;;;;;;;;;;;;;;;;;;;AAOf", "debugId": null}}, {"offset": {"line": 834, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/backend/lc-workflow-frontend/src/components/auth/ProtectedRoute.tsx"], "sourcesContent": ["'use client';\n\nimport { useRouter } from 'next/navigation';\nimport { useAuthContext } from '@/providers/AuthProvider';\nimport { useEffect } from 'react';\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode;\n  requiredRoles?: string[];\n}\n\nexport function ProtectedRoute({ children, requiredRoles }: ProtectedRouteProps) {\n  const router = useRouter();\n  const { isAuthenticated, isLoading, user, role } = useAuthContext();\n\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      if (!isLoading) {\n        if (!isAuthenticated) {\n          router.push('/login');\n        } else if (requiredRoles && requiredRoles.length > 0 && !requiredRoles.includes(role || '')) {\n          router.push('/unauthorized');\n        }\n      }\n    }, 1000); // Wait 1 second for auth state to stabilize\n\n    return () => clearTimeout(timer);\n  }, [isAuthenticated, isLoading, requiredRoles, role, router]);\n\n  if (isLoading || !isAuthenticated) {\n    return (\n      <div className=\"flex min-h-screen items-center justify-center\">\n        <div className=\"h-8 w-8 animate-spin rounded-full border-4 border-blue-600 border-t-transparent\"></div>\n      </div>\n    );\n  }\n\n  if (requiredRoles && requiredRoles.length > 0 && !requiredRoles.includes(role || '')) {\n    return null;\n  }\n\n  return <>{children}</>;\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAWO,SAAS,eAAe,EAAE,QAAQ,EAAE,aAAa,EAAuB;IAC7E,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IAEhE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,WAAW;YACvB,IAAI,CAAC,WAAW;gBACd,IAAI,CAAC,iBAAiB;oBACpB,OAAO,IAAI,CAAC;gBACd,OAAO,IAAI,iBAAiB,cAAc,MAAM,GAAG,KAAK,CAAC,cAAc,QAAQ,CAAC,QAAQ,KAAK;oBAC3F,OAAO,IAAI,CAAC;gBACd;YACF;QACF,GAAG,OAAO,4CAA4C;QAEtD,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;QAAiB;QAAW;QAAe;QAAM;KAAO;IAE5D,IAAI,aAAa,CAAC,iBAAiB;QACjC,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,iBAAiB,cAAc,MAAM,GAAG,KAAK,CAAC,cAAc,QAAQ,CAAC,QAAQ,KAAK;QACpF,OAAO;IACT;IAEA,qBAAO;kBAAG;;AACZ", "debugId": null}}, {"offset": {"line": 894, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/backend/lc-workflow-frontend/src/app/branches/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useBranches, useDeleteBranch } from '@/hooks/useBranches';\nimport { Plus, Search, Edit, Trash2, Eye, MapPin, ChevronLeft, ChevronRight } from 'lucide-react';\nimport Link from 'next/link';\nimport { Layout } from '@/components/layout/Layout';\nimport { ProtectedRoute } from '@/components/auth/ProtectedRoute';\n\nexport default function BranchesPage() {\n  const [searchTerm, setSearchTerm] = useState('');\n\n  const [currentPage, setCurrentPage] = useState(1);\n  const [deleteConfirm, setDeleteConfirm] = useState<string | null>(null);\n  const pageSize = 10;\n\n  const { data: branchesData, isLoading, error } = useBranches({\n    page: currentPage,\n    size: pageSize,\n    search: searchTerm || undefined,\n  });\n\n  const deleteBranchMutation = useDeleteBranch();\n\n  const handleSearch = (e: React.FormEvent) => {\n    e.preventDefault();\n    setCurrentPage(1);\n  };\n\n  const handleDelete = async (branchId: string) => {\n    try {\n      await deleteBranchMutation.mutateAsync(branchId);\n      setDeleteConfirm(null);\n    } catch (error) {\n      console.error('Failed to delete branch:', error);\n    }\n  };\n\n  const totalPages = Math.ceil((branchesData?.total || 0) / pageSize);\n\n  if (error) {\n    return (\n      <ProtectedRoute>\n        <Layout>\n          <div className=\"flex items-center justify-center h-full\">\n            <div className=\"text-center\">\n              <h2 className=\"text-2xl font-bold text-gray-900\">Error loading branches</h2>\n              <p className=\"text-gray-600 mt-2\">Please try again later.</p>\n            </div>\n          </div>\n        </Layout>\n      </ProtectedRoute>\n    );\n  }\n\n  return (\n    <ProtectedRoute>\n      <Layout>\n        {/* Header */}\n        <div className=\"mb-8\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h1 className=\"text-3xl font-bold text-gray-900\">Branches</h1>\n              <p className=\"text-gray-600 mt-1\">Manage branch locations and information</p>\n            </div>\n            <Link\n              href=\"/branches/new\"\n              className=\"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n            >\n              <Plus className=\"h-4 w-4 mr-2\" />\n              Add Branch\n            </Link>\n          </div>\n        </div>\n\n        {/* Filters */}\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6\">\n          <form onSubmit={handleSearch} className=\"flex gap-4\">\n            <div className=\"flex-1\">\n              <div className=\"relative\">\n                <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\" />\n                <input\n                  type=\"text\"\n                  placeholder=\"Search branches...\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                />\n              </div>\n            </div>\n            <button\n              type=\"submit\"\n              className=\"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n            >\n              Search\n            </button>\n          </form>\n        </div>\n\n        {/* Branches List */}\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden\">\n          {isLoading ? (\n            <div className=\"flex items-center justify-center py-12\">\n              <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n            </div>\n          ) : branchesData?.items?.length === 0 ? (\n            <div className=\"text-center py-12\">\n              <MapPin className=\"mx-auto h-12 w-12 text-gray-400\" />\n              <h3 className=\"mt-2 text-sm font-medium text-gray-900\">No branches found</h3>\n              <p className=\"mt-1 text-sm text-gray-500\">\n                {searchTerm\n                  ? 'Try adjusting your search criteria.'\n                  : 'Get started by creating a new branch.'}\n              </p>\n              {!searchTerm && (\n                <div className=\"mt-6\">\n                  <Link\n                    href=\"/branches/new\"\n                    className=\"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n                  >\n                    <Plus className=\"h-4 w-4 mr-2\" />\n                    Add Branch\n                  </Link>\n                </div>\n              )}\n            </div>\n          ) : (\n            <>\n              {/* Table Header */}\n              <div className=\"bg-gray-50 px-6 py-3 border-b border-gray-200\">\n                <div className=\"grid grid-cols-12 gap-4 text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  <div className=\"col-span-3\">Branch Name</div>\n                  <div className=\"col-span-2\">Code</div>\n                  <div className=\"col-span-4\">Address</div>\n                  <div className=\"col-span-2\">Phone</div>\n                  <div className=\"col-span-1 text-right\">Actions</div>\n                </div>\n              </div>\n\n              {/* Table Body */}\n              <div className=\"divide-y divide-gray-200\">\n                {branchesData?.items?.map((branch) => (\n                  <div key={branch.id} className=\"px-6 py-4 hover:bg-gray-50\">\n                    <div className=\"grid grid-cols-12 gap-4 items-center\">\n                      <div className=\"col-span-3\">\n                        <div className=\"flex items-center space-x-3\">\n                          <div className=\"h-10 w-10 bg-blue-100 rounded-lg flex items-center justify-center\">\n                            <MapPin className=\"h-5 w-5 text-blue-600\" />\n                          </div>\n                          <div>\n                            <p className=\"text-sm font-medium text-gray-900\">{branch.name}</p>\n                            <p className=\"text-xs text-gray-500\">ID: {branch.id}</p>\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"col-span-2\">\n                        <p className=\"text-sm font-medium text-gray-900\">{branch.code}</p>\n                      </div>\n                      <div className=\"col-span-4\">\n                        <p className=\"text-sm text-gray-900\">{branch.address || 'No address provided'}</p>\n                      </div>\n                      <div className=\"col-span-2\">\n                        <p className=\"text-sm text-gray-900\">{branch.phone_number || 'No phone'}</p>\n                      </div>\n                      <div className=\"col-span-1\">\n                        <div className=\"flex items-center justify-end space-x-2\">\n                          <Link\n                            href={`/branches/${branch.id}`}\n                            className=\"p-2 text-gray-400 hover:text-blue-600 transition-colors\"\n                            title=\"View branch\"\n                          >\n                            <Eye className=\"h-4 w-4\" />\n                          </Link>\n                          <Link\n                            href={`/branches/${branch.id}/edit`}\n                            className=\"p-2 text-gray-400 hover:text-blue-600 transition-colors\"\n                            title=\"Edit branch\"\n                          >\n                            <Edit className=\"h-4 w-4\" />\n                          </Link>\n                          <button\n                            onClick={() => setDeleteConfirm(branch.id)}\n                            className=\"p-2 text-gray-400 hover:text-red-600 transition-colors\"\n                            title=\"Delete branch\"\n                          >\n                            <Trash2 className=\"h-4 w-4\" />\n                          </button>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n\n              {/* Pagination */}\n              {totalPages > 1 && (\n                <div className=\"bg-gray-50 px-6 py-3 border-t border-gray-200\">\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"text-sm text-gray-700\">\n                      Showing {((currentPage - 1) * pageSize) + 1} to {Math.min(currentPage * pageSize, branchesData?.total || 0)} of {branchesData?.total || 0} results\n                    </div>\n                    <div className=\"flex items-center space-x-2\">\n                      <button\n                        onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}\n                        disabled={currentPage === 1}\n                        className=\"p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed\"\n                      >\n                        <ChevronLeft className=\"h-4 w-4\" />\n                      </button>\n                      <span className=\"text-sm text-gray-700\">\n                        Page {currentPage} of {totalPages}\n                      </span>\n                      <button\n                        onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}\n                        disabled={currentPage === totalPages}\n                        className=\"p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed\"\n                      >\n                        <ChevronRight className=\"h-4 w-4\" />\n                      </button>\n                    </div>\n                  </div>\n                </div>\n              )}\n            </>\n          )}\n        </div>\n\n      {/* Delete Confirmation Modal */}\n      {deleteConfirm && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n          <div className=\"bg-white rounded-lg p-6 max-w-md w-full mx-4\">\n            <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Delete Branch</h3>\n            <p className=\"text-sm text-gray-600 mb-6\">\n              Are you sure you want to delete this branch? This action cannot be undone.\n            </p>\n            <div className=\"flex justify-end space-x-3\">\n              <button\n                onClick={() => setDeleteConfirm(null)}\n                className=\"px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors\"\n              >\n                Cancel\n              </button>\n              <button\n                onClick={() => handleDelete(deleteConfirm)}\n                disabled={deleteBranchMutation.isPending}\n                className=\"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50\"\n              >\n                {deleteBranchMutation.isPending ? 'Deleting...' : 'Delete'}\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n      </Layout>\n    </ProtectedRoute>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAPA;;;;;;;;AASe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,WAAW;IAEjB,MAAM,EAAE,MAAM,YAAY,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,cAAW,AAAD,EAAE;QAC3D,MAAM;QACN,MAAM;QACN,QAAQ,cAAc;IACxB;IAEA,MAAM,uBAAuB,CAAA,GAAA,2HAAA,CAAA,kBAAe,AAAD;IAE3C,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,eAAe;IACjB;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,MAAM,qBAAqB,WAAW,CAAC;YACvC,iBAAiB;QACnB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C;IACF;IAEA,MAAM,aAAa,KAAK,IAAI,CAAC,CAAC,cAAc,SAAS,CAAC,IAAI;IAE1D,IAAI,OAAO;QACT,qBACE,8OAAC,4IAAA,CAAA,iBAAc;sBACb,cAAA,8OAAC,sIAAA,CAAA,SAAM;0BACL,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;IAM9C;IAEA,qBACE,8OAAC,4IAAA,CAAA,iBAAc;kBACb,cAAA,8OAAC,sIAAA,CAAA,SAAM;;8BAEL,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAmC;;;;;;kDACjD,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;;;;;;;0CAEpC,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;8BAOvC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAK,UAAU;wBAAc,WAAU;;0CACtC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,WAAU;;;;;;;;;;;;;;;;;0CAIhB,8OAAC;gCACC,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;8BAOL,8OAAC;oBAAI,WAAU;8BACZ,0BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;;;;;;;;;+BAEf,cAAc,OAAO,WAAW,kBAClC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0MAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,8OAAC;gCAAE,WAAU;0CACV,aACG,wCACA;;;;;;4BAEL,CAAC,4BACA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;6CAOzC;;0CAEE,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAa;;;;;;sDAC5B,8OAAC;4CAAI,WAAU;sDAAa;;;;;;sDAC5B,8OAAC;4CAAI,WAAU;sDAAa;;;;;;sDAC5B,8OAAC;4CAAI,WAAU;sDAAa;;;;;;sDAC5B,8OAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;;;;;;0CAK3C,8OAAC;gCAAI,WAAU;0CACZ,cAAc,OAAO,IAAI,CAAC,uBACzB,8OAAC;wCAAoB,WAAU;kDAC7B,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC,0MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;;;;;;0EAEpB,8OAAC;;kFACC,8OAAC;wEAAE,WAAU;kFAAqC,OAAO,IAAI;;;;;;kFAC7D,8OAAC;wEAAE,WAAU;;4EAAwB;4EAAK,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;8DAIzD,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAE,WAAU;kEAAqC,OAAO,IAAI;;;;;;;;;;;8DAE/D,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAE,WAAU;kEAAyB,OAAO,OAAO,IAAI;;;;;;;;;;;8DAE1D,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAE,WAAU;kEAAyB,OAAO,YAAY,IAAI;;;;;;;;;;;8DAE/D,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,4JAAA,CAAA,UAAI;gEACH,MAAM,CAAC,UAAU,EAAE,OAAO,EAAE,EAAE;gEAC9B,WAAU;gEACV,OAAM;0EAEN,cAAA,8OAAC,gMAAA,CAAA,MAAG;oEAAC,WAAU;;;;;;;;;;;0EAEjB,8OAAC,4JAAA,CAAA,UAAI;gEACH,MAAM,CAAC,UAAU,EAAE,OAAO,EAAE,CAAC,KAAK,CAAC;gEACnC,WAAU;gEACV,OAAM;0EAEN,cAAA,8OAAC,2MAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;;;;;;0EAElB,8OAAC;gEACC,SAAS,IAAM,iBAAiB,OAAO,EAAE;gEACzC,WAAU;gEACV,OAAM;0EAEN,cAAA,8OAAC,0MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;uCA3ClB,OAAO,EAAE;;;;;;;;;;4BAqDtB,aAAa,mBACZ,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;gDAAwB;gDAC3B,CAAC,cAAc,CAAC,IAAI,WAAY;gDAAE;gDAAK,KAAK,GAAG,CAAC,cAAc,UAAU,cAAc,SAAS;gDAAG;gDAAK,cAAc,SAAS;gDAAE;;;;;;;sDAE5I,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,SAAS,IAAM,eAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,OAAO,GAAG;oDACzD,UAAU,gBAAgB;oDAC1B,WAAU;8DAEV,cAAA,8OAAC,oNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;;;;;;8DAEzB,8OAAC;oDAAK,WAAU;;wDAAwB;wDAChC;wDAAY;wDAAK;;;;;;;8DAEzB,8OAAC;oDACC,SAAS,IAAM,eAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,OAAO,GAAG;oDACzD,UAAU,gBAAgB;oDAC1B,WAAU;8DAEV,cAAA,8OAAC,sNAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAWzC,+BACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;0CAG1C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS,IAAM,iBAAiB;wCAChC,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,UAAU,qBAAqB,SAAS;wCACxC,WAAU;kDAET,qBAAqB,SAAS,GAAG,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASlE", "debugId": null}}]}