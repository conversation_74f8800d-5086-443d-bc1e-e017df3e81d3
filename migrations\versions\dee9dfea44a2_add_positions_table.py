"""Add positions table

Revision ID: dee9dfea44a2
Revises: 1b9af58a72d2
Create Date: 2025-08-07 20:09:45.941088

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'dee9dfea44a2'
down_revision = '1b9af58a72d2'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # When adding a NOT NULL column to an existing table with data,
    # add it as nullable first, backfill or set a server_default,
    # then alter to NOT NULL to avoid integrity errors.

    # 1) Add 'name' as nullable with a temporary default, then drop default and set NOT NULL
    op.add_column('positions', sa.Column('name', sa.String(length=100), nullable=True, server_default=''))
    # Drop temporary default so future inserts don't silently use ''
    op.alter_column('positions', 'name', server_default=None)
    # If your DB already has data and you want a specific backfill, do it here via op.execute(...)
    # Finally enforce NOT NULL
    op.alter_column('positions', 'name', existing_type=sa.String(length=100), nullable=False)

    # 2) Adjust unique constraints and indexes based on current model
    # Remove legacy index/constraint if they exist; these are safe even if absent in some DBs
    op.drop_index(op.f('ix_positions_is_active'), table_name='positions')
    op.drop_constraint(op.f('positions_code_key'), 'positions', type_='unique')

    # 3) Create uniqueness on 'name'
    # Provide a deterministic constraint name to satisfy type checkers and maintain clarity
    op.create_unique_constraint('uq_positions_name', 'positions', ['name'])

    # 4) Drop old FKs and columns that no longer exist in the model
    op.drop_constraint(op.f('positions_department_id_fkey'), 'positions', type_='foreignkey')
    op.drop_constraint(op.f('positions_branch_id_fkey'), 'positions', type_='foreignkey')
    op.drop_column('positions', 'branch_id')
    op.drop_column('positions', 'level')
    op.drop_column('positions', 'department_id')
    op.drop_column('positions', 'code')
    op.drop_column('positions', 'title')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('positions', sa.Column('title', sa.VARCHAR(length=100), autoincrement=False, nullable=False))
    op.add_column('positions', sa.Column('code', sa.VARCHAR(length=50), autoincrement=False, nullable=False))
    op.add_column('positions', sa.Column('department_id', sa.UUID(), autoincrement=False, nullable=False))
    op.add_column('positions', sa.Column('level', sa.INTEGER(), autoincrement=False, nullable=True))
    op.add_column('positions', sa.Column('branch_id', sa.UUID(), autoincrement=False, nullable=True))
    op.create_foreign_key(op.f('positions_branch_id_fkey'), 'positions', 'branches', ['branch_id'], ['id'])
    op.create_foreign_key(op.f('positions_department_id_fkey'), 'positions', 'departments', ['department_id'], ['id'])
    # Drop our deterministic unique constraint on name
    op.drop_constraint('uq_positions_name', 'positions', type_='unique')
    op.create_unique_constraint(op.f('positions_code_key'), 'positions', ['code'], postgresql_nulls_not_distinct=False)
    op.create_index(op.f('ix_positions_is_active'), 'positions', ['is_active'], unique=False)
    op.drop_column('positions', 'name')
    # ### end Alembic commands ###