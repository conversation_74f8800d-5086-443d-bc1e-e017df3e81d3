"""Create all tables with proper schema

Revision ID: 48f8d5284a84
Revises: 44a7ba5fc5a3
Create Date: 2025-08-01 11:18:58.222235

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '48f8d5284a84'
down_revision = '44a7ba5fc5a3'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('customer_applications', sa.Column('current_address', sa.Text(), nullable=True))
    op.add_column('customer_applications', sa.Column('province', sa.String(length=100), nullable=True))
    op.add_column('customer_applications', sa.Column('district', sa.String(length=100), nullable=True))
    op.add_column('customer_applications', sa.Column('commune', sa.String(length=100), nullable=True))
    op.add_column('customer_applications', sa.Column('village', sa.String(length=100), nullable=True))
    op.add_column('customer_applications', sa.Column('occupation', sa.String(length=100), nullable=True))
    op.add_column('customer_applications', sa.Column('employer_name', sa.String(length=255), nullable=True))
    op.add_column('customer_applications', sa.Column('monthly_income', sa.Numeric(precision=15, scale=2), nullable=True))
    op.add_column('customer_applications', sa.Column('income_source', sa.String(length=100), nullable=True))
    op.add_column('customer_applications', sa.Column('interest_rate', sa.Numeric(precision=5, scale=2), nullable=True))
    op.add_column('customer_applications', sa.Column('guarantor_id_number', sa.String(length=50), nullable=True))
    op.add_column('customer_applications', sa.Column('guarantor_address', sa.Text(), nullable=True))
    op.add_column('customer_applications', sa.Column('guarantor_relationship', sa.String(length=100), nullable=True))
    op.add_column('customer_applications', sa.Column('existing_loans', sa.JSON(), nullable=True))
    op.add_column('customer_applications', sa.Column('monthly_expenses', sa.Numeric(precision=15, scale=2), nullable=True))
    op.add_column('customer_applications', sa.Column('assets_value', sa.Numeric(precision=15, scale=2), nullable=True))
    op.add_column('customer_applications', sa.Column('credit_score', sa.Integer(), nullable=True))
    op.add_column('customer_applications', sa.Column('risk_category', sa.String(length=20), nullable=True))
    op.add_column('customer_applications', sa.Column('assessment_notes', sa.Text(), nullable=True))
    op.add_column('customer_applications', sa.Column('workflow_stage', sa.String(length=50), nullable=True))
    op.add_column('customer_applications', sa.Column('assigned_reviewer', sa.UUID(), nullable=True))
    op.add_column('customer_applications', sa.Column('priority_level', sa.String(length=20), nullable=True))
    op.create_foreign_key(None, 'customer_applications', 'users', ['assigned_reviewer'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'customer_applications', type_='foreignkey')
    op.drop_column('customer_applications', 'priority_level')
    op.drop_column('customer_applications', 'assigned_reviewer')
    op.drop_column('customer_applications', 'workflow_stage')
    op.drop_column('customer_applications', 'assessment_notes')
    op.drop_column('customer_applications', 'risk_category')
    op.drop_column('customer_applications', 'credit_score')
    op.drop_column('customer_applications', 'assets_value')
    op.drop_column('customer_applications', 'monthly_expenses')
    op.drop_column('customer_applications', 'existing_loans')
    op.drop_column('customer_applications', 'guarantor_relationship')
    op.drop_column('customer_applications', 'guarantor_address')
    op.drop_column('customer_applications', 'guarantor_id_number')
    op.drop_column('customer_applications', 'interest_rate')
    op.drop_column('customer_applications', 'income_source')
    op.drop_column('customer_applications', 'monthly_income')
    op.drop_column('customer_applications', 'employer_name')
    op.drop_column('customer_applications', 'occupation')
    op.drop_column('customer_applications', 'village')
    op.drop_column('customer_applications', 'commune')
    op.drop_column('customer_applications', 'district')
    op.drop_column('customer_applications', 'province')
    op.drop_column('customer_applications', 'current_address')
    # ### end Alembic commands ###