{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/backend/lc-workflow-frontend/src/hooks/useBranches.ts"], "sourcesContent": ["import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';\nimport { apiClient } from '@/lib/api';\nimport { Branch, PaginatedResponse } from '@/types/models';\nimport toast from 'react-hot-toast';\n\n// Branch query keys\nexport const branchKeys = {\n  all: ['branches'] as const,\n  lists: () => [...branchKeys.all, 'list'] as const,\n  list: (filters: any) => [...branchKeys.lists(), filters] as const,\n  details: () => [...branchKeys.all, 'detail'] as const,\n  detail: (id: string) => [...branchKeys.details(), id] as const,\n};\n\n// Branch hooks\nexport const useBranches = (filters: {\n  page?: number;\n  size?: number;\n  search?: string;\n  is_active?: boolean;\n} = {}) => {\n  return useQuery({\n    queryKey: branchKeys.list(filters),\n    queryFn: () => apiClient.get<PaginatedResponse<Branch>>('/branches', {\n      params: filters,\n    }),\n    staleTime: 5 * 60 * 1000, // 5 minutes\n  });\n};\n\nexport const useActiveBranches = () => {\n  return useQuery({\n    queryKey: branchKeys.list({ is_active: true }),\n    queryFn: () => apiClient.get<Branch[]>('/branches/active'),\n    staleTime: 5 * 60 * 1000,\n  });\n};\n\nexport const useBranch = (id: string) => {\n  return useQuery({\n    queryKey: branchKeys.detail(id),\n    queryFn: () => apiClient.get<Branch>(`/branches/${id}`),\n    staleTime: 5 * 60 * 1000,\n    enabled: !!id && id !== 'undefined' && /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/.test(id),\n  });\n};\n\nexport const useCreateBranch = () => {\n  const queryClient = useQueryClient();\n\n  return useMutation({\n    mutationFn: (data: {\n      name: string;\n      code: string;\n      address: string;\n      phone_number?: string;\n      email?: string;\n      manager_id?: string;\n      latitude?: number;\n      longitude?: number;\n    }) => apiClient.post<Branch>('/branches', data),\n    onSuccess: () => {\n      queryClient.invalidateQueries({ queryKey: branchKeys.lists() });\n      toast.success('Branch created successfully!');\n    },\n    onError: (error: any) => {\n      const message = error.response?.data?.detail || 'Failed to create branch';\n      toast.error(message);\n    },\n  });\n};\n\nexport const useUpdateBranch = (id: string) => {\n  const queryClient = useQueryClient();\n\n  return useMutation({\n    mutationFn: (data: {\n      name?: string;\n      code?: string;\n      address?: string;\n      phone_number?: string;\n      email?: string;\n      manager_id?: string;\n      latitude?: number;\n      longitude?: number;\n      is_active?: boolean;\n    }) => {\n      if (!id || id === 'undefined' || !/^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/.test(id)) {\n        throw new Error('Invalid branch ID format');\n      }\n      return apiClient.patch<Branch>(`/branches/${id}`, data);\n    },\n    onSuccess: (updatedBranch) => {\n      queryClient.setQueryData(branchKeys.detail(id), updatedBranch);\n      queryClient.invalidateQueries({ queryKey: branchKeys.lists() });\n      toast.success('Branch updated successfully!');\n    },\n    onError: (error: any) => {\n      const message = error.response?.data?.detail || 'Failed to update branch';\n      toast.error(message);\n    },\n  });\n};\n\nexport const useDeleteBranch = () => {\n  const queryClient = useQueryClient();\n\n  return useMutation({\n    mutationFn: (id: string) => {\n      if (!id || id === 'undefined' || !/^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/.test(id)) {\n        throw new Error('Invalid branch ID format');\n      }\n      return apiClient.delete(`/branches/${id}`);\n    },\n    onSuccess: () => {\n      queryClient.invalidateQueries({ queryKey: branchKeys.lists() });\n      toast.success('Branch deleted successfully!');\n    },\n    onError: (error: any) => {\n      const message = error.response?.data?.detail || 'Failed to delete branch';\n      toast.error(message);\n    },\n  });\n};"], "names": [], "mappings": ";;;;;;;;;AAAA;AAAA;AAAA;AACA;AAEA;;;;;AAGO,MAAM,aAAa;IACxB,KAAK;QAAC;KAAW;IACjB,OAAO,IAAM;eAAI,WAAW,GAAG;YAAE;SAAO;IACxC,MAAM,CAAC,UAAiB;eAAI,WAAW,KAAK;YAAI;SAAQ;IACxD,SAAS,IAAM;eAAI,WAAW,GAAG;YAAE;SAAS;IAC5C,QAAQ,CAAC,KAAe;eAAI,WAAW,OAAO;YAAI;SAAG;AACvD;AAGO,MAAM,cAAc;QAAC,2EAKxB,CAAC;;IACH,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,WAAW,IAAI,CAAC;QAC1B,OAAO;oCAAE,IAAM,oHAAA,CAAA,YAAS,CAAC,GAAG,CAA4B,aAAa;oBACnE,QAAQ;gBACV;;QACA,WAAW,IAAI,KAAK;IACtB;AACF;GAba;;QAMJ,8KAAA,CAAA,WAAQ;;;AASV,MAAM,oBAAoB;;IAC/B,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,WAAW,IAAI,CAAC;YAAE,WAAW;QAAK;QAC5C,OAAO;0CAAE,IAAM,oHAAA,CAAA,YAAS,CAAC,GAAG,CAAW;;QACvC,WAAW,IAAI,KAAK;IACtB;AACF;IANa;;QACJ,8KAAA,CAAA,WAAQ;;;AAOV,MAAM,YAAY,CAAC;;IACxB,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,WAAW,MAAM,CAAC;QAC5B,OAAO;kCAAE,IAAM,oHAAA,CAAA,YAAS,CAAC,GAAG,CAAS,AAAC,aAAe,OAAH;;QAClD,WAAW,IAAI,KAAK;QACpB,SAAS,CAAC,CAAC,MAAM,OAAO,eAAe,gFAAgF,IAAI,CAAC;IAC9H;AACF;IAPa;;QACJ,8KAAA,CAAA,WAAQ;;;AAQV,MAAM,kBAAkB;;IAC7B,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;2CAAE,CAAC,OASP,oHAAA,CAAA,YAAS,CAAC,IAAI,CAAS,aAAa;;QAC1C,SAAS;2CAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU,WAAW,KAAK;gBAAG;gBAC7D,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;YAChB;;QACA,OAAO;2CAAE,CAAC;oBACQ,sBAAA;gBAAhB,MAAM,UAAU,EAAA,kBAAA,MAAM,QAAQ,cAAd,uCAAA,uBAAA,gBAAgB,IAAI,cAApB,2CAAA,qBAAsB,MAAM,KAAI;gBAChD,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACd;;IACF;AACF;IAvBa;;QACS,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAsBb,MAAM,kBAAkB,CAAC;;IAC9B,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;2CAAE,CAAC;gBAWX,IAAI,CAAC,MAAM,OAAO,eAAe,CAAC,gFAAgF,IAAI,CAAC,KAAK;oBAC1H,MAAM,IAAI,MAAM;gBAClB;gBACA,OAAO,oHAAA,CAAA,YAAS,CAAC,KAAK,CAAS,AAAC,aAAe,OAAH,KAAM;YACpD;;QACA,SAAS;2CAAE,CAAC;gBACV,YAAY,YAAY,CAAC,WAAW,MAAM,CAAC,KAAK;gBAChD,YAAY,iBAAiB,CAAC;oBAAE,UAAU,WAAW,KAAK;gBAAG;gBAC7D,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;YAChB;;QACA,OAAO;2CAAE,CAAC;oBACQ,sBAAA;gBAAhB,MAAM,UAAU,EAAA,kBAAA,MAAM,QAAQ,cAAd,uCAAA,uBAAA,gBAAgB,IAAI,cAApB,2CAAA,qBAAsB,MAAM,KAAI;gBAChD,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACd;;IACF;AACF;IA9Ba;;QACS,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AA6Bb,MAAM,kBAAkB;;IAC7B,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;2CAAE,CAAC;gBACX,IAAI,CAAC,MAAM,OAAO,eAAe,CAAC,gFAAgF,IAAI,CAAC,KAAK;oBAC1H,MAAM,IAAI,MAAM;gBAClB;gBACA,OAAO,oHAAA,CAAA,YAAS,CAAC,MAAM,CAAC,AAAC,aAAe,OAAH;YACvC;;QACA,SAAS;2CAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU,WAAW,KAAK;gBAAG;gBAC7D,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;YAChB;;QACA,OAAO;2CAAE,CAAC;oBACQ,sBAAA;gBAAhB,MAAM,UAAU,EAAA,kBAAA,MAAM,QAAQ,cAAd,uCAAA,uBAAA,gBAAgB,IAAI,cAApB,2CAAA,qBAAsB,MAAM,KAAI;gBAChD,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACd;;IACF;AACF;IAnBa;;QACS,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW", "debugId": null}}, {"offset": {"line": 207, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/backend/lc-workflow-frontend/src/components/layout/Sidebar.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { \n  HomeIcon, \n  DocumentTextIcon, \n  UsersIcon, \n  BuildingOfficeIcon, \n  MapPinIcon, \n  Cog6ToothIcon,\n  FolderIcon,\n  ChevronDownIcon,\n  ChevronRightIcon,\n  XMarkIcon,\n  Bars3Icon,\n} from '@heroicons/react/24/outline';\nimport { useAuthContext } from '@/providers/AuthProvider';\n\ninterface NavItem {\n  name: string;\n  href: string;\n  icon: React.ComponentType<{ className?: string }>;\n  requiredRoles?: string[];\n}\n\nconst navigation: NavItem[] = [\n  { name: 'Dashboard', href: '/dashboard', icon: HomeIcon },\n  { name: 'Applications', href: '/applications', icon: DocumentTextIcon },\n  { name: 'Files', href: '/files', icon: FolderIcon },\n];\n\nconst adminNavigation: NavItem[] = [\n  { name: 'Users', href: '/users', icon: UsersIcon, requiredRoles: ['admin', 'manager'] },\n  { name: 'Departments', href: '/departments', icon: BuildingOfficeIcon, requiredRoles: ['admin', 'manager'] },\n  { name: 'Branches', href: '/branches', icon: MapPinIcon, requiredRoles: ['admin', 'manager'] },\n  { name: 'Settings', href: '/settings', icon: Cog6ToothIcon, requiredRoles: ['admin'] },\n];\n\ninterface SidebarProps {\n  isOpen: boolean;\n  onClose: () => void;\n}\n\nexport function Sidebar({ isOpen, onClose }: SidebarProps) {\n  const pathname = usePathname();\n  const { user, isAdmin, isManager } = useAuthContext();\n\n  const hasRequiredRole = (requiredRoles?: string[]) => {\n    if (!requiredRoles) return true;\n    return isAdmin || (isManager && requiredRoles.includes('manager'));\n  };\n\n  const allNavigation = [...navigation, ...adminNavigation];\n\n  const NavLink = ({ item }: { item: NavItem }) => {\n    const isActive = pathname === item.href;\n    \n    if (!hasRequiredRole(item.requiredRoles)) return null;\n\n    return (\n      <Link\n        href={item.href}\n        className={`group flex items-center gap-x-3 rounded-md p-2 text-sm font-semibold leading-6 transition-all duration-200 ${\n          isActive\n            ? 'bg-blue-600 text-white'\n            : 'text-gray-700 hover:bg-gray-50 hover:text-blue-600'\n        }`}\n        onClick={onClose}\n      >\n        <item.icon className=\"h-5 w-5 shrink-0\" />\n        {item.name}\n      </Link>\n    );\n  };\n\n  return (\n    <>\n      {/* Mobile sidebar */}\n      <div\n        className={`fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:hidden ${\n          isOpen ? 'translate-x-0' : '-translate-x-full'\n        }`}\n      >\n        <div className=\"flex h-16 items-center justify-between px-4\">\n          <div className=\"text-xl font-bold text-gray-900\">LC Workflow</div>\n          <button\n            type=\"button\"\n            className=\"-m-2.5 p-2.5 text-gray-700\"\n            onClick={onClose}\n          >\n            <XMarkIcon className=\"h-6 w-6\" />\n          </button>\n        </div>\n        <nav className=\"flex flex-1 flex-col gap-y-2 p-4\">\n          {allNavigation.map((item) => (\n            <NavLink key={item.name} item={item} />\n          ))}\n        </nav>\n      </div>\n\n      {/* Desktop sidebar */}\n      <div className=\"hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-64 lg:flex-col\">\n        <div className=\"flex grow flex-col gap-y-5 overflow-y-auto bg-white px-6 ring-1 ring-gray-200\">\n          <div className=\"flex h-16 shrink-0 items-center\">\n            <div className=\"text-xl font-bold text-gray-900\">LC Workflow</div>\n          </div>\n          <nav className=\"flex flex-1 flex-col gap-y-2\">\n            {allNavigation.map((item) => (\n              <NavLink key={item.name} item={item} />\n            ))}\n          </nav>\n          \n          {/* User info */}\n          {user && (\n            <div className=\"mb-4\">\n              <div className=\"flex items-center gap-x-3 rounded-md p-2 text-sm font-medium text-gray-700\">\n                <div className=\"h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center\">\n                  <span className=\"text-sm font-medium text-gray-600\">\n                    {user.first_name.charAt(0)}{user.last_name.charAt(0)}\n                  </span>\n                </div>\n                <div>\n                  <div className=\"font-medium\">{user.first_name} {user.last_name}</div>\n                  <div className=\"text-xs text-gray-500\">{user.role}</div>\n                </div>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </>\n  );\n}"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;;;AAlBA;;;;;AA2BA,MAAM,aAAwB;IAC5B;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,kNAAA,CAAA,WAAQ;IAAC;IACxD;QAAE,MAAM;QAAgB,MAAM;QAAiB,MAAM,kOAAA,CAAA,mBAAgB;IAAC;IACtE;QAAE,MAAM;QAAS,MAAM;QAAU,MAAM,sNAAA,CAAA,aAAU;IAAC;CACnD;AAED,MAAM,kBAA6B;IACjC;QAAE,MAAM;QAAS,MAAM;QAAU,MAAM,oNAAA,CAAA,YAAS;QAAE,eAAe;YAAC;YAAS;SAAU;IAAC;IACtF;QAAE,MAAM;QAAe,MAAM;QAAgB,MAAM,sOAAA,CAAA,qBAAkB;QAAE,eAAe;YAAC;YAAS;SAAU;IAAC;IAC3G;QAAE,MAAM;QAAY,MAAM;QAAa,MAAM,sNAAA,CAAA,aAAU;QAAE,eAAe;YAAC;YAAS;SAAU;IAAC;IAC7F;QAAE,MAAM;QAAY,MAAM;QAAa,MAAM,4NAAA,CAAA,gBAAa;QAAE,eAAe;YAAC;SAAQ;IAAC;CACtF;AAOM,SAAS,QAAQ,KAAiC;QAAjC,EAAE,MAAM,EAAE,OAAO,EAAgB,GAAjC;;IACtB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,iBAAc,AAAD;IAElD,MAAM,kBAAkB,CAAC;QACvB,IAAI,CAAC,eAAe,OAAO;QAC3B,OAAO,WAAY,aAAa,cAAc,QAAQ,CAAC;IACzD;IAEA,MAAM,gBAAgB;WAAI;WAAe;KAAgB;IAEzD,MAAM,UAAU;YAAC,EAAE,IAAI,EAAqB;QAC1C,MAAM,WAAW,aAAa,KAAK,IAAI;QAEvC,IAAI,CAAC,gBAAgB,KAAK,aAAa,GAAG,OAAO;QAEjD,qBACE,6LAAC,+JAAA,CAAA,UAAI;YACH,MAAM,KAAK,IAAI;YACf,WAAW,AAAC,8GAIX,OAHC,WACI,2BACA;YAEN,SAAS;;8BAET,6LAAC,KAAK,IAAI;oBAAC,WAAU;;;;;;gBACpB,KAAK,IAAI;;;;;;;IAGhB;IAEA,qBACE;;0BAEE,6LAAC;gBACC,WAAW,AAAC,yHAEX,OADC,SAAS,kBAAkB;;kCAG7B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAAkC;;;;;;0CACjD,6LAAC;gCACC,MAAK;gCACL,WAAU;gCACV,SAAS;0CAET,cAAA,6LAAC,oNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAGzB,6LAAC;wBAAI,WAAU;kCACZ,cAAc,GAAG,CAAC,CAAC,qBAClB,6LAAC;gCAAwB,MAAM;+BAAjB,KAAK,IAAI;;;;;;;;;;;;;;;;0BAM7B,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CAAkC;;;;;;;;;;;sCAEnD,6LAAC;4BAAI,WAAU;sCACZ,cAAc,GAAG,CAAC,CAAC,qBAClB,6LAAC;oCAAwB,MAAM;mCAAjB,KAAK,IAAI;;;;;;;;;;wBAK1B,sBACC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;;gDACb,KAAK,UAAU,CAAC,MAAM,CAAC;gDAAI,KAAK,SAAS,CAAC,MAAM,CAAC;;;;;;;;;;;;kDAGtD,6LAAC;;0DACC,6LAAC;gDAAI,WAAU;;oDAAe,KAAK,UAAU;oDAAC;oDAAE,KAAK,SAAS;;;;;;;0DAC9D,6LAAC;gDAAI,WAAU;0DAAyB,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASnE;GAzFgB;;QACG,qIAAA,CAAA,cAAW;QACS,oIAAA,CAAA,iBAAc;;;KAFrC", "debugId": null}}, {"offset": {"line": 506, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/backend/lc-workflow-frontend/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useRef, useEffect } from 'react';\r\nimport { Bars3Icon, BellIcon, UserCircleIcon, ArrowRightOnRectangleIcon, Cog6ToothIcon } from '@heroicons/react/24/outline';\r\nimport { useLogout } from '@/hooks/useAuth';\r\nimport { useAuthContext } from '@/providers/AuthProvider';\r\nimport Link from 'next/link';\r\n\r\ninterface HeaderProps {\r\n  onMenuClick: () => void;\r\n}\r\n\r\nexport function Header({ onMenuClick }: HeaderProps) {\r\n  const logout = useLogout();\r\n  const { user } = useAuthContext();\r\n  const [profileDropdownOpen, setProfileDropdownOpen] = useState(false);\r\n  const dropdownRef = useRef<HTMLDivElement>(null);\r\n\r\n  // Close dropdown when clicking outside\r\n  useEffect(() => {\r\n    function handleClickOutside(event: MouseEvent) {\r\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\r\n        setProfileDropdownOpen(false);\r\n      }\r\n    }\r\n\r\n    document.addEventListener('mousedown', handleClickOutside);\r\n    return () => {\r\n      document.removeEventListener('mousedown', handleClickOutside);\r\n    };\r\n  }, []);\r\n\r\n  return (\r\n    <div className=\"sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 bg-white px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8\">\r\n      <button\r\n        type=\"button\"\r\n        className=\"-m-2.5 p-2.5 text-gray-700 lg:hidden\"\r\n        onClick={onMenuClick}\r\n      >\r\n        <span className=\"sr-only\">Open sidebar</span>\r\n        <Bars3Icon className=\"h-6 w-6\" aria-hidden=\"true\" />\r\n      </button>\r\n\r\n      {/* Separator */}\r\n      <div className=\"h-6 w-px bg-gray-900/10 lg:hidden\" aria-hidden=\"true\" />\r\n\r\n      <div className=\"flex flex-1 gap-x-4 self-stretch lg:gap-x-6\">\r\n        <div className=\"flex flex-1\" />\r\n\r\n        <div className=\"flex items-center gap-x-4 lg:gap-x-6\">\r\n          <button\r\n            type=\"button\"\r\n            className=\"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500\"\r\n          >\r\n            <span className=\"sr-only\">View notifications</span>\r\n            <BellIcon className=\"h-6 w-6\" aria-hidden=\"true\" />\r\n          </button>\r\n\r\n          {/* Separator */}\r\n          <div className=\"hidden lg:block lg:h-6 lg:w-px lg:bg-gray-900/10\" aria-hidden=\"true\" />\r\n\r\n          {/* Profile dropdown */}\r\n          <div className=\"relative\">\r\n            <button\r\n              type=\"button\"\r\n              className=\"-m-1.5 flex items-center p-1.5\"\r\n              id=\"user-menu-button\"\r\n              aria-expanded=\"false\"\r\n              aria-haspopup=\"true\"\r\n              onClick={() => setProfileDropdownOpen(!profileDropdownOpen)}\r\n            >\r\n              <span className=\"sr-only\">Open user menu</span>\r\n              <div className=\"h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center\">\r\n                <span className=\"text-sm font-medium text-gray-600\">\r\n                  {user?.first_name?.charAt(0)}{user?.last_name?.charAt(0)}\r\n                </span>\r\n              </div>\r\n              <span className=\"hidden lg:flex lg:items-center\">\r\n                <span className=\"ml-4 text-sm leading-6 text-gray-900\" aria-hidden=\"true\">\r\n                  <span className=\"font-semibold\">{user?.first_name} {user?.last_name}</span>\r\n                  {user?.position?.title ? (\r\n                    <span className=\"ml-2 text-gray-500 truncate max-w-[10rem]\">\r\n                      • {user?.position?.title}\r\n                    </span>\r\n                  ) : null}\r\n                </span>\r\n              </span>\r\n            </button>\r\n\r\n            {/* Dropdown menu */}\r\n            {profileDropdownOpen && (\r\n              <div\r\n                className=\"absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none\"\r\n                role=\"menu\"\r\n                aria-orientation=\"vertical\"\r\n                aria-labelledby=\"user-menu-button\"\r\n                ref={dropdownRef}\r\n              >\r\n                <div className=\"px-4 py-3\">\r\n                  <p className=\"text-sm\">Signed in as</p>\r\n                  <p className=\"truncate text-sm font-medium text-gray-900\">{user?.email}</p>\r\n                </div>\r\n                <div className=\"border-t border-gray-100\"></div>\r\n                <Link\r\n                  href=\"/profile\"\r\n                  className=\"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\r\n                  role=\"menuitem\"\r\n                  onClick={() => setProfileDropdownOpen(false)}\r\n                >\r\n                  <UserCircleIcon className=\"mr-3 h-5 w-5 text-gray-400\" aria-hidden=\"true\" />\r\n                  Your Profile\r\n                </Link>\r\n                <Link\r\n                  href=\"/settings\"\r\n                  className=\"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\r\n                  role=\"menuitem\"\r\n                  onClick={() => setProfileDropdownOpen(false)}\r\n                >\r\n                  <Cog6ToothIcon className=\"mr-3 h-5 w-5 text-gray-400\" aria-hidden=\"true\" />\r\n                  Settings\r\n                </Link>\r\n                <div className=\"border-t border-gray-100\"></div>\r\n                <button\r\n                  type=\"button\"\r\n                  className=\"flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\r\n                  role=\"menuitem\"\r\n                  onClick={() => {\r\n                    setProfileDropdownOpen(false);\r\n                    logout.mutate();\r\n                  }}\r\n                >\r\n                  <ArrowRightOnRectangleIcon className=\"mr-3 h-5 w-5 text-gray-400\" aria-hidden=\"true\" />\r\n                  Sign out\r\n                </button>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;AANA;;;;;;AAYO,SAAS,OAAO,KAA4B;QAA5B,EAAE,WAAW,EAAe,GAA5B;QA8DJ,kBAA6B,iBAM7B,gBAEM;;IArEvB,MAAM,SAAS,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,iBAAc,AAAD;IAC9B,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAE3C,uCAAuC;IACvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,SAAS,mBAAmB,KAAiB;gBAC3C,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;oBAC9E,uBAAuB;gBACzB;YACF;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC;oCAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;gBAC5C;;QACF;2BAAG,EAAE;IAEL,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACC,MAAK;gBACL,WAAU;gBACV,SAAS;;kCAET,6LAAC;wBAAK,WAAU;kCAAU;;;;;;kCAC1B,6LAAC,oNAAA,CAAA,YAAS;wBAAC,WAAU;wBAAU,eAAY;;;;;;;;;;;;0BAI7C,6LAAC;gBAAI,WAAU;gBAAoC,eAAY;;;;;;0BAE/D,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCAEf,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,MAAK;gCACL,WAAU;;kDAEV,6LAAC;wCAAK,WAAU;kDAAU;;;;;;kDAC1B,6LAAC,kNAAA,CAAA,WAAQ;wCAAC,WAAU;wCAAU,eAAY;;;;;;;;;;;;0CAI5C,6LAAC;gCAAI,WAAU;gCAAmD,eAAY;;;;;;0CAG9E,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,WAAU;wCACV,IAAG;wCACH,iBAAc;wCACd,iBAAc;wCACd,SAAS,IAAM,uBAAuB,CAAC;;0DAEvC,6LAAC;gDAAK,WAAU;0DAAU;;;;;;0DAC1B,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;;wDACb,iBAAA,4BAAA,mBAAA,KAAM,UAAU,cAAhB,uCAAA,iBAAkB,MAAM,CAAC;wDAAI,iBAAA,4BAAA,kBAAA,KAAM,SAAS,cAAf,sCAAA,gBAAiB,MAAM,CAAC;;;;;;;;;;;;0DAG1D,6LAAC;gDAAK,WAAU;0DACd,cAAA,6LAAC;oDAAK,WAAU;oDAAuC,eAAY;;sEACjE,6LAAC;4DAAK,WAAU;;gEAAiB,iBAAA,2BAAA,KAAM,UAAU;gEAAC;gEAAE,iBAAA,2BAAA,KAAM,SAAS;;;;;;;wDAClE,CAAA,iBAAA,4BAAA,iBAAA,KAAM,QAAQ,cAAd,qCAAA,eAAgB,KAAK,kBACpB,6LAAC;4DAAK,WAAU;;gEAA4C;gEACvD,iBAAA,4BAAA,kBAAA,KAAM,QAAQ,cAAd,sCAAA,gBAAgB,KAAK;;;;;;mEAExB;;;;;;;;;;;;;;;;;;oCAMT,qCACC,6LAAC;wCACC,WAAU;wCACV,MAAK;wCACL,oBAAiB;wCACjB,mBAAgB;wCAChB,KAAK;;0DAEL,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAU;;;;;;kEACvB,6LAAC;wDAAE,WAAU;kEAA8C,iBAAA,2BAAA,KAAM,KAAK;;;;;;;;;;;;0DAExE,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,MAAK;gDACL,SAAS,IAAM,uBAAuB;;kEAEtC,6LAAC,8NAAA,CAAA,iBAAc;wDAAC,WAAU;wDAA6B,eAAY;;;;;;oDAAS;;;;;;;0DAG9E,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,MAAK;gDACL,SAAS,IAAM,uBAAuB;;kEAEtC,6LAAC,4NAAA,CAAA,gBAAa;wDAAC,WAAU;wDAA6B,eAAY;;;;;;oDAAS;;;;;;;0DAG7E,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDACC,MAAK;gDACL,WAAU;gDACV,MAAK;gDACL,SAAS;oDACP,uBAAuB;oDACvB,OAAO,MAAM;gDACf;;kEAEA,6LAAC,oPAAA,CAAA,4BAAyB;wDAAC,WAAU;wDAA6B,eAAY;;;;;;oDAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUzG;GAjIgB;;QACC,0HAAA,CAAA,YAAS;QACP,oIAAA,CAAA,iBAAc;;;KAFjB", "debugId": null}}, {"offset": {"line": 880, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/backend/lc-workflow-frontend/src/components/layout/Layout.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Sidebar } from './Sidebar';\nimport { Header } from './Header';\n\ninterface LayoutProps {\n  children: React.ReactNode;\n}\n\nexport function Layout({ children }: LayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n\n  return (\n    <>\n      <div className=\"min-h-full\">\n        <Sidebar \n          isOpen={sidebarOpen} \n          onClose={() => setSidebarOpen(false)} \n        />\n        \n        <div className=\"lg:pl-64\">\n          <Header onMenuClick={() => setSidebarOpen(true)} />\n          \n          <main className=\"py-10\">\n            <div className=\"px-4 sm:px-6 lg:px-8\">\n              {children}\n            </div>\n          </main>\n        </div>\n      </div>\n    </>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAUO,SAAS,OAAO,KAAyB;QAAzB,EAAE,QAAQ,EAAe,GAAzB;;IACrB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,qBACE;kBACE,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,0IAAA,CAAA,UAAO;oBACN,QAAQ;oBACR,SAAS,IAAM,eAAe;;;;;;8BAGhC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,yIAAA,CAAA,SAAM;4BAAC,aAAa,IAAM,eAAe;;;;;;sCAE1C,6LAAC;4BAAK,WAAU;sCACd,cAAA,6LAAC;gCAAI,WAAU;0CACZ;;;;;;;;;;;;;;;;;;;;;;;;AAOf;GAvBgB;KAAA", "debugId": null}}, {"offset": {"line": 961, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/backend/lc-workflow-frontend/src/components/auth/ProtectedRoute.tsx"], "sourcesContent": ["'use client';\n\nimport { useRouter } from 'next/navigation';\nimport { useAuthContext } from '@/providers/AuthProvider';\nimport { useEffect } from 'react';\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode;\n  requiredRoles?: string[];\n}\n\nexport function ProtectedRoute({ children, requiredRoles }: ProtectedRouteProps) {\n  const router = useRouter();\n  const { isAuthenticated, isLoading, user, role } = useAuthContext();\n\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      if (!isLoading) {\n        if (!isAuthenticated) {\n          router.push('/login');\n        } else if (requiredRoles && requiredRoles.length > 0 && !requiredRoles.includes(role || '')) {\n          router.push('/unauthorized');\n        }\n      }\n    }, 1000); // Wait 1 second for auth state to stabilize\n\n    return () => clearTimeout(timer);\n  }, [isAuthenticated, isLoading, requiredRoles, role, router]);\n\n  if (isLoading || !isAuthenticated) {\n    return (\n      <div className=\"flex min-h-screen items-center justify-center\">\n        <div className=\"h-8 w-8 animate-spin rounded-full border-4 border-blue-600 border-t-transparent\"></div>\n      </div>\n    );\n  }\n\n  if (requiredRoles && requiredRoles.length > 0 && !requiredRoles.includes(role || '')) {\n    return null;\n  }\n\n  return <>{children}</>;\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAWO,SAAS,eAAe,KAAgD;QAAhD,EAAE,QAAQ,EAAE,aAAa,EAAuB,GAAhD;;IAC7B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,iBAAc,AAAD;IAEhE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM,QAAQ;kDAAW;oBACvB,IAAI,CAAC,WAAW;wBACd,IAAI,CAAC,iBAAiB;4BACpB,OAAO,IAAI,CAAC;wBACd,OAAO,IAAI,iBAAiB,cAAc,MAAM,GAAG,KAAK,CAAC,cAAc,QAAQ,CAAC,QAAQ,KAAK;4BAC3F,OAAO,IAAI,CAAC;wBACd;oBACF;gBACF;iDAAG,OAAO,4CAA4C;YAEtD;4CAAO,IAAM,aAAa;;QAC5B;mCAAG;QAAC;QAAiB;QAAW;QAAe;QAAM;KAAO;IAE5D,IAAI,aAAa,CAAC,iBAAiB;QACjC,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,iBAAiB,cAAc,MAAM,GAAG,KAAK,CAAC,cAAc,QAAQ,CAAC,QAAQ,KAAK;QACpF,OAAO;IACT;IAEA,qBAAO;kBAAG;;AACZ;GA/BgB;;QACC,qIAAA,CAAA,YAAS;QAC2B,oIAAA,CAAA,iBAAc;;;KAFnD", "debugId": null}}, {"offset": {"line": 1044, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/backend/lc-workflow-frontend/src/app/branches/new/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useCreateBranch } from '@/hooks/useBranches';\nimport { ArrowLeft, MapPin } from 'lucide-react';\nimport Link from 'next/link';\nimport { Layout } from '@/components/layout/Layout';\nimport { ProtectedRoute } from '@/components/auth/ProtectedRoute';\n\nexport default function NewBranchPage() {\n  const router = useRouter();\n  const [formData, setFormData] = useState({\n    name: '',\n    code: '',\n    address: '',\n    phone: '',\n  });\n  const [errors, setErrors] = useState<Record<string, string>>({});\n\n  const createBranchMutation = useCreateBranch();\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({ ...prev, [name]: value }));\n    if (errors[name]) {\n      setErrors(prev => ({ ...prev, [name]: '' }));\n    }\n  };\n\n  const validateForm = () => {\n    const newErrors: Record<string, string> = {};\n\n    if (!formData.name.trim()) {\n      newErrors.name = 'Branch name is required';\n    }\n\n    if (!formData.code.trim()) {\n      newErrors.code = 'Branch code is required';\n    } else if (formData.code.length < 2) {\n      newErrors.code = 'Branch code must be at least 2 characters';\n    } else if (!/^[A-Z0-9_-]+$/i.test(formData.code)) {\n      newErrors.code = 'Branch code can only contain letters, numbers, hyphens, and underscores';\n    }\n\n    if (!formData.address.trim()) {\n      newErrors.address = 'Branch address is required';\n    }\n\n\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!validateForm()) {\n      return;\n    }\n\n    try {\n      await createBranchMutation.mutateAsync({\n        name: formData.name.trim(),\n        code: formData.code.trim(),\n        address: formData.address.trim(),\n        phone_number: formData.phone.trim() || undefined,\n      });\n      router.push('/branches');\n    } catch (error: any) {\n      if (error.response?.data?.detail) {\n        if (typeof error.response.data.detail === 'string') {\n          setErrors({ general: error.response.data.detail });\n        } else if (Array.isArray(error.response.data.detail)) {\n          const newErrors: Record<string, string> = {};\n          error.response.data.detail.forEach((err: any) => {\n            if (err.loc && err.loc.length > 1) {\n              newErrors[err.loc[1]] = err.msg;\n            }\n          });\n          setErrors(newErrors);\n        }\n      } else {\n        setErrors({ general: 'Failed to create branch. Please try again.' });\n      }\n    }\n  };\n\n  return (\n    <ProtectedRoute>\n      <Layout>\n        <div className=\"max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Header */}\n        <div className=\"mb-8\">\n          <Link\n            href=\"/branches\"\n            className=\"inline-flex items-center text-gray-600 hover:text-gray-900 mb-4\"\n          >\n            <ArrowLeft className=\"h-4 w-4 mr-1\" />\n            Back to Branches\n          </Link>\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"h-10 w-10 bg-blue-100 rounded-lg flex items-center justify-center\">\n              <MapPin className=\"h-5 w-5 text-blue-600\" />\n            </div>\n            <div>\n              <h1 className=\"text-2xl font-bold text-gray-900\">Create New Branch</h1>\n              <p className=\"text-gray-600\">Add a new branch location to the system</p>\n            </div>\n          </div>\n        </div>\n\n        {/* Form */}\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200\">\n          <form onSubmit={handleSubmit} className=\"p-6 space-y-6\">\n            {errors.general && (\n              <div className=\"bg-red-50 border border-red-200 rounded-lg p-4\">\n                <p className=\"text-sm text-red-600\">{errors.general}</p>\n              </div>\n            )}\n\n            {/* Branch Name and Code */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div>\n                <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Branch Name *\n                </label>\n                <input\n                  type=\"text\"\n                  id=\"name\"\n                  name=\"name\"\n                  value={formData.name}\n                  onChange={handleInputChange}\n                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${\n                    errors.name ? 'border-red-300' : 'border-gray-300'\n                  }`}\n                  placeholder=\"Enter branch name\"\n                />\n                {errors.name && <p className=\"mt-1 text-sm text-red-600\">{errors.name}</p>}\n              </div>\n\n              <div>\n                <label htmlFor=\"code\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Branch Code *\n                </label>\n                <input\n                  type=\"text\"\n                  id=\"code\"\n                  name=\"code\"\n                  value={formData.code}\n                  onChange={(e) => {\n                    const { name, value } = e.target;\n                    setFormData(prev => ({ ...prev, [name]: value.toUpperCase() }));\n                    if (errors[name]) {\n                      setErrors(prev => ({ ...prev, [name]: '' }));\n                    }\n                  }}\n                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${\n                    errors.code ? 'border-red-300' : 'border-gray-300'\n                  }`}\n                  placeholder=\"Enter branch code (e.g., NYC, LA, CHI)\"\n                  maxLength={20}\n                />\n                {errors.code && <p className=\"mt-1 text-sm text-red-600\">{errors.code}</p>}\n                <p className=\"mt-1 text-sm text-gray-500\">\n                  Short code for the branch (letters, numbers, hyphens, underscores only)\n                </p>\n              </div>\n            </div>\n\n\n\n            {/* Address */}\n            <div>\n              <label htmlFor=\"address\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Address *\n              </label>\n              <textarea\n                id=\"address\"\n                name=\"address\"\n                value={formData.address}\n                onChange={handleInputChange}\n                rows={3}\n                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${\n                  errors.address ? 'border-red-300' : 'border-gray-300'\n                }`}\n                placeholder=\"Enter branch address\"\n              />\n              {errors.address && <p className=\"mt-1 text-sm text-red-600\">{errors.address}</p>}\n            </div>\n\n            {/* Phone */}\n            <div>\n              <label htmlFor=\"phone\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Phone Number\n              </label>\n              <input\n                type=\"tel\"\n                id=\"phone\"\n                name=\"phone\"\n                value={formData.phone}\n                onChange={handleInputChange}\n                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${\n                  errors.phone ? 'border-red-300' : 'border-gray-300'\n                }`}\n                placeholder=\"Enter phone number\"\n              />\n              {errors.phone && <p className=\"mt-1 text-sm text-red-600\">{errors.phone}</p>}\n            </div>\n\n            {/* Form Actions */}\n            <div className=\"flex justify-end space-x-3 pt-6 border-t border-gray-200\">\n              <Link\n                href=\"/branches\"\n                className=\"px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors\"\n              >\n                Cancel\n              </Link>\n              <button\n                type=\"submit\"\n                disabled={createBranchMutation.isPending}\n                className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed\"\n              >\n                {createBranchMutation.isPending ? 'Creating...' : 'Create Branch'}\n              </button>\n            </div>\n          </form>\n        </div>\n\n        {/* Help Text */}\n        <div className=\"mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4\">\n          <h3 className=\"text-sm font-medium text-blue-900 mb-2\">Branch Creation Guidelines</h3>\n          <ul className=\"text-sm text-blue-700 space-y-1\">\n            <li>• Branch name and code must be unique across the system</li>\n            <li>• Address is required for proper branch identification</li>\n            <li>• Phone number is optional but recommended</li>\n            <li>• All fields can be updated later if needed</li>\n          </ul>\n        </div>\n      </div>\n    </Layout>\n  </ProtectedRoute>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;;;AARA;;;;;;;;AAUe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,MAAM;QACN,SAAS;QACT,OAAO;IACT;IACA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAE9D,MAAM,uBAAuB,CAAA,GAAA,8HAAA,CAAA,kBAAe,AAAD;IAE3C,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,KAAK,EAAE;YAAM,CAAC;QAC/C,IAAI,MAAM,CAAC,KAAK,EAAE;YAChB,UAAU,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,KAAK,EAAE;gBAAG,CAAC;QAC5C;IACF;IAEA,MAAM,eAAe;QACnB,MAAM,YAAoC,CAAC;QAE3C,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI;YACzB,UAAU,IAAI,GAAG;QACnB;QAEA,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI;YACzB,UAAU,IAAI,GAAG;QACnB,OAAO,IAAI,SAAS,IAAI,CAAC,MAAM,GAAG,GAAG;YACnC,UAAU,IAAI,GAAG;QACnB,OAAO,IAAI,CAAC,iBAAiB,IAAI,CAAC,SAAS,IAAI,GAAG;YAChD,UAAU,IAAI,GAAG;QACnB;QAEA,IAAI,CAAC,SAAS,OAAO,CAAC,IAAI,IAAI;YAC5B,UAAU,OAAO,GAAG;QACtB;QAIA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;YACnB;QACF;QAEA,IAAI;YACF,MAAM,qBAAqB,WAAW,CAAC;gBACrC,MAAM,SAAS,IAAI,CAAC,IAAI;gBACxB,MAAM,SAAS,IAAI,CAAC,IAAI;gBACxB,SAAS,SAAS,OAAO,CAAC,IAAI;gBAC9B,cAAc,SAAS,KAAK,CAAC,IAAI,MAAM;YACzC;YACA,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAY;gBACf,sBAAA;YAAJ,KAAI,kBAAA,MAAM,QAAQ,cAAd,uCAAA,uBAAA,gBAAgB,IAAI,cAApB,2CAAA,qBAAsB,MAAM,EAAE;gBAChC,IAAI,OAAO,MAAM,QAAQ,CAAC,IAAI,CAAC,MAAM,KAAK,UAAU;oBAClD,UAAU;wBAAE,SAAS,MAAM,QAAQ,CAAC,IAAI,CAAC,MAAM;oBAAC;gBAClD,OAAO,IAAI,MAAM,OAAO,CAAC,MAAM,QAAQ,CAAC,IAAI,CAAC,MAAM,GAAG;oBACpD,MAAM,YAAoC,CAAC;oBAC3C,MAAM,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;wBAClC,IAAI,IAAI,GAAG,IAAI,IAAI,GAAG,CAAC,MAAM,GAAG,GAAG;4BACjC,SAAS,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,GAAG;wBACjC;oBACF;oBACA,UAAU;gBACZ;YACF,OAAO;gBACL,UAAU;oBAAE,SAAS;gBAA6C;YACpE;QACF;IACF;IAEA,qBACE,6LAAC,+IAAA,CAAA,iBAAc;kBACb,cAAA,6LAAC,yIAAA,CAAA,SAAM;sBACL,cAAA,6LAAC;gBAAI,WAAU;;kCAEf,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGxC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,6MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;kDAEpB,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAmC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;;;;;;;;;;;;;kCAMnC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,UAAU;4BAAc,WAAU;;gCACrC,OAAO,OAAO,kBACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAE,WAAU;kDAAwB,OAAO,OAAO;;;;;;;;;;;8CAKvD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAM,SAAQ;oDAAO,WAAU;8DAA+C;;;;;;8DAG/E,6LAAC;oDACC,MAAK;oDACL,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,IAAI;oDACpB,UAAU;oDACV,WAAW,AAAC,gGAEX,OADC,OAAO,IAAI,GAAG,mBAAmB;oDAEnC,aAAY;;;;;;gDAEb,OAAO,IAAI,kBAAI,6LAAC;oDAAE,WAAU;8DAA6B,OAAO,IAAI;;;;;;;;;;;;sDAGvE,6LAAC;;8DACC,6LAAC;oDAAM,SAAQ;oDAAO,WAAU;8DAA+C;;;;;;8DAG/E,6LAAC;oDACC,MAAK;oDACL,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,IAAI;oDACpB,UAAU,CAAC;wDACT,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;wDAChC,YAAY,CAAA,OAAQ,CAAC;gEAAE,GAAG,IAAI;gEAAE,CAAC,KAAK,EAAE,MAAM,WAAW;4DAAG,CAAC;wDAC7D,IAAI,MAAM,CAAC,KAAK,EAAE;4DAChB,UAAU,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,CAAC,KAAK,EAAE;gEAAG,CAAC;wDAC5C;oDACF;oDACA,WAAW,AAAC,gGAEX,OADC,OAAO,IAAI,GAAG,mBAAmB;oDAEnC,aAAY;oDACZ,WAAW;;;;;;gDAEZ,OAAO,IAAI,kBAAI,6LAAC;oDAAE,WAAU;8DAA6B,OAAO,IAAI;;;;;;8DACrE,6LAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;;;;;;;8CAS9C,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAU,WAAU;sDAA+C;;;;;;sDAGlF,6LAAC;4CACC,IAAG;4CACH,MAAK;4CACL,OAAO,SAAS,OAAO;4CACvB,UAAU;4CACV,MAAM;4CACN,WAAW,AAAC,gGAEX,OADC,OAAO,OAAO,GAAG,mBAAmB;4CAEtC,aAAY;;;;;;wCAEb,OAAO,OAAO,kBAAI,6LAAC;4CAAE,WAAU;sDAA6B,OAAO,OAAO;;;;;;;;;;;;8CAI7E,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAQ,WAAU;sDAA+C;;;;;;sDAGhF,6LAAC;4CACC,MAAK;4CACL,IAAG;4CACH,MAAK;4CACL,OAAO,SAAS,KAAK;4CACrB,UAAU;4CACV,WAAW,AAAC,gGAEX,OADC,OAAO,KAAK,GAAG,mBAAmB;4CAEpC,aAAY;;;;;;wCAEb,OAAO,KAAK,kBAAI,6LAAC;4CAAE,WAAU;sDAA6B,OAAO,KAAK;;;;;;;;;;;;8CAIzE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,6LAAC;4CACC,MAAK;4CACL,UAAU,qBAAqB,SAAS;4CACxC,WAAU;sDAET,qBAAqB,SAAS,GAAG,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;kCAO1D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC;kDAAG;;;;;;kDACJ,6LAAC;kDAAG;;;;;;kDACJ,6LAAC;kDAAG;;;;;;kDACJ,6LAAC;kDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOhB;GA1OwB;;QACP,qIAAA,CAAA,YAAS;QASK,8HAAA,CAAA,kBAAe;;;KAVtB", "debugId": null}}]}