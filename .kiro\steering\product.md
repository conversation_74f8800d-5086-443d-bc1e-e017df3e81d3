# Product Overview

LC Workflow is a loan application management system designed for financial institutions. The system provides a complete solution for managing customer loan applications with role-based access control and offline-first mobile support.

## Core Features

- **Customer Loan Applications**: Full lifecycle management from draft to approval/rejection
- **User Management**: Role-based access (admin, manager, officer, viewer) with department/branch hierarchy
- **File Management**: Document upload, storage, and retrieval for loan applications
- **Authentication**: JWT-based authentication with refresh tokens
- **Offline Support**: Designed for offline-first mobile applications with sync capabilities

## Business Domain

The system manages:
- Customer loan applications with borrower information, loan details, and guarantor data
- Organizational structure with departments and branches
- Document management for application supporting files
- User roles and permissions for different access levels
- Application workflow states (draft, submitted, approved, rejected)

## Target Users

- **Loan Officers**: Create and manage customer applications
- **Managers**: Oversee applications and manage users within their scope
- **Admins**: Full system access and user management
- **Viewers**: Read-only access for reporting and monitoring