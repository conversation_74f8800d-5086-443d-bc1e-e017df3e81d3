{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/backend/lc-workflow-frontend/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\nexport function formatBytes(bytes: number, decimals = 2): string {\n  if (bytes === 0) return '0 Bytes';\n\n  const k = 1024;\n  const dm = decimals < 0 ? 0 : decimals;\n  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];\n\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];\n}\n\nexport function formatDate(dateString: string): string {\n  const date = new Date(dateString);\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  }).format(date);\n}\n\nexport function formatCurrency(amount: number, currency = 'USD'): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency,\n  }).format(amount);\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text;\n  return text.slice(0, maxLength) + '...';\n}\n\nexport function getFileIcon(mimeType: string): string {\n  if (mimeType.startsWith('image/')) return '🖼️';\n  if (mimeType === 'application/pdf') return '📄';\n  if (mimeType.includes('word')) return '📝';\n  if (mimeType.includes('excel') || mimeType.includes('spreadsheet')) return '📊';\n  if (mimeType.startsWith('text/')) return '📄';\n  return '📎';\n}\n\nexport function validateEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\nexport function validatePhone(phone: string): boolean {\n  const phoneRegex = /^\\+?[\\d\\s\\-\\(\\)]+$/;\n  return phoneRegex.test(phone) && phone.replace(/\\D/g, '').length >= 10;\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9);\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean;\n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args);\n      inThrottle = true;\n      setTimeout(() => (inThrottle = false), limit);\n    }\n  };\n}\n\nexport function isValidUUID(uuid: string): boolean {\n  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;\n  return uuidRegex.test(uuid);\n}\n\nexport function validateUUID(uuid: string): string {\n  if (!isValidUUID(uuid)) {\n    throw new Error('Invalid UUID format');\n  }\n  return uuid;\n}\n\nexport function sanitizeUUID(uuid: string): string {\n  return uuid.replace(/[^a-f0-9-]/gi, '');\n}"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,YAAY,KAAa;QAAE,WAAA,iEAAW;IACpD,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,KAAK,WAAW,IAAI,IAAI;IAC9B,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;KAAK;IAEvE,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,OAAO,MAAM,KAAK,CAAC,EAAE;AAC1E;AAEO,SAAS,WAAW,UAAkB;IAC3C,MAAM,OAAO,IAAI,KAAK;IACtB,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,eAAe,MAAc;QAAE,WAAA,iEAAW;IACxD,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP;IACF,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,aAAa;AACpC;AAEO,SAAS,YAAY,QAAgB;IAC1C,IAAI,SAAS,UAAU,CAAC,WAAW,OAAO;IAC1C,IAAI,aAAa,mBAAmB,OAAO;IAC3C,IAAI,SAAS,QAAQ,CAAC,SAAS,OAAO;IACtC,IAAI,SAAS,QAAQ,CAAC,YAAY,SAAS,QAAQ,CAAC,gBAAgB,OAAO;IAC3E,IAAI,SAAS,UAAU,CAAC,UAAU,OAAO;IACzC,OAAO;AACT;AAEO,SAAS,cAAc,KAAa;IACzC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,cAAc,KAAa;IACzC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC,UAAU,MAAM,OAAO,CAAC,OAAO,IAAI,MAAM,IAAI;AACtE;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO;yCAAI;YAAA;;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,SACd,IAAO,EACP,KAAa;IAEb,IAAI;IACJ,OAAO;yCAAI;YAAA;;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW,IAAO,aAAa,OAAQ;QACzC;IACF;AACF;AAEO,SAAS,YAAY,IAAY;IACtC,MAAM,YAAY;IAClB,OAAO,UAAU,IAAI,CAAC;AACxB;AAEO,SAAS,aAAa,IAAY;IACvC,IAAI,CAAC,YAAY,OAAO;QACtB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAEO,SAAS,aAAa,IAAY;IACvC,OAAO,KAAK,OAAO,CAAC,gBAAgB;AACtC", "debugId": null}}, {"offset": {"line": 137, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/backend/lc-workflow-frontend/src/hooks/useUsers.ts"], "sourcesContent": ["import { useMutation, useQuery, useQueryClient, useInfiniteQuery } from '@tanstack/react-query';\r\nimport { apiClient } from '@/lib/api';\r\nimport { User, UserCreate, UserUpdate, PaginatedResponse } from '@/types/models';\r\nimport toast from 'react-hot-toast';\r\nimport { isValidUUID, validateUUID } from '@/lib/utils';\r\n\r\n// User query keys\r\nexport const userKeys = {\r\n  all: ['users'] as const,\r\n  lists: () => [...userKeys.all, 'list'] as const,\r\n  list: (filters: any) => [...userKeys.lists(), filters] as const,\r\n  details: () => [...userKeys.all, 'detail'] as const,\r\n  detail: (id: string) => [...userKeys.details(), id] as const,\r\n};\r\n\r\n// User hooks\r\nexport const useUsers = (filters: {\r\n  page?: number;\r\n  size?: number;\r\n  role?: string;\r\n  department_id?: string;\r\n  branch_id?: string;\r\n  search?: string;\r\n  status?: string;\r\n} = {}) => {\r\n  return useQuery({\r\n    queryKey: userKeys.list(filters),\r\n    queryFn: () => apiClient.get<PaginatedResponse<User>>('/users', {\r\n      params: filters,\r\n    }),\r\n    staleTime: 60 * 1000, // 1 minute\r\n  });\r\n};\r\n\r\nexport const useInfiniteUsers = (filters: {\r\n  size?: number;\r\n  role?: string;\r\n  department_id?: string;\r\n  branch_id?: string;\r\n  search?: string;\r\n  status?: string;\r\n} = {}) => {\r\n  return useInfiniteQuery({\r\n    queryKey: userKeys.list(filters),\r\n    queryFn: ({ pageParam = 1 }) => \r\n      apiClient.get<PaginatedResponse<User>>('/users', {\r\n        params: { ...filters, page: pageParam },\r\n      }),\r\n    getNextPageParam: (lastPage) => {\r\n      if (lastPage.page < lastPage.pages) {\r\n        return lastPage.page + 1;\r\n      }\r\n      return undefined;\r\n    },\r\n    initialPageParam: 1,\r\n    staleTime: 60 * 1000,\r\n  });\r\n};\r\n\r\nexport const useUser = (id: string) => {\r\n  return useQuery({\r\n    queryKey: userKeys.detail(id),\r\n    queryFn: () => apiClient.get<User>(`/users/${id}`),\r\n    staleTime: 5 * 60 * 1000, // 5 minutes\r\n    enabled: !!id && isValidUUID(id),\r\n  });\r\n};\r\n\r\nexport const useCreateUser = () => {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (data: UserCreate) => apiClient.post<User>('/users', data),\r\n    onSuccess: (newUser) => {\r\n      queryClient.invalidateQueries({ queryKey: userKeys.lists() });\r\n      toast.success('User created successfully!');\r\n    },\r\n    onError: (error: any) => {\r\n      const message = error.response?.data?.detail || 'Failed to create user';\r\n      toast.error(message);\r\n    },\r\n  });\r\n};\r\n\r\nexport const useUpdateUser = (id: string) => {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (data: UserUpdate) => {\r\n      validateUUID(id, 'User');\r\n      return apiClient.patch<User>(`/users/${id}`, data);\r\n    },\r\n    onSuccess: (updatedUser) => {\r\n      queryClient.setQueryData(userKeys.detail(id), updatedUser);\r\n      queryClient.invalidateQueries({ queryKey: userKeys.lists() });\r\n      toast.success('User updated successfully!');\r\n    },\r\n    onError: (error: any) => {\r\n      const message = error.response?.data?.detail || 'Failed to update user';\r\n      toast.error(message);\r\n    },\r\n  });\r\n};\r\n\r\nexport const useDeleteUser = () => {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (id: string) => {\r\n      validateUUID(id, 'User');\r\n      return apiClient.delete(`/users/${id}`);\r\n    },\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: userKeys.lists() });\r\n      toast.success('User deleted successfully!');\r\n    },\r\n    onError: (error: any) => {\r\n      const message = error.response?.data?.detail || 'Failed to delete user';\r\n      toast.error(message);\r\n    },\r\n  });\r\n};\r\n\r\n// User profile hooks\r\nexport const useUpdateProfile = (userId: string) => {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (data: Partial<UserUpdate>) => \r\n      apiClient.patch<User>(`/users/${userId}`, data),\r\n    onSuccess: (updatedUser) => {\r\n      queryClient.setQueryData(['auth', 'user'], updatedUser);\r\n      toast.success('Profile updated successfully!');\r\n    },\r\n    onError: (error: any) => {\r\n      const message = error.response?.data?.detail || 'Failed to update profile';\r\n      toast.error(message);\r\n    },\r\n  });\r\n};\r\n\r\nexport const useChangePassword = () => {\r\n  return useMutation({\r\n    mutationFn: ({ old_password, new_password }: { old_password: string; new_password: string }) =>\r\n      apiClient.patch('/users/me/change-password', { old_password, new_password }),\r\n    onSuccess: () => {\r\n      toast.success('Password changed successfully!');\r\n    },\r\n    onError: (error: any) => {\r\n      const message = error.response?.data?.detail || 'Failed to change password';\r\n      toast.error(message);\r\n    },\r\n  });\r\n};\r\n\r\n// User statistics hook\r\nexport const useUserStats = () => {\r\n  return useQuery({\r\n    queryKey: [...userKeys.all, 'stats'],\r\n    queryFn: () => apiClient.get('/users/stats'),\r\n    staleTime: 5 * 60 * 1000,\r\n  });\r\n};"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;;;;;;AAGO,MAAM,WAAW;IACtB,KAAK;QAAC;KAAQ;IACd,OAAO,IAAM;eAAI,SAAS,GAAG;YAAE;SAAO;IACtC,MAAM,CAAC,UAAiB;eAAI,SAAS,KAAK;YAAI;SAAQ;IACtD,SAAS,IAAM;eAAI,SAAS,GAAG;YAAE;SAAS;IAC1C,QAAQ,CAAC,KAAe;eAAI,SAAS,OAAO;YAAI;SAAG;AACrD;AAGO,MAAM,WAAW;QAAC,2EAQrB,CAAC;;IACH,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,SAAS,IAAI,CAAC;QACxB,OAAO;iCAAE,IAAM,oHAAA,CAAA,YAAS,CAAC,GAAG,CAA0B,UAAU;oBAC9D,QAAQ;gBACV;;QACA,WAAW,KAAK;IAClB;AACF;GAhBa;;QASJ,8KAAA,CAAA,WAAQ;;;AASV,MAAM,mBAAmB;QAAC,2EAO7B,CAAC;;IACH,OAAO,CAAA,GAAA,sLAAA,CAAA,mBAAgB,AAAD,EAAE;QACtB,UAAU,SAAS,IAAI,CAAC;QACxB,OAAO;iDAAE;oBAAC,EAAE,YAAY,CAAC,EAAE;uBACzB,oHAAA,CAAA,YAAS,CAAC,GAAG,CAA0B,UAAU;oBAC/C,QAAQ;wBAAE,GAAG,OAAO;wBAAE,MAAM;oBAAU;gBACxC;;;QACF,gBAAgB;iDAAE,CAAC;gBACjB,IAAI,SAAS,IAAI,GAAG,SAAS,KAAK,EAAE;oBAClC,OAAO,SAAS,IAAI,GAAG;gBACzB;gBACA,OAAO;YACT;;QACA,kBAAkB;QAClB,WAAW,KAAK;IAClB;AACF;IAvBa;;QAQJ,sLAAA,CAAA,mBAAgB;;;AAiBlB,MAAM,UAAU,CAAC;;IACtB,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,SAAS,MAAM,CAAC;QAC1B,OAAO;gCAAE,IAAM,oHAAA,CAAA,YAAS,CAAC,GAAG,CAAO,AAAC,UAAY,OAAH;;QAC7C,WAAW,IAAI,KAAK;QACpB,SAAS,CAAC,CAAC,MAAM,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE;IAC/B;AACF;IAPa;;QACJ,8KAAA,CAAA,WAAQ;;;AAQV,MAAM,gBAAgB;;IAC3B,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;yCAAE,CAAC,OAAqB,oHAAA,CAAA,YAAS,CAAC,IAAI,CAAO,UAAU;;QACjE,SAAS;yCAAE,CAAC;gBACV,YAAY,iBAAiB,CAAC;oBAAE,UAAU,SAAS,KAAK;gBAAG;gBAC3D,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;YAChB;;QACA,OAAO;yCAAE,CAAC;oBACQ,sBAAA;gBAAhB,MAAM,UAAU,EAAA,kBAAA,MAAM,QAAQ,cAAd,uCAAA,uBAAA,gBAAgB,IAAI,cAApB,2CAAA,qBAAsB,MAAM,KAAI;gBAChD,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACd;;IACF;AACF;IAda;;QACS,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAab,MAAM,gBAAgB,CAAC;;IAC5B,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;yCAAE,CAAC;gBACX,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,IAAI;gBACjB,OAAO,oHAAA,CAAA,YAAS,CAAC,KAAK,CAAO,AAAC,UAAY,OAAH,KAAM;YAC/C;;QACA,SAAS;yCAAE,CAAC;gBACV,YAAY,YAAY,CAAC,SAAS,MAAM,CAAC,KAAK;gBAC9C,YAAY,iBAAiB,CAAC;oBAAE,UAAU,SAAS,KAAK;gBAAG;gBAC3D,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;YAChB;;QACA,OAAO;yCAAE,CAAC;oBACQ,sBAAA;gBAAhB,MAAM,UAAU,EAAA,kBAAA,MAAM,QAAQ,cAAd,uCAAA,uBAAA,gBAAgB,IAAI,cAApB,2CAAA,qBAAsB,MAAM,KAAI;gBAChD,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACd;;IACF;AACF;IAlBa;;QACS,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAiBb,MAAM,gBAAgB;;IAC3B,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;yCAAE,CAAC;gBACX,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,IAAI;gBACjB,OAAO,oHAAA,CAAA,YAAS,CAAC,MAAM,CAAC,AAAC,UAAY,OAAH;YACpC;;QACA,SAAS;yCAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU,SAAS,KAAK;gBAAG;gBAC3D,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;YAChB;;QACA,OAAO;yCAAE,CAAC;oBACQ,sBAAA;gBAAhB,MAAM,UAAU,EAAA,kBAAA,MAAM,QAAQ,cAAd,uCAAA,uBAAA,gBAAgB,IAAI,cAApB,2CAAA,qBAAsB,MAAM,KAAI;gBAChD,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACd;;IACF;AACF;IAjBa;;QACS,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAiBb,MAAM,mBAAmB,CAAC;;IAC/B,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;4CAAE,CAAC,OACX,oHAAA,CAAA,YAAS,CAAC,KAAK,CAAO,AAAC,UAAgB,OAAP,SAAU;;QAC5C,SAAS;4CAAE,CAAC;gBACV,YAAY,YAAY,CAAC;oBAAC;oBAAQ;iBAAO,EAAE;gBAC3C,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;YAChB;;QACA,OAAO;4CAAE,CAAC;oBACQ,sBAAA;gBAAhB,MAAM,UAAU,EAAA,kBAAA,MAAM,QAAQ,cAAd,uCAAA,uBAAA,gBAAgB,IAAI,cAApB,2CAAA,qBAAsB,MAAM,KAAI;gBAChD,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACd;;IACF;AACF;IAfa;;QACS,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAcb,MAAM,oBAAoB;;IAC/B,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;6CAAE;oBAAC,EAAE,YAAY,EAAE,YAAY,EAAkD;uBACzF,oHAAA,CAAA,YAAS,CAAC,KAAK,CAAC,6BAA6B;oBAAE;oBAAc;gBAAa;;;QAC5E,SAAS;6CAAE;gBACT,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;YAChB;;QACA,OAAO;6CAAE,CAAC;oBACQ,sBAAA;gBAAhB,MAAM,UAAU,EAAA,kBAAA,MAAM,QAAQ,cAAd,uCAAA,uBAAA,gBAAgB,IAAI,cAApB,2CAAA,qBAAsB,MAAM,KAAI;gBAChD,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACd;;IACF;AACF;IAZa;;QACJ,iLAAA,CAAA,cAAW;;;AAcb,MAAM,eAAe;;IAC1B,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;eAAI,SAAS,GAAG;YAAE;SAAQ;QACpC,OAAO;qCAAE,IAAM,oHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;;QAC7B,WAAW,IAAI,KAAK;IACtB;AACF;IANa;;QACJ,8KAAA,CAAA,WAAQ", "debugId": null}}, {"offset": {"line": 435, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/backend/lc-workflow-frontend/src/components/layout/Sidebar.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { \n  HomeIcon, \n  DocumentTextIcon, \n  UsersIcon, \n  BuildingOfficeIcon, \n  MapPinIcon, \n  Cog6ToothIcon,\n  FolderIcon,\n  ChevronDownIcon,\n  ChevronRightIcon,\n  XMarkIcon,\n  Bars3Icon,\n} from '@heroicons/react/24/outline';\nimport { useAuthContext } from '@/providers/AuthProvider';\n\ninterface NavItem {\n  name: string;\n  href: string;\n  icon: React.ComponentType<{ className?: string }>;\n  requiredRoles?: string[];\n}\n\nconst navigation: NavItem[] = [\n  { name: 'Dashboard', href: '/dashboard', icon: HomeIcon },\n  { name: 'Applications', href: '/applications', icon: DocumentTextIcon },\n  { name: 'Files', href: '/files', icon: FolderIcon },\n];\n\nconst adminNavigation: NavItem[] = [\n  { name: 'Users', href: '/users', icon: UsersIcon, requiredRoles: ['admin', 'manager'] },\n  { name: 'Departments', href: '/departments', icon: BuildingOfficeIcon, requiredRoles: ['admin', 'manager'] },\n  { name: 'Branches', href: '/branches', icon: MapPinIcon, requiredRoles: ['admin', 'manager'] },\n  { name: 'Settings', href: '/settings', icon: Cog6ToothIcon, requiredRoles: ['admin'] },\n];\n\ninterface SidebarProps {\n  isOpen: boolean;\n  onClose: () => void;\n}\n\nexport function Sidebar({ isOpen, onClose }: SidebarProps) {\n  const pathname = usePathname();\n  const { user, isAdmin, isManager } = useAuthContext();\n\n  const hasRequiredRole = (requiredRoles?: string[]) => {\n    if (!requiredRoles) return true;\n    return isAdmin || (isManager && requiredRoles.includes('manager'));\n  };\n\n  const allNavigation = [...navigation, ...adminNavigation];\n\n  const NavLink = ({ item }: { item: NavItem }) => {\n    const isActive = pathname === item.href;\n    \n    if (!hasRequiredRole(item.requiredRoles)) return null;\n\n    return (\n      <Link\n        href={item.href}\n        className={`group flex items-center gap-x-3 rounded-md p-2 text-sm font-semibold leading-6 transition-all duration-200 ${\n          isActive\n            ? 'bg-blue-600 text-white'\n            : 'text-gray-700 hover:bg-gray-50 hover:text-blue-600'\n        }`}\n        onClick={onClose}\n      >\n        <item.icon className=\"h-5 w-5 shrink-0\" />\n        {item.name}\n      </Link>\n    );\n  };\n\n  return (\n    <>\n      {/* Mobile sidebar */}\n      <div\n        className={`fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:hidden ${\n          isOpen ? 'translate-x-0' : '-translate-x-full'\n        }`}\n      >\n        <div className=\"flex h-16 items-center justify-between px-4\">\n          <div className=\"text-xl font-bold text-gray-900\">LC Workflow</div>\n          <button\n            type=\"button\"\n            className=\"-m-2.5 p-2.5 text-gray-700\"\n            onClick={onClose}\n          >\n            <XMarkIcon className=\"h-6 w-6\" />\n          </button>\n        </div>\n        <nav className=\"flex flex-1 flex-col gap-y-2 p-4\">\n          {allNavigation.map((item) => (\n            <NavLink key={item.name} item={item} />\n          ))}\n        </nav>\n      </div>\n\n      {/* Desktop sidebar */}\n      <div className=\"hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-64 lg:flex-col\">\n        <div className=\"flex grow flex-col gap-y-5 overflow-y-auto bg-white px-6 ring-1 ring-gray-200\">\n          <div className=\"flex h-16 shrink-0 items-center\">\n            <div className=\"text-xl font-bold text-gray-900\">LC Workflow</div>\n          </div>\n          <nav className=\"flex flex-1 flex-col gap-y-2\">\n            {allNavigation.map((item) => (\n              <NavLink key={item.name} item={item} />\n            ))}\n          </nav>\n          \n          {/* User info */}\n          {user && (\n            <div className=\"mb-4\">\n              <div className=\"flex items-center gap-x-3 rounded-md p-2 text-sm font-medium text-gray-700\">\n                <div className=\"h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center\">\n                  <span className=\"text-sm font-medium text-gray-600\">\n                    {user.first_name.charAt(0)}{user.last_name.charAt(0)}\n                  </span>\n                </div>\n                <div>\n                  <div className=\"font-medium\">{user.first_name} {user.last_name}</div>\n                  <div className=\"text-xs text-gray-500\">{user.role}</div>\n                </div>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </>\n  );\n}"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;;;AAlBA;;;;;AA2BA,MAAM,aAAwB;IAC5B;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,kNAAA,CAAA,WAAQ;IAAC;IACxD;QAAE,MAAM;QAAgB,MAAM;QAAiB,MAAM,kOAAA,CAAA,mBAAgB;IAAC;IACtE;QAAE,MAAM;QAAS,MAAM;QAAU,MAAM,sNAAA,CAAA,aAAU;IAAC;CACnD;AAED,MAAM,kBAA6B;IACjC;QAAE,MAAM;QAAS,MAAM;QAAU,MAAM,oNAAA,CAAA,YAAS;QAAE,eAAe;YAAC;YAAS;SAAU;IAAC;IACtF;QAAE,MAAM;QAAe,MAAM;QAAgB,MAAM,sOAAA,CAAA,qBAAkB;QAAE,eAAe;YAAC;YAAS;SAAU;IAAC;IAC3G;QAAE,MAAM;QAAY,MAAM;QAAa,MAAM,sNAAA,CAAA,aAAU;QAAE,eAAe;YAAC;YAAS;SAAU;IAAC;IAC7F;QAAE,MAAM;QAAY,MAAM;QAAa,MAAM,4NAAA,CAAA,gBAAa;QAAE,eAAe;YAAC;SAAQ;IAAC;CACtF;AAOM,SAAS,QAAQ,KAAiC;QAAjC,EAAE,MAAM,EAAE,OAAO,EAAgB,GAAjC;;IACtB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,iBAAc,AAAD;IAElD,MAAM,kBAAkB,CAAC;QACvB,IAAI,CAAC,eAAe,OAAO;QAC3B,OAAO,WAAY,aAAa,cAAc,QAAQ,CAAC;IACzD;IAEA,MAAM,gBAAgB;WAAI;WAAe;KAAgB;IAEzD,MAAM,UAAU;YAAC,EAAE,IAAI,EAAqB;QAC1C,MAAM,WAAW,aAAa,KAAK,IAAI;QAEvC,IAAI,CAAC,gBAAgB,KAAK,aAAa,GAAG,OAAO;QAEjD,qBACE,6LAAC,+JAAA,CAAA,UAAI;YACH,MAAM,KAAK,IAAI;YACf,WAAW,AAAC,8GAIX,OAHC,WACI,2BACA;YAEN,SAAS;;8BAET,6LAAC,KAAK,IAAI;oBAAC,WAAU;;;;;;gBACpB,KAAK,IAAI;;;;;;;IAGhB;IAEA,qBACE;;0BAEE,6LAAC;gBACC,WAAW,AAAC,yHAEX,OADC,SAAS,kBAAkB;;kCAG7B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAAkC;;;;;;0CACjD,6LAAC;gCACC,MAAK;gCACL,WAAU;gCACV,SAAS;0CAET,cAAA,6LAAC,oNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAGzB,6LAAC;wBAAI,WAAU;kCACZ,cAAc,GAAG,CAAC,CAAC,qBAClB,6LAAC;gCAAwB,MAAM;+BAAjB,KAAK,IAAI;;;;;;;;;;;;;;;;0BAM7B,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CAAkC;;;;;;;;;;;sCAEnD,6LAAC;4BAAI,WAAU;sCACZ,cAAc,GAAG,CAAC,CAAC,qBAClB,6LAAC;oCAAwB,MAAM;mCAAjB,KAAK,IAAI;;;;;;;;;;wBAK1B,sBACC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;;gDACb,KAAK,UAAU,CAAC,MAAM,CAAC;gDAAI,KAAK,SAAS,CAAC,MAAM,CAAC;;;;;;;;;;;;kDAGtD,6LAAC;;0DACC,6LAAC;gDAAI,WAAU;;oDAAe,KAAK,UAAU;oDAAC;oDAAE,KAAK,SAAS;;;;;;;0DAC9D,6LAAC;gDAAI,WAAU;0DAAyB,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASnE;GAzFgB;;QACG,qIAAA,CAAA,cAAW;QACS,oIAAA,CAAA,iBAAc;;;KAFrC", "debugId": null}}, {"offset": {"line": 734, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/backend/lc-workflow-frontend/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useRef, useEffect } from 'react';\r\nimport { Bars3Icon, BellIcon, UserCircleIcon, ArrowRightOnRectangleIcon, Cog6ToothIcon } from '@heroicons/react/24/outline';\r\nimport { useLogout } from '@/hooks/useAuth';\r\nimport { useAuthContext } from '@/providers/AuthProvider';\r\nimport Link from 'next/link';\r\n\r\ninterface HeaderProps {\r\n  onMenuClick: () => void;\r\n}\r\n\r\nexport function Header({ onMenuClick }: HeaderProps) {\r\n  const logout = useLogout();\r\n  const { user } = useAuthContext();\r\n  const [profileDropdownOpen, setProfileDropdownOpen] = useState(false);\r\n  const dropdownRef = useRef<HTMLDivElement>(null);\r\n\r\n  // Close dropdown when clicking outside\r\n  useEffect(() => {\r\n    function handleClickOutside(event: MouseEvent) {\r\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\r\n        setProfileDropdownOpen(false);\r\n      }\r\n    }\r\n\r\n    document.addEventListener('mousedown', handleClickOutside);\r\n    return () => {\r\n      document.removeEventListener('mousedown', handleClickOutside);\r\n    };\r\n  }, []);\r\n\r\n  return (\r\n    <div className=\"sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 bg-white px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8\">\r\n      <button\r\n        type=\"button\"\r\n        className=\"-m-2.5 p-2.5 text-gray-700 lg:hidden\"\r\n        onClick={onMenuClick}\r\n      >\r\n        <span className=\"sr-only\">Open sidebar</span>\r\n        <Bars3Icon className=\"h-6 w-6\" aria-hidden=\"true\" />\r\n      </button>\r\n\r\n      {/* Separator */}\r\n      <div className=\"h-6 w-px bg-gray-900/10 lg:hidden\" aria-hidden=\"true\" />\r\n\r\n      <div className=\"flex flex-1 gap-x-4 self-stretch lg:gap-x-6\">\r\n        <div className=\"flex flex-1\" />\r\n\r\n        <div className=\"flex items-center gap-x-4 lg:gap-x-6\">\r\n          <button\r\n            type=\"button\"\r\n            className=\"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500\"\r\n          >\r\n            <span className=\"sr-only\">View notifications</span>\r\n            <BellIcon className=\"h-6 w-6\" aria-hidden=\"true\" />\r\n          </button>\r\n\r\n          {/* Separator */}\r\n          <div className=\"hidden lg:block lg:h-6 lg:w-px lg:bg-gray-900/10\" aria-hidden=\"true\" />\r\n\r\n          {/* Profile dropdown */}\r\n          <div className=\"relative\">\r\n            <button\r\n              type=\"button\"\r\n              className=\"-m-1.5 flex items-center p-1.5\"\r\n              id=\"user-menu-button\"\r\n              aria-expanded=\"false\"\r\n              aria-haspopup=\"true\"\r\n              onClick={() => setProfileDropdownOpen(!profileDropdownOpen)}\r\n            >\r\n              <span className=\"sr-only\">Open user menu</span>\r\n              <div className=\"h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center\">\r\n                <span className=\"text-sm font-medium text-gray-600\">\r\n                  {user?.first_name?.charAt(0)}{user?.last_name?.charAt(0)}\r\n                </span>\r\n              </div>\r\n              <span className=\"hidden lg:flex lg:items-center\">\r\n                <span className=\"ml-4 text-sm leading-6 text-gray-900\" aria-hidden=\"true\">\r\n                  <span className=\"font-semibold\">{user?.first_name} {user?.last_name}</span>\r\n                  {user?.position?.title ? (\r\n                    <span className=\"ml-2 text-gray-500 truncate max-w-[10rem]\">\r\n                      • {user?.position?.title}\r\n                    </span>\r\n                  ) : null}\r\n                </span>\r\n              </span>\r\n            </button>\r\n\r\n            {/* Dropdown menu */}\r\n            {profileDropdownOpen && (\r\n              <div\r\n                className=\"absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none\"\r\n                role=\"menu\"\r\n                aria-orientation=\"vertical\"\r\n                aria-labelledby=\"user-menu-button\"\r\n                ref={dropdownRef}\r\n              >\r\n                <div className=\"px-4 py-3\">\r\n                  <p className=\"text-sm\">Signed in as</p>\r\n                  <p className=\"truncate text-sm font-medium text-gray-900\">{user?.email}</p>\r\n                </div>\r\n                <div className=\"border-t border-gray-100\"></div>\r\n                <Link\r\n                  href=\"/profile\"\r\n                  className=\"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\r\n                  role=\"menuitem\"\r\n                  onClick={() => setProfileDropdownOpen(false)}\r\n                >\r\n                  <UserCircleIcon className=\"mr-3 h-5 w-5 text-gray-400\" aria-hidden=\"true\" />\r\n                  Your Profile\r\n                </Link>\r\n                <Link\r\n                  href=\"/settings\"\r\n                  className=\"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\r\n                  role=\"menuitem\"\r\n                  onClick={() => setProfileDropdownOpen(false)}\r\n                >\r\n                  <Cog6ToothIcon className=\"mr-3 h-5 w-5 text-gray-400\" aria-hidden=\"true\" />\r\n                  Settings\r\n                </Link>\r\n                <div className=\"border-t border-gray-100\"></div>\r\n                <button\r\n                  type=\"button\"\r\n                  className=\"flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\r\n                  role=\"menuitem\"\r\n                  onClick={() => {\r\n                    setProfileDropdownOpen(false);\r\n                    logout.mutate();\r\n                  }}\r\n                >\r\n                  <ArrowRightOnRectangleIcon className=\"mr-3 h-5 w-5 text-gray-400\" aria-hidden=\"true\" />\r\n                  Sign out\r\n                </button>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;AANA;;;;;;AAYO,SAAS,OAAO,KAA4B;QAA5B,EAAE,WAAW,EAAe,GAA5B;QA8DJ,kBAA6B,iBAM7B,gBAEM;;IArEvB,MAAM,SAAS,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,iBAAc,AAAD;IAC9B,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAE3C,uCAAuC;IACvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,SAAS,mBAAmB,KAAiB;gBAC3C,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;oBAC9E,uBAAuB;gBACzB;YACF;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC;oCAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;gBAC5C;;QACF;2BAAG,EAAE;IAEL,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACC,MAAK;gBACL,WAAU;gBACV,SAAS;;kCAET,6LAAC;wBAAK,WAAU;kCAAU;;;;;;kCAC1B,6LAAC,oNAAA,CAAA,YAAS;wBAAC,WAAU;wBAAU,eAAY;;;;;;;;;;;;0BAI7C,6LAAC;gBAAI,WAAU;gBAAoC,eAAY;;;;;;0BAE/D,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCAEf,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,MAAK;gCACL,WAAU;;kDAEV,6LAAC;wCAAK,WAAU;kDAAU;;;;;;kDAC1B,6LAAC,kNAAA,CAAA,WAAQ;wCAAC,WAAU;wCAAU,eAAY;;;;;;;;;;;;0CAI5C,6LAAC;gCAAI,WAAU;gCAAmD,eAAY;;;;;;0CAG9E,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,WAAU;wCACV,IAAG;wCACH,iBAAc;wCACd,iBAAc;wCACd,SAAS,IAAM,uBAAuB,CAAC;;0DAEvC,6LAAC;gDAAK,WAAU;0DAAU;;;;;;0DAC1B,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;;wDACb,iBAAA,4BAAA,mBAAA,KAAM,UAAU,cAAhB,uCAAA,iBAAkB,MAAM,CAAC;wDAAI,iBAAA,4BAAA,kBAAA,KAAM,SAAS,cAAf,sCAAA,gBAAiB,MAAM,CAAC;;;;;;;;;;;;0DAG1D,6LAAC;gDAAK,WAAU;0DACd,cAAA,6LAAC;oDAAK,WAAU;oDAAuC,eAAY;;sEACjE,6LAAC;4DAAK,WAAU;;gEAAiB,iBAAA,2BAAA,KAAM,UAAU;gEAAC;gEAAE,iBAAA,2BAAA,KAAM,SAAS;;;;;;;wDAClE,CAAA,iBAAA,4BAAA,iBAAA,KAAM,QAAQ,cAAd,qCAAA,eAAgB,KAAK,kBACpB,6LAAC;4DAAK,WAAU;;gEAA4C;gEACvD,iBAAA,4BAAA,kBAAA,KAAM,QAAQ,cAAd,sCAAA,gBAAgB,KAAK;;;;;;mEAExB;;;;;;;;;;;;;;;;;;oCAMT,qCACC,6LAAC;wCACC,WAAU;wCACV,MAAK;wCACL,oBAAiB;wCACjB,mBAAgB;wCAChB,KAAK;;0DAEL,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAU;;;;;;kEACvB,6LAAC;wDAAE,WAAU;kEAA8C,iBAAA,2BAAA,KAAM,KAAK;;;;;;;;;;;;0DAExE,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,MAAK;gDACL,SAAS,IAAM,uBAAuB;;kEAEtC,6LAAC,8NAAA,CAAA,iBAAc;wDAAC,WAAU;wDAA6B,eAAY;;;;;;oDAAS;;;;;;;0DAG9E,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,MAAK;gDACL,SAAS,IAAM,uBAAuB;;kEAEtC,6LAAC,4NAAA,CAAA,gBAAa;wDAAC,WAAU;wDAA6B,eAAY;;;;;;oDAAS;;;;;;;0DAG7E,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDACC,MAAK;gDACL,WAAU;gDACV,MAAK;gDACL,SAAS;oDACP,uBAAuB;oDACvB,OAAO,MAAM;gDACf;;kEAEA,6LAAC,oPAAA,CAAA,4BAAyB;wDAAC,WAAU;wDAA6B,eAAY;;;;;;oDAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUzG;GAjIgB;;QACC,0HAAA,CAAA,YAAS;QACP,oIAAA,CAAA,iBAAc;;;KAFjB", "debugId": null}}, {"offset": {"line": 1108, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/backend/lc-workflow-frontend/src/components/layout/Layout.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Sidebar } from './Sidebar';\nimport { Header } from './Header';\n\ninterface LayoutProps {\n  children: React.ReactNode;\n}\n\nexport function Layout({ children }: LayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n\n  return (\n    <>\n      <div className=\"min-h-full\">\n        <Sidebar \n          isOpen={sidebarOpen} \n          onClose={() => setSidebarOpen(false)} \n        />\n        \n        <div className=\"lg:pl-64\">\n          <Header onMenuClick={() => setSidebarOpen(true)} />\n          \n          <main className=\"py-10\">\n            <div className=\"px-4 sm:px-6 lg:px-8\">\n              {children}\n            </div>\n          </main>\n        </div>\n      </div>\n    </>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAUO,SAAS,OAAO,KAAyB;QAAzB,EAAE,QAAQ,EAAe,GAAzB;;IACrB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,qBACE;kBACE,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,0IAAA,CAAA,UAAO;oBACN,QAAQ;oBACR,SAAS,IAAM,eAAe;;;;;;8BAGhC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,yIAAA,CAAA,SAAM;4BAAC,aAAa,IAAM,eAAe;;;;;;sCAE1C,6LAAC;4BAAK,WAAU;sCACd,cAAA,6LAAC;gCAAI,WAAU;0CACZ;;;;;;;;;;;;;;;;;;;;;;;;AAOf;GAvBgB;KAAA", "debugId": null}}, {"offset": {"line": 1189, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/backend/lc-workflow-frontend/src/components/auth/ProtectedRoute.tsx"], "sourcesContent": ["'use client';\n\nimport { useRouter } from 'next/navigation';\nimport { useAuthContext } from '@/providers/AuthProvider';\nimport { useEffect } from 'react';\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode;\n  requiredRoles?: string[];\n}\n\nexport function ProtectedRoute({ children, requiredRoles }: ProtectedRouteProps) {\n  const router = useRouter();\n  const { isAuthenticated, isLoading, user, role } = useAuthContext();\n\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      if (!isLoading) {\n        if (!isAuthenticated) {\n          router.push('/login');\n        } else if (requiredRoles && requiredRoles.length > 0 && !requiredRoles.includes(role || '')) {\n          router.push('/unauthorized');\n        }\n      }\n    }, 1000); // Wait 1 second for auth state to stabilize\n\n    return () => clearTimeout(timer);\n  }, [isAuthenticated, isLoading, requiredRoles, role, router]);\n\n  if (isLoading || !isAuthenticated) {\n    return (\n      <div className=\"flex min-h-screen items-center justify-center\">\n        <div className=\"h-8 w-8 animate-spin rounded-full border-4 border-blue-600 border-t-transparent\"></div>\n      </div>\n    );\n  }\n\n  if (requiredRoles && requiredRoles.length > 0 && !requiredRoles.includes(role || '')) {\n    return null;\n  }\n\n  return <>{children}</>;\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAWO,SAAS,eAAe,KAAgD;QAAhD,EAAE,QAAQ,EAAE,aAAa,EAAuB,GAAhD;;IAC7B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,iBAAc,AAAD;IAEhE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM,QAAQ;kDAAW;oBACvB,IAAI,CAAC,WAAW;wBACd,IAAI,CAAC,iBAAiB;4BACpB,OAAO,IAAI,CAAC;wBACd,OAAO,IAAI,iBAAiB,cAAc,MAAM,GAAG,KAAK,CAAC,cAAc,QAAQ,CAAC,QAAQ,KAAK;4BAC3F,OAAO,IAAI,CAAC;wBACd;oBACF;gBACF;iDAAG,OAAO,4CAA4C;YAEtD;4CAAO,IAAM,aAAa;;QAC5B;mCAAG;QAAC;QAAiB;QAAW;QAAe;QAAM;KAAO;IAE5D,IAAI,aAAa,CAAC,iBAAiB;QACjC,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,iBAAiB,cAAc,MAAM,GAAG,KAAK,CAAC,cAAc,QAAQ,CAAC,QAAQ,KAAK;QACpF,OAAO;IACT;IAEA,qBAAO;kBAAG;;AACZ;GA/BgB;;QACC,qIAAA,CAAA,YAAS;QAC2B,oIAAA,CAAA,iBAAc;;;KAFnD", "debugId": null}}, {"offset": {"line": 1272, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/backend/lc-workflow-frontend/src/app/profile/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport { useAuthContext } from '@/providers/AuthProvider';\r\nimport { useUpdateProfile, useChangePassword } from '@/hooks/useUsers';\r\nimport { Layout } from '@/components/layout/Layout';\r\nimport { ProtectedRoute } from '@/components/auth/ProtectedRoute';\r\nimport { Eye, EyeOff, Save, User as UserIcon, Mail, Phone, Shield } from 'lucide-react';\r\nimport toast from 'react-hot-toast';\r\n\r\nexport default function ProfilePage() {\r\n  const { user } = useAuthContext();\r\n  const updateProfile = useUpdateProfile(user?.id || '');\r\n  const changePassword = useChangePassword();\r\n\r\n  const [profileData, setProfileData] = useState({\r\n    first_name: '',\r\n    last_name: '',\r\n    email: '',\r\n    phone_number: '',\r\n    username: '',\r\n    employee_id: '',\r\n  });\r\n\r\n  const [passwordData, setPasswordData] = useState({\r\n    old_password: '',\r\n    new_password: '',\r\n    confirm_password: '',\r\n  });\r\n\r\n  const [showOldPassword, setShowOldPassword] = useState(false);\r\n  const [showNewPassword, setShowNewPassword] = useState(false);\r\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\r\n  const [errors, setErrors] = useState<Record<string, string>>({});\r\n\r\n  useEffect(() => {\r\n    if (user) {\r\n      setProfileData({\r\n        first_name: user.first_name || '',\r\n        last_name: user.last_name || '',\r\n        email: user.email || '',\r\n        phone_number: user.phone_number || '',\r\n        username: user.username || '',\r\n        employee_id: user.employee_id || '',\r\n      });\r\n    }\r\n  }, [user]);\r\n\r\n  const handleProfileChange = (field: string, value: string) => {\r\n    setProfileData(prev => ({ ...prev, [field]: value }));\r\n    if (errors[field]) {\r\n      setErrors(prev => ({ ...prev, [field]: '' }));\r\n    }\r\n  };\r\n\r\n  const handlePasswordChange = (field: string, value: string) => {\r\n    setPasswordData(prev => ({ ...prev, [field]: value }));\r\n    if (errors[field]) {\r\n      setErrors(prev => ({ ...prev, [field]: '' }));\r\n    }\r\n  };\r\n\r\n  const validateProfileForm = () => {\r\n    const newErrors: Record<string, string> = {};\r\n\r\n    if (!profileData.first_name) {\r\n      newErrors.first_name = 'First name is required';\r\n    }\r\n\r\n    if (!profileData.last_name) {\r\n      newErrors.last_name = 'Last name is required';\r\n    }\r\n\r\n    if (!profileData.email) {\r\n      newErrors.email = 'Email is required';\r\n    } else if (!/^\\S+@\\S+\\.\\S+$/.test(profileData.email)) {\r\n      newErrors.email = 'Invalid email format';\r\n    }\r\n\r\n    setErrors(newErrors);\r\n    return Object.keys(newErrors).length === 0;\r\n  };\r\n\r\n  const validatePasswordForm = () => {\r\n    const newErrors: Record<string, string> = {};\r\n\r\n    if (!passwordData.old_password) {\r\n      newErrors.old_password = 'Current password is required';\r\n    }\r\n\r\n    if (!passwordData.new_password) {\r\n      newErrors.new_password = 'New password is required';\r\n    } else if (passwordData.new_password.length < 6) {\r\n      newErrors.new_password = 'Password must be at least 6 characters';\r\n    }\r\n\r\n    if (!passwordData.confirm_password) {\r\n      newErrors.confirm_password = 'Please confirm your new password';\r\n    } else if (passwordData.new_password !== passwordData.confirm_password) {\r\n      newErrors.confirm_password = 'Passwords do not match';\r\n    }\r\n\r\n    setErrors(newErrors);\r\n    return Object.keys(newErrors).length === 0;\r\n  };\r\n\r\n  const handleProfileSubmit = (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n\r\n    if (!validateProfileForm()) {\r\n      return;\r\n    }\r\n\r\n    // Strip empty fields and ensure employee_id is 4 digits or omitted\r\n    const filteredData = Object.fromEntries(\r\n      Object.entries(profileData).filter(([key, value]) => {\r\n        if (key === 'employee_id') {\r\n          return value && /^\\d{4}$/.test(value);\r\n        }\r\n        return value !== '' && value !== undefined && value !== null;\r\n      })\r\n    );\r\n    updateProfile.mutate(filteredData);\r\n  };\r\n\r\n  const handlePasswordSubmit = (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n\r\n    if (!validatePasswordForm()) {\r\n      return;\r\n    }\r\n\r\n    changePassword.mutate({\r\n      old_password: passwordData.old_password,\r\n      new_password: passwordData.new_password,\r\n    }, {\r\n      onSuccess: () => {\r\n        setPasswordData({\r\n          old_password: '',\r\n          new_password: '',\r\n          confirm_password: '',\r\n        });\r\n      }\r\n    });\r\n  };\r\n\r\n  if (!user) {\r\n    return (\r\n      <ProtectedRoute>\r\n        <Layout>\r\n          <div className=\"flex items-center justify-center py-12\">\r\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\r\n          </div>\r\n        </Layout>\r\n      </ProtectedRoute>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <ProtectedRoute>\r\n      <Layout>\r\n        <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\r\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-8\">My Profile</h1>\r\n\r\n          <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden mb-8\">\r\n            {/* Header Section */}\r\n            <div className=\"bg-gradient-to-r from-blue-500 to-blue-600 px-6 py-8\">\r\n              <div className=\"flex items-center space-x-4\">\r\n                <div className=\"h-20 w-20 bg-white rounded-full flex items-center justify-center\">\r\n                  <UserIcon className=\"h-10 w-10 text-blue-600\" />\r\n                </div>\r\n                <div className=\"text-white\">\r\n                  <h2 className=\"text-2xl font-bold\">\r\n                    {user.first_name} {user.last_name}\r\n                  </h2>\r\n                  <p className=\"text-blue-100\">@{user.username}</p>\r\n                  <div className=\"flex items-center mt-2 gap-2 flex-wrap\">\r\n                    <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-200 text-blue-800\">\r\n                      <Shield className=\"h-3 w-3 mr-1\" />\r\n                      {user.role}\r\n                    </span>\r\n                    {user.position ? (\r\n                      <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-white/20 text-white border border-white/30\">\r\n                        <span className=\"truncate max-w-[12rem]\">\r\n                          {user.position.title}\r\n                          {user.position.code ? <span className=\"ml-1 opacity-80\">({user.position.code})</span> : null}\r\n                          {typeof user.position.level === 'number' ? <span className=\"ml-1 opacity-80\">• L{user.position.level}</span> : null}\r\n                        </span>\r\n                      </span>\r\n                    ) : (\r\n                      <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-white/10 text-white/80 border border-white/20\">\r\n                        No position assigned\r\n                      </span>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Profile Information Form */}\r\n            <div className=\"p-6\">\r\n              <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Profile Information</h3>\r\n              <form onSubmit={handleProfileSubmit} className=\"space-y-6\">\r\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n                  {/* First Name */}\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                      First Name\r\n                    </label>\r\n                    <input\r\n                      type=\"text\"\r\n                      value={profileData.first_name}\r\n                      onChange={(e) => handleProfileChange('first_name', e.target.value)}\r\n                      className={`w-full px-3 py-2 border ${errors.first_name ? 'border-red-500' : 'border-gray-300'} rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent`}\r\n                    />\r\n                    {errors.first_name && (\r\n                      <p className=\"mt-1 text-sm text-red-600\">{errors.first_name}</p>\r\n                    )}\r\n                  </div>\r\n\r\n                  {/* Last Name */}\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                      Last Name\r\n                    </label>\r\n                    <input\r\n                      type=\"text\"\r\n                      value={profileData.last_name}\r\n                      onChange={(e) => handleProfileChange('last_name', e.target.value)}\r\n                      className={`w-full px-3 py-2 border ${errors.last_name ? 'border-red-500' : 'border-gray-300'} rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent`}\r\n                    />\r\n                    {errors.last_name && (\r\n                      <p className=\"mt-1 text-sm text-red-600\">{errors.last_name}</p>\r\n                    )}\r\n                  </div>\r\n\r\n                  {/* Email */}\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                      Email\r\n                    </label>\r\n                    <div className=\"relative\">\r\n                      <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n                        <Mail className=\"h-5 w-5 text-gray-400\" />\r\n                      </div>\r\n                      <input\r\n                        type=\"email\"\r\n                        value={profileData.email}\r\n                        onChange={(e) => handleProfileChange('email', e.target.value)}\r\n                        className={`w-full pl-10 px-3 py-2 border ${errors.email ? 'border-red-500' : 'border-gray-300'} rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent`}\r\n                      />\r\n                    </div>\r\n                    {errors.email && (\r\n                      <p className=\"mt-1 text-sm text-red-600\">{errors.email}</p>\r\n                    )}\r\n                  </div>\r\n\r\n                  {/* Phone Number */}\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                      Phone Number\r\n                    </label>\r\n                    <div className=\"relative\">\r\n                      <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n                        <Phone className=\"h-5 w-5 text-gray-400\" />\r\n                      </div>\r\n                      <input\r\n                        type=\"tel\"\r\n                        value={profileData.phone_number}\r\n                        onChange={(e) => handleProfileChange('phone_number', e.target.value)}\r\n                        className=\"w-full pl-10 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n                      />\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Employee ID (HR 4-digit) */}\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                      Employee ID (4 digits)\r\n                    </label>\r\n                    <input\r\n                      type=\"text\"\r\n                      maxLength={4}\r\n                      pattern=\"\\d{4}\"\r\n                      value={profileData.employee_id}\r\n                      onChange={(e) => handleProfileChange('employee_id', e.target.value.replace(/[^\\d]/g, '').slice(0, 4))}\r\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n                      placeholder=\"e.g. 1234\"\r\n                    />\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"flex justify-end\">\r\n                  <button\r\n                    type=\"submit\"\r\n                    disabled={updateProfile.isPending}\r\n                    className=\"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\r\n                  >\r\n                    {updateProfile.isPending ? (\r\n                      <>\r\n                        <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\r\n                        Updating...\r\n                      </>\r\n                    ) : (\r\n                      <>\r\n                        <Save className=\"h-4 w-4 mr-2\" />\r\n                        Save Changes\r\n                      </>\r\n                    )}\r\n                  </button>\r\n                </div>\r\n              </form>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Change Password Section */}\r\n          <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden\">\r\n            <div className=\"p-6\">\r\n              <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Change Password</h3>\r\n              <form onSubmit={handlePasswordSubmit} className=\"space-y-6\">\r\n                {/* Current Password */}\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                    Current Password\r\n                  </label>\r\n                  <div className=\"relative\">\r\n                    <input\r\n                      type={showOldPassword ? 'text' : 'password'}\r\n                      value={passwordData.old_password}\r\n                      onChange={(e) => handlePasswordChange('old_password', e.target.value)}\r\n                      className={`w-full pr-10 px-3 py-2 border ${errors.old_password ? 'border-red-500' : 'border-gray-300'} rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent`}\r\n                    />\r\n                    <button\r\n                      type=\"button\"\r\n                      className=\"absolute inset-y-0 right-0 pr-3 flex items-center\"\r\n                      onClick={() => setShowOldPassword(!showOldPassword)}\r\n                    >\r\n                      {showOldPassword ? (\r\n                        <EyeOff className=\"h-5 w-5 text-gray-400\" />\r\n                      ) : (\r\n                        <Eye className=\"h-5 w-5 text-gray-400\" />\r\n                      )}\r\n                    </button>\r\n                  </div>\r\n                  {errors.old_password && (\r\n                    <p className=\"mt-1 text-sm text-red-600\">{errors.old_password}</p>\r\n                  )}\r\n                </div>\r\n\r\n                {/* New Password */}\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                    New Password\r\n                  </label>\r\n                  <div className=\"relative\">\r\n                    <input\r\n                      type={showNewPassword ? 'text' : 'password'}\r\n                      value={passwordData.new_password}\r\n                      onChange={(e) => handlePasswordChange('new_password', e.target.value)}\r\n                      className={`w-full pr-10 px-3 py-2 border ${errors.new_password ? 'border-red-500' : 'border-gray-300'} rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent`}\r\n                    />\r\n                    <button\r\n                      type=\"button\"\r\n                      className=\"absolute inset-y-0 right-0 pr-3 flex items-center\"\r\n                      onClick={() => setShowNewPassword(!showNewPassword)}\r\n                    >\r\n                      {showNewPassword ? (\r\n                        <EyeOff className=\"h-5 w-5 text-gray-400\" />\r\n                      ) : (\r\n                        <Eye className=\"h-5 w-5 text-gray-400\" />\r\n                      )}\r\n                    </button>\r\n                  </div>\r\n                  {errors.new_password && (\r\n                    <p className=\"mt-1 text-sm text-red-600\">{errors.new_password}</p>\r\n                  )}\r\n                </div>\r\n\r\n                {/* Confirm New Password */}\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                    Confirm New Password\r\n                  </label>\r\n                  <div className=\"relative\">\r\n                    <input\r\n                      type={showConfirmPassword ? 'text' : 'password'}\r\n                      value={passwordData.confirm_password}\r\n                      onChange={(e) => handlePasswordChange('confirm_password', e.target.value)}\r\n                      className={`w-full pr-10 px-3 py-2 border ${errors.confirm_password ? 'border-red-500' : 'border-gray-300'} rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent`}\r\n                    />\r\n                    <button\r\n                      type=\"button\"\r\n                      className=\"absolute inset-y-0 right-0 pr-3 flex items-center\"\r\n                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}\r\n                    >\r\n                      {showConfirmPassword ? (\r\n                        <EyeOff className=\"h-5 w-5 text-gray-400\" />\r\n                      ) : (\r\n                        <Eye className=\"h-5 w-5 text-gray-400\" />\r\n                      )}\r\n                    </button>\r\n                  </div>\r\n                  {errors.confirm_password && (\r\n                    <p className=\"mt-1 text-sm text-red-600\">{errors.confirm_password}</p>\r\n                  )}\r\n                </div>\r\n\r\n                <div className=\"flex justify-end\">\r\n                  <button\r\n                    type=\"submit\"\r\n                    disabled={changePassword.isPending}\r\n                    className=\"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\r\n                  >\r\n                    {changePassword.isPending ? (\r\n                      <>\r\n                        <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\r\n                        Updating...\r\n                      </>\r\n                    ) : (\r\n                      'Change Password'\r\n                    )}\r\n                  </button>\r\n                </div>\r\n              </form>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </Layout>\r\n    </ProtectedRoute>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAPA;;;;;;;AAUe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,iBAAc,AAAD;IAC9B,MAAM,gBAAgB,CAAA,GAAA,2HAAA,CAAA,mBAAgB,AAAD,EAAE,CAAA,iBAAA,2BAAA,KAAM,EAAE,KAAI;IACnD,MAAM,iBAAiB,CAAA,GAAA,2HAAA,CAAA,oBAAiB,AAAD;IAEvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAC7C,YAAY;QACZ,WAAW;QACX,OAAO;QACP,cAAc;QACd,UAAU;QACV,aAAa;IACf;IAEA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAC/C,cAAc;QACd,cAAc;QACd,kBAAkB;IACpB;IAEA,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAE9D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,MAAM;gBACR,eAAe;oBACb,YAAY,KAAK,UAAU,IAAI;oBAC/B,WAAW,KAAK,SAAS,IAAI;oBAC7B,OAAO,KAAK,KAAK,IAAI;oBACrB,cAAc,KAAK,YAAY,IAAI;oBACnC,UAAU,KAAK,QAAQ,IAAI;oBAC3B,aAAa,KAAK,WAAW,IAAI;gBACnC;YACF;QACF;gCAAG;QAAC;KAAK;IAET,MAAM,sBAAsB,CAAC,OAAe;QAC1C,eAAe,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;QACnD,IAAI,MAAM,CAAC,MAAM,EAAE;YACjB,UAAU,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,MAAM,EAAE;gBAAG,CAAC;QAC7C;IACF;IAEA,MAAM,uBAAuB,CAAC,OAAe;QAC3C,gBAAgB,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;QACpD,IAAI,MAAM,CAAC,MAAM,EAAE;YACjB,UAAU,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,MAAM,EAAE;gBAAG,CAAC;QAC7C;IACF;IAEA,MAAM,sBAAsB;QAC1B,MAAM,YAAoC,CAAC;QAE3C,IAAI,CAAC,YAAY,UAAU,EAAE;YAC3B,UAAU,UAAU,GAAG;QACzB;QAEA,IAAI,CAAC,YAAY,SAAS,EAAE;YAC1B,UAAU,SAAS,GAAG;QACxB;QAEA,IAAI,CAAC,YAAY,KAAK,EAAE;YACtB,UAAU,KAAK,GAAG;QACpB,OAAO,IAAI,CAAC,iBAAiB,IAAI,CAAC,YAAY,KAAK,GAAG;YACpD,UAAU,KAAK,GAAG;QACpB;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,uBAAuB;QAC3B,MAAM,YAAoC,CAAC;QAE3C,IAAI,CAAC,aAAa,YAAY,EAAE;YAC9B,UAAU,YAAY,GAAG;QAC3B;QAEA,IAAI,CAAC,aAAa,YAAY,EAAE;YAC9B,UAAU,YAAY,GAAG;QAC3B,OAAO,IAAI,aAAa,YAAY,CAAC,MAAM,GAAG,GAAG;YAC/C,UAAU,YAAY,GAAG;QAC3B;QAEA,IAAI,CAAC,aAAa,gBAAgB,EAAE;YAClC,UAAU,gBAAgB,GAAG;QAC/B,OAAO,IAAI,aAAa,YAAY,KAAK,aAAa,gBAAgB,EAAE;YACtE,UAAU,gBAAgB,GAAG;QAC/B;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,sBAAsB,CAAC;QAC3B,EAAE,cAAc;QAEhB,IAAI,CAAC,uBAAuB;YAC1B;QACF;QAEA,mEAAmE;QACnE,MAAM,eAAe,OAAO,WAAW,CACrC,OAAO,OAAO,CAAC,aAAa,MAAM,CAAC;gBAAC,CAAC,KAAK,MAAM;YAC9C,IAAI,QAAQ,eAAe;gBACzB,OAAO,SAAS,UAAU,IAAI,CAAC;YACjC;YACA,OAAO,UAAU,MAAM,UAAU,aAAa,UAAU;QAC1D;QAEF,cAAc,MAAM,CAAC;IACvB;IAEA,MAAM,uBAAuB,CAAC;QAC5B,EAAE,cAAc;QAEhB,IAAI,CAAC,wBAAwB;YAC3B;QACF;QAEA,eAAe,MAAM,CAAC;YACpB,cAAc,aAAa,YAAY;YACvC,cAAc,aAAa,YAAY;QACzC,GAAG;YACD,WAAW;gBACT,gBAAgB;oBACd,cAAc;oBACd,cAAc;oBACd,kBAAkB;gBACpB;YACF;QACF;IACF;IAEA,IAAI,CAAC,MAAM;QACT,qBACE,6LAAC,+IAAA,CAAA,iBAAc;sBACb,cAAA,6LAAC,yIAAA,CAAA,SAAM;0BACL,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;;;;;IAKzB;IAEA,qBACE,6LAAC,+IAAA,CAAA,iBAAc;kBACb,cAAA,6LAAC,yIAAA,CAAA,SAAM;sBACL,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCAEtD,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,qMAAA,CAAA,OAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;;wDACX,KAAK,UAAU;wDAAC;wDAAE,KAAK,SAAS;;;;;;;8DAEnC,6LAAC;oDAAE,WAAU;;wDAAgB;wDAAE,KAAK,QAAQ;;;;;;;8DAC5C,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;;8EACd,6LAAC,yMAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;gEACjB,KAAK,IAAI;;;;;;;wDAEX,KAAK,QAAQ,iBACZ,6LAAC;4DAAK,WAAU;sEACd,cAAA,6LAAC;gEAAK,WAAU;;oEACb,KAAK,QAAQ,CAAC,KAAK;oEACnB,KAAK,QAAQ,CAAC,IAAI,iBAAG,6LAAC;wEAAK,WAAU;;4EAAkB;4EAAE,KAAK,QAAQ,CAAC,IAAI;4EAAC;;;;;;+EAAW;oEACvF,OAAO,KAAK,QAAQ,CAAC,KAAK,KAAK,yBAAW,6LAAC;wEAAK,WAAU;;4EAAkB;4EAAI,KAAK,QAAQ,CAAC,KAAK;;;;;;+EAAW;;;;;;;;;;;iFAInH,6LAAC;4DAAK,WAAU;sEAA2H;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAUrJ,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,6LAAC;wCAAK,UAAU;wCAAqB,WAAU;;0DAC7C,6LAAC;gDAAI,WAAU;;kEAEb,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAGhE,6LAAC;gEACC,MAAK;gEACL,OAAO,YAAY,UAAU;gEAC7B,UAAU,CAAC,IAAM,oBAAoB,cAAc,EAAE,MAAM,CAAC,KAAK;gEACjE,WAAW,AAAC,2BAAmF,OAAzD,OAAO,UAAU,GAAG,mBAAmB,mBAAkB;;;;;;4DAEhG,OAAO,UAAU,kBAChB,6LAAC;gEAAE,WAAU;0EAA6B,OAAO,UAAU;;;;;;;;;;;;kEAK/D,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAGhE,6LAAC;gEACC,MAAK;gEACL,OAAO,YAAY,SAAS;gEAC5B,UAAU,CAAC,IAAM,oBAAoB,aAAa,EAAE,MAAM,CAAC,KAAK;gEAChE,WAAW,AAAC,2BAAkF,OAAxD,OAAO,SAAS,GAAG,mBAAmB,mBAAkB;;;;;;4DAE/F,OAAO,SAAS,kBACf,6LAAC;gEAAE,WAAU;0EAA6B,OAAO,SAAS;;;;;;;;;;;;kEAK9D,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAGhE,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;;;;;;kFAElB,6LAAC;wEACC,MAAK;wEACL,OAAO,YAAY,KAAK;wEACxB,UAAU,CAAC,IAAM,oBAAoB,SAAS,EAAE,MAAM,CAAC,KAAK;wEAC5D,WAAW,AAAC,iCAAoF,OAApD,OAAO,KAAK,GAAG,mBAAmB,mBAAkB;;;;;;;;;;;;4DAGnG,OAAO,KAAK,kBACX,6LAAC;gEAAE,WAAU;0EAA6B,OAAO,KAAK;;;;;;;;;;;;kEAK1D,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAGhE,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;;;;;;kFAEnB,6LAAC;wEACC,MAAK;wEACL,OAAO,YAAY,YAAY;wEAC/B,UAAU,CAAC,IAAM,oBAAoB,gBAAgB,EAAE,MAAM,CAAC,KAAK;wEACnE,WAAU;;;;;;;;;;;;;;;;;;kEAMhB,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAGhE,6LAAC;gEACC,MAAK;gEACL,WAAW;gEACX,SAAQ;gEACR,OAAO,YAAY,WAAW;gEAC9B,UAAU,CAAC,IAAM,oBAAoB,eAAe,EAAE,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,IAAI,KAAK,CAAC,GAAG;gEAClG,WAAU;gEACV,aAAY;;;;;;;;;;;;;;;;;;0DAKlB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDACC,MAAK;oDACL,UAAU,cAAc,SAAS;oDACjC,WAAU;8DAET,cAAc,SAAS,iBACtB;;0EACE,6LAAC;gEAAI,WAAU;;;;;;4DAAuE;;qFAIxF;;0EACE,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAW/C,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,6LAAC;oCAAK,UAAU;oCAAsB,WAAU;;sDAE9C,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,MAAM,kBAAkB,SAAS;4DACjC,OAAO,aAAa,YAAY;4DAChC,UAAU,CAAC,IAAM,qBAAqB,gBAAgB,EAAE,MAAM,CAAC,KAAK;4DACpE,WAAW,AAAC,iCAA2F,OAA3D,OAAO,YAAY,GAAG,mBAAmB,mBAAkB;;;;;;sEAEzG,6LAAC;4DACC,MAAK;4DACL,WAAU;4DACV,SAAS,IAAM,mBAAmB,CAAC;sEAElC,gCACC,6LAAC,6MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;qFAElB,6LAAC,mMAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;;;;;;;;;;;;gDAIpB,OAAO,YAAY,kBAClB,6LAAC;oDAAE,WAAU;8DAA6B,OAAO,YAAY;;;;;;;;;;;;sDAKjE,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,MAAM,kBAAkB,SAAS;4DACjC,OAAO,aAAa,YAAY;4DAChC,UAAU,CAAC,IAAM,qBAAqB,gBAAgB,EAAE,MAAM,CAAC,KAAK;4DACpE,WAAW,AAAC,iCAA2F,OAA3D,OAAO,YAAY,GAAG,mBAAmB,mBAAkB;;;;;;sEAEzG,6LAAC;4DACC,MAAK;4DACL,WAAU;4DACV,SAAS,IAAM,mBAAmB,CAAC;sEAElC,gCACC,6LAAC,6MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;qFAElB,6LAAC,mMAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;;;;;;;;;;;;gDAIpB,OAAO,YAAY,kBAClB,6LAAC;oDAAE,WAAU;8DAA6B,OAAO,YAAY;;;;;;;;;;;;sDAKjE,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,MAAM,sBAAsB,SAAS;4DACrC,OAAO,aAAa,gBAAgB;4DACpC,UAAU,CAAC,IAAM,qBAAqB,oBAAoB,EAAE,MAAM,CAAC,KAAK;4DACxE,WAAW,AAAC,iCAA+F,OAA/D,OAAO,gBAAgB,GAAG,mBAAmB,mBAAkB;;;;;;sEAE7G,6LAAC;4DACC,MAAK;4DACL,WAAU;4DACV,SAAS,IAAM,uBAAuB,CAAC;sEAEtC,oCACC,6LAAC,6MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;qFAElB,6LAAC,mMAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;;;;;;;;;;;;gDAIpB,OAAO,gBAAgB,kBACtB,6LAAC;oDAAE,WAAU;8DAA6B,OAAO,gBAAgB;;;;;;;;;;;;sDAIrE,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDACC,MAAK;gDACL,UAAU,eAAe,SAAS;gDAClC,WAAU;0DAET,eAAe,SAAS,iBACvB;;sEACE,6LAAC;4DAAI,WAAU;;;;;;wDAAuE;;mEAIxF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWtB;GApawB;;QACL,oIAAA,CAAA,iBAAc;QACT,2HAAA,CAAA,mBAAgB;QACf,2HAAA,CAAA,oBAAiB;;;KAHlB", "debugId": null}}]}