{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/backend/lc-workflow-frontend/src/providers/QueryProvider.tsx"], "sourcesContent": ["'use client';\n\nimport { QueryClient, QueryClientProvider } from '@tanstack/react-query';\nimport { ReactQueryDevtools } from '@tanstack/react-query-devtools';\nimport { useState } from 'react';\n\nexport function QueryProvider({ children }: { children: React.ReactNode }) {\n  const [queryClient] = useState(\n    () =>\n      new QueryClient({\n        defaultOptions: {\n          queries: {\n            staleTime: 60 * 1000,\n            refetchOnWindowFocus: false,\n            retry: (failureCount, error: any) => {\n              // Don't retry on 401 or 403 errors\n              if (error?.response?.status === 401 || error?.response?.status === 403) {\n                return false;\n              }\n              return failureCount < 3;\n            },\n          },\n          mutations: {\n            retry: false,\n          },\n        },\n      })\n  );\n\n  return (\n    <QueryClientProvider client={queryClient}>\n      {children}\n      <ReactQueryDevtools initialIsOpen={false} />\n    </QueryClientProvider>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AACA;;;AAJA;;;;AAMO,SAAS,cAAc,KAA2C;QAA3C,EAAE,QAAQ,EAAiC,GAA3C;;IAC5B,MAAM,CAAC,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;kCAC3B,IACE,IAAI,gLAAA,CAAA,cAAW,CAAC;gBACd,gBAAgB;oBACd,SAAS;wBACP,WAAW,KAAK;wBAChB,sBAAsB;wBACtB,KAAK;sDAAE,CAAC,cAAc;oCAEhB,iBAAmC;gCADvC,mCAAmC;gCACnC,IAAI,CAAA,kBAAA,6BAAA,kBAAA,MAAO,QAAQ,cAAf,sCAAA,gBAAiB,MAAM,MAAK,OAAO,CAAA,kBAAA,6BAAA,mBAAA,MAAO,QAAQ,cAAf,uCAAA,iBAAiB,MAAM,MAAK,KAAK;oCACtE,OAAO;gCACT;gCACA,OAAO,eAAe;4BACxB;;oBACF;oBACA,WAAW;wBACT,OAAO;oBACT;gBACF;YACF;;IAGJ,qBACE,6LAAC,yLAAA,CAAA,sBAAmB;QAAC,QAAQ;;YAC1B;0BACD,6LAAC,uLAAA,CAAA,qBAAkB;gBAAC,eAAe;;;;;;;;;;;;AAGzC;GA7BgB;KAAA", "debugId": null}}, {"offset": {"line": 78, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/backend/lc-workflow-frontend/src/lib/handleApiError.ts"], "sourcesContent": ["import toast from 'react-hot-toast';\n\nexport const handleApiError = (error: any, defaultMessage: string) => {\n  let message = defaultMessage;\n\n  const detail = error.response?.data?.detail;\n\n  if (typeof detail === 'string') {\n    message = detail;\n  } else if (Array.isArray(detail)) {\n    message = detail.map((err) => err.msg || 'An error occurred').join('\\n');\n  } else if (error.response?.data?.errors) {\n    const errors = error.response.data.errors;\n    if (Array.isArray(errors) && errors.length > 0) {\n      message = errors.map((err: any) => err.msg || 'Validation error').join('\\n');\n    }\n  } else if (error.response?.status === 422) {\n    message = 'Invalid data provided';\n  }\n\n  toast.error(message);\n};"], "names": [], "mappings": ";;;AAAA;;AAEO,MAAM,iBAAiB,CAAC,OAAY;QAG1B,sBAAA,iBAMJ,uBAAA,kBAKA;IAbX,IAAI,UAAU;IAEd,MAAM,UAAS,kBAAA,MAAM,QAAQ,cAAd,uCAAA,uBAAA,gBAAgB,IAAI,cAApB,2CAAA,qBAAsB,MAAM;IAE3C,IAAI,OAAO,WAAW,UAAU;QAC9B,UAAU;IACZ,OAAO,IAAI,MAAM,OAAO,CAAC,SAAS;QAChC,UAAU,OAAO,GAAG,CAAC,CAAC,MAAQ,IAAI,GAAG,IAAI,qBAAqB,IAAI,CAAC;IACrE,OAAO,KAAI,mBAAA,MAAM,QAAQ,cAAd,wCAAA,wBAAA,iBAAgB,IAAI,cAApB,4CAAA,sBAAsB,MAAM,EAAE;QACvC,MAAM,SAAS,MAAM,QAAQ,CAAC,IAAI,CAAC,MAAM;QACzC,IAAI,MAAM,OAAO,CAAC,WAAW,OAAO,MAAM,GAAG,GAAG;YAC9C,UAAU,OAAO,GAAG,CAAC,CAAC,MAAa,IAAI,GAAG,IAAI,oBAAoB,IAAI,CAAC;QACzE;IACF,OAAO,IAAI,EAAA,mBAAA,MAAM,QAAQ,cAAd,uCAAA,iBAAgB,MAAM,MAAK,KAAK;QACzC,UAAU;IACZ;IAEA,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;AACd", "debugId": null}}, {"offset": {"line": 110, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/backend/lc-workflow-frontend/src/lib/api.ts"], "sourcesContent": ["import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';\nimport { AuthResponse, LoginCredentials } from '@/types/models';\nimport { handleApiError } from './handleApiError';\n\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';\n\nclass ApiClient {\n  private client: AxiosInstance;\n\n  constructor() {\n    this.client = axios.create({\n      baseURL: `${API_BASE_URL}/api/v1`,\n      timeout: 30000,\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      withCredentials: true,\n    });\n\n    this.setupInterceptors();\n  }\n\n  private setupInterceptors() {\n    // Request interceptor to add auth token\n    this.client.interceptors.request.use(\n      (config) => {\n        const token = localStorage.getItem('access_token');\n        if (token) {\n          config.headers.Authorization = `Bearer ${token}`;\n        }\n        return config;\n      },\n      (error) => Promise.reject(error)\n    );\n\n    // Response interceptor to handle token refresh\n    this.client.interceptors.response.use(\n      (response) => response,\n      (error) => {\n        const originalRequestUrl = error.config.url;\n\n        if (error.response?.status === 401) {\n          // Don't redirect for /auth/me failures, as this is used for auth checks.\n          if (originalRequestUrl?.endsWith('/auth/me')) {\n            return Promise.reject(error);\n          }\n\n          // For other 401s, redirect to login.\n          if (typeof window !== 'undefined' && window.location.pathname !== '/login') {\n            window.location.href = '/login';\n          }\n          return Promise.reject(error);\n        }\n\n        // For all other errors, use the global handler.\n        if (!originalRequestUrl?.endsWith('/auth/me')) {\n            handleApiError(error, 'An unexpected error occurred');\n        }\n        return Promise.reject(error);\n      }\n    );\n  }\n\n  // Auth methods\n  async login(credentials: LoginCredentials): Promise<AuthResponse> {\n    const formData = new URLSearchParams();\n    formData.append('username', credentials.username);\n    formData.append('password', credentials.password);\n    \n    const response = await this.client.post('/auth/login', formData, {\n      headers: {\n        'Content-Type': 'application/x-www-form-urlencoded',\n      },\n    });\n    const { access_token, refresh_token, user } = response.data;\n\n    localStorage.setItem('access_token', access_token);\n    localStorage.setItem('refresh_token', refresh_token);\n    localStorage.setItem('user', JSON.stringify(user));\n\n    return response.data;\n  }\n\n  async logout() {\n    try {\n      await this.client.post('/auth/logout');\n    } catch (error) {\n      console.error('Logout error:', error);\n    }\n  }\n\n  async getCurrentUser(): Promise<any> {\n    const response = await this.client.get('/auth/me');\n    return response.data;\n  }\n\n  // Generic API methods\n  async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {\n    const response: AxiosResponse<T> = await this.client.get(url, config);\n    return response.data;\n  }\n\n  async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {\n    const response: AxiosResponse<T> = await this.client.post(url, data, config);\n    return response.data;\n  }\n\n  async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {\n    const response: AxiosResponse<T> = await this.client.put(url, data, config);\n    return response.data;\n  }\n\n  async patch<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {\n    const response: AxiosResponse<T> = await this.client.patch(url, data, config);\n    return response.data;\n  }\n\n  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {\n    const response: AxiosResponse<T> = await this.client.delete(url, config);\n    return response.data;\n  }\n\n  // File upload helper\n  async uploadFile(url: string, file: File, onProgress?: (progress: number) => void): Promise<any> {\n    const formData = new FormData();\n    formData.append('file', file);\n\n    return this.client.post(url, formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data',\n      },\n      onUploadProgress: (progressEvent) => {\n        if (onProgress && progressEvent.total) {\n          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);\n          onProgress(progress);\n        }\n      },\n    });\n  }\n}\n\n// Export singleton instance\nexport const apiClient = new ApiClient();\nexport const axiosInstance = apiClient['client'];"], "names": [], "mappings": ";;;;AAIqB;;AAJrB;AAEA;;;;AAEA,MAAM,eAAe,6DAAmC;AAExD,MAAM;IAgBI,oBAAoB;QAC1B,wCAAwC;QACxC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CAClC,CAAC;YACC,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,IAAI,OAAO;gBACT,OAAO,OAAO,CAAC,aAAa,GAAG,AAAC,UAAe,OAAN;YAC3C;YACA,OAAO;QACT,GACA,CAAC,QAAU,QAAQ,MAAM,CAAC;QAG5B,+CAA+C;QAC/C,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CACnC,CAAC,WAAa,UACd,CAAC;gBAGK;YAFJ,MAAM,qBAAqB,MAAM,MAAM,CAAC,GAAG;YAE3C,IAAI,EAAA,kBAAA,MAAM,QAAQ,cAAd,sCAAA,gBAAgB,MAAM,MAAK,KAAK;gBAClC,yEAAyE;gBACzE,IAAI,+BAAA,yCAAA,mBAAoB,QAAQ,CAAC,aAAa;oBAC5C,OAAO,QAAQ,MAAM,CAAC;gBACxB;gBAEA,qCAAqC;gBACrC,IAAI,aAAkB,eAAe,OAAO,QAAQ,CAAC,QAAQ,KAAK,UAAU;oBAC1E,OAAO,QAAQ,CAAC,IAAI,GAAG;gBACzB;gBACA,OAAO,QAAQ,MAAM,CAAC;YACxB;YAEA,gDAAgD;YAChD,IAAI,EAAC,+BAAA,yCAAA,mBAAoB,QAAQ,CAAC,cAAa;gBAC3C,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE,OAAO;YAC1B;YACA,OAAO,QAAQ,MAAM,CAAC;QACxB;IAEJ;IAEA,eAAe;IACf,MAAM,MAAM,WAA6B,EAAyB;QAChE,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,YAAY,YAAY,QAAQ;QAChD,SAAS,MAAM,CAAC,YAAY,YAAY,QAAQ;QAEhD,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,UAAU;YAC/D,SAAS;gBACP,gBAAgB;YAClB;QACF;QACA,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,IAAI,EAAE,GAAG,SAAS,IAAI;QAE3D,aAAa,OAAO,CAAC,gBAAgB;QACrC,aAAa,OAAO,CAAC,iBAAiB;QACtC,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC;QAE5C,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,SAAS;QACb,IAAI;YACF,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;QACzB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC;IACF;IAEA,MAAM,iBAA+B;QACnC,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC;QACvC,OAAO,SAAS,IAAI;IACtB;IAEA,sBAAsB;IACtB,MAAM,IAAO,GAAW,EAAE,MAA2B,EAAc;QACjE,MAAM,WAA6B,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK;QAC9D,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,KAAQ,GAAW,EAAE,IAAU,EAAE,MAA2B,EAAc;QAC9E,MAAM,WAA6B,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,MAAM;QACrE,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,IAAO,GAAW,EAAE,IAAU,EAAE,MAA2B,EAAc;QAC7E,MAAM,WAA6B,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,MAAM;QACpE,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,MAAS,GAAW,EAAE,IAAU,EAAE,MAA2B,EAAc;QAC/E,MAAM,WAA6B,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,MAAM;QACtE,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,OAAU,GAAW,EAAE,MAA2B,EAAc;QACpE,MAAM,WAA6B,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK;QACjE,OAAO,SAAS,IAAI;IACtB;IAEA,qBAAqB;IACrB,MAAM,WAAW,GAAW,EAAE,IAAU,EAAE,UAAuC,EAAgB;QAC/F,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ;QAExB,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,UAAU;YACrC,SAAS;gBACP,gBAAgB;YAClB;YACA,kBAAkB,CAAC;gBACjB,IAAI,cAAc,cAAc,KAAK,EAAE;oBACrC,MAAM,WAAW,KAAK,KAAK,CAAC,AAAC,cAAc,MAAM,GAAG,MAAO,cAAc,KAAK;oBAC9E,WAAW;gBACb;YACF;QACF;IACF;IAjIA,aAAc;QAFd,+KAAQ,UAAR,KAAA;QAGE,IAAI,CAAC,MAAM,GAAG,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;YACzB,SAAS,AAAC,GAAe,OAAb,cAAa;YACzB,SAAS;YACT,SAAS;gBACP,gBAAgB;YAClB;YACA,iBAAiB;QACnB;QAEA,IAAI,CAAC,iBAAiB;IACxB;AAuHF;AAGO,MAAM,YAAY,IAAI;AACtB,MAAM,gBAAgB,SAAS,CAAC,SAAS", "debugId": null}}, {"offset": {"line": 242, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/backend/lc-workflow-frontend/src/hooks/useAuth.ts"], "sourcesContent": ["import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';\nimport { apiClient } from '@/lib/api';\nimport { LoginCredentials, User } from '@/types/models';\nimport { useRouter } from 'next/navigation';\nimport toast from 'react-hot-toast';\nimport { handleApiError } from '@/lib/handleApiError';\n\n// Auth query keys\nexport const authKeys = {\n  all: ['auth'] as const,\n  user: () => [...authKeys.all, 'user'] as const,\n};\n\n// Auth hooks\nexport const useLogin = () => {\n  const router = useRouter();\n  const queryClient = useQueryClient();\n\n  return useMutation({\n    mutationFn: (credentials: LoginCredentials) => apiClient.login(credentials),\n    onSuccess: async (data) => {\n      // Ensure token is stored before redirecting\n      await new Promise(resolve => setTimeout(resolve, 500));\n\n      // Force refetch user data to update auth state\n      await queryClient.invalidateQueries({ queryKey: authKeys.user() });\n      await queryClient.refetchQueries({ queryKey: authKeys.user() });\n\n      toast.success('Login successful!');\n      // Add small delay to ensure auth state is updated\n      setTimeout(() => router.push('/dashboard'), 200);\n    },\n    onError: (error: any) => {\n      handleApiError(error, 'Login failed');\n    },\n  });\n};\n\nexport const useLogout = () => {\n  const router = useRouter();\n  const queryClient = useQueryClient();\n\n  return useMutation({\n    mutationFn: () => apiClient.logout(),\n    onSuccess: () => {\n      queryClient.clear();\n      router.push('/login');\n      toast.success('Logged out successfully');\n    },\n    onError: (error: any) => {\n      handleApiError(error, 'Logout failed');\n      // Still clear cache and redirect even if logout API fails\n      queryClient.clear();\n      router.push('/login');\n    },\n  });\n};\n\nexport const useCurrentUser = () => {\n  return useQuery<User>({\n    queryKey: authKeys.user(),\n    // Ensure type aligns with updated User including optional position fields\n    queryFn: () => apiClient.getCurrentUser() as Promise<User>,\n    staleTime: 5 * 60 * 1000, // 5 minutes\n    retry: 1,\n    retryDelay: 500,\n    enabled: typeof window !== 'undefined' && !!localStorage.getItem('access_token'), // Only run when token exists\n  });\n};\n\n// Utility hook to check if user is authenticated\nexport const useAuth = () => {\n  const { data: user, isLoading, error } = useCurrentUser();\n\n  return {\n    user: user || null,\n    isLoading: typeof window === 'undefined' ? false : isLoading,\n    isAuthenticated: typeof window === 'undefined' ? false : (!!user && !error),\n    error: typeof window === 'undefined' ? null : error,\n  };\n};\n\n// Hook to check user roles\nexport const useRole = () => {\n  const { user } = useAuth();\n\n  return {\n    isAdmin: user?.role === 'admin',\n    isManager: user?.role === 'manager',\n    isOfficer: user?.role === 'officer',\n    role: user?.role,\n  };\n};"], "names": [], "mappings": ";;;;;;;;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AACA;;;;;;;AAGO,MAAM,WAAW;IACtB,KAAK;QAAC;KAAO;IACb,MAAM,IAAM;eAAI,SAAS,GAAG;YAAE;SAAO;AACvC;AAGO,MAAM,WAAW;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;oCAAE,CAAC,cAAkC,oHAAA,CAAA,YAAS,CAAC,KAAK,CAAC;;QAC/D,SAAS;oCAAE,OAAO;gBAChB,4CAA4C;gBAC5C,MAAM,IAAI;4CAAQ,CAAA,UAAW,WAAW,SAAS;;gBAEjD,+CAA+C;gBAC/C,MAAM,YAAY,iBAAiB,CAAC;oBAAE,UAAU,SAAS,IAAI;gBAAG;gBAChE,MAAM,YAAY,cAAc,CAAC;oBAAE,UAAU,SAAS,IAAI;gBAAG;gBAE7D,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;gBACd,kDAAkD;gBAClD;4CAAW,IAAM,OAAO,IAAI,CAAC;2CAAe;YAC9C;;QACA,OAAO;oCAAE,CAAC;gBACR,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE,OAAO;YACxB;;IACF;AACF;GAtBa;;QACI,qIAAA,CAAA,YAAS;QACJ,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAoBb,MAAM,YAAY;;IACvB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;qCAAE,IAAM,oHAAA,CAAA,YAAS,CAAC,MAAM;;QAClC,SAAS;qCAAE;gBACT,YAAY,KAAK;gBACjB,OAAO,IAAI,CAAC;gBACZ,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;YAChB;;QACA,OAAO;qCAAE,CAAC;gBACR,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE,OAAO;gBACtB,0DAA0D;gBAC1D,YAAY,KAAK;gBACjB,OAAO,IAAI,CAAC;YACd;;IACF;AACF;IAlBa;;QACI,qIAAA,CAAA,YAAS;QACJ,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAgBb,MAAM,iBAAiB;;IAC5B,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAQ;QACpB,UAAU,SAAS,IAAI;QACvB,0EAA0E;QAC1E,OAAO;uCAAE,IAAM,oHAAA,CAAA,YAAS,CAAC,cAAc;;QACvC,WAAW,IAAI,KAAK;QACpB,OAAO;QACP,YAAY;QACZ,SAAS,aAAkB,eAAe,CAAC,CAAC,aAAa,OAAO,CAAC;IACnE;AACF;IAVa;;QACJ,8KAAA,CAAA,WAAQ;;;AAYV,MAAM,UAAU;;IACrB,MAAM,EAAE,MAAM,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG;IAEzC,OAAO;QACL,MAAM,QAAQ;QACd,WAAW,sCAAgC,0BAAQ;QACnD,iBAAiB,sCAAgC,0BAAS,CAAC,CAAC,QAAQ,CAAC;QACrE,OAAO,sCAAgC,0BAAO;IAChD;AACF;IATa;;QAC8B;;;AAWpC,MAAM,UAAU;;IACrB,MAAM,EAAE,IAAI,EAAE,GAAG;IAEjB,OAAO;QACL,SAAS,CAAA,iBAAA,2BAAA,KAAM,IAAI,MAAK;QACxB,WAAW,CAAA,iBAAA,2BAAA,KAAM,IAAI,MAAK;QAC1B,WAAW,CAAA,iBAAA,2BAAA,KAAM,IAAI,MAAK;QAC1B,IAAI,EAAE,iBAAA,2BAAA,KAAM,IAAI;IAClB;AACF;IATa;;QACM", "debugId": null}}, {"offset": {"line": 404, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/backend/lc-workflow-frontend/src/providers/AuthProvider.tsx"], "sourcesContent": ["'use client';\n\nimport { createContext, useContext, ReactNode } from 'react';\nimport { useAuth, useRole } from '@/hooks/useAuth';\nimport { User } from '@/types/models';\n\ninterface AuthContextType {\n  user: User | null;\n  isLoading: boolean;\n  isAuthenticated: boolean;\n  error: string | null;\n  isAdmin: boolean;\n  isManager: boolean;\n  isOfficer: boolean;\n  role: string | undefined;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\nexport function AuthProvider({ children }: { children: ReactNode }) {\n  const { user, isLoading, isAuthenticated, error } = useAuth();\n  const { isAdmin, isManager, isOfficer, role } = useRole();\n\n  return (\n    <AuthContext.Provider\n      value={{\n        user,\n        isLoading,\n        isAuthenticated,\n        error: error ? (typeof error === 'string' ? error : JSON.stringify(error)) : null,\n        isAd<PERSON>,\n        isManager,\n        isOfficer,\n        role,\n      }}\n    >\n      {children}\n    </AuthContext.Provider>\n  );\n}\n\nexport const useAuthContext = () => {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuthContext must be used within an AuthProvider');\n  }\n  return context;\n};"], "names": [], "mappings": ";;;;;AAEA;AACA;;;AAHA;;;AAiBA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,KAAqC;QAArC,EAAE,QAAQ,EAA2B,GAArC;;IAC3B,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,eAAe,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD;IAC1D,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD;IAEtD,qBACE,6LAAC,YAAY,QAAQ;QACnB,OAAO;YACL;YACA;YACA;YACA,OAAO,QAAS,OAAO,UAAU,WAAW,QAAQ,KAAK,SAAS,CAAC,SAAU;YAC7E;YACA;YACA;YACA;QACF;kBAEC;;;;;;AAGP;GApBgB;;QACsC,0HAAA,CAAA,UAAO;QACX,0HAAA,CAAA,UAAO;;;KAFzC;AAsBT,MAAM,iBAAiB;;IAC5B,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANa", "debugId": null}}, {"offset": {"line": 467, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/backend/lc-workflow-frontend/src/providers/ThemeProvider.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { createContext, useContext, useEffect, useState } from 'react';\r\n\r\ntype Theme = 'light' | 'dark' | 'system';\r\n\r\ntype ThemeProviderProps = {\r\n  children: React.ReactNode;\r\n  defaultTheme?: Theme;\r\n  storageKey?: string;\r\n};\r\n\r\ntype ThemeProviderState = {\r\n  theme: Theme;\r\n  setTheme: (theme: Theme) => void;\r\n};\r\n\r\nconst initialState: ThemeProviderState = {\r\n  theme: 'system',\r\n  setTheme: () => null,\r\n};\r\n\r\nconst ThemeProviderContext = createContext<ThemeProviderState>(initialState);\r\n\r\nexport function ThemeProvider({\r\n  children,\r\n  defaultTheme = 'system',\r\n  storageKey = 'theme',\r\n  ...props\r\n}: ThemeProviderProps) {\r\n  const [theme, setTheme] = useState<Theme>(defaultTheme);\r\n  \r\n  // Initialize theme from localStorage once the component is mounted\r\n  useEffect(() => {\r\n    try {\r\n      const storedTheme = localStorage.getItem(storageKey) as Theme;\r\n      if (storedTheme) {\r\n        setTheme(storedTheme);\r\n      }\r\n    } catch (error) {\r\n      // localStorage is not available during server-side rendering\r\n      console.error('localStorage is not available:', error);\r\n    }\r\n  }, [storageKey]);\r\n\r\n  useEffect(() => {\r\n    try {\r\n      const root = window.document.documentElement;\r\n\r\n      root.classList.remove('light', 'dark');\r\n\r\n      if (theme === 'system') {\r\n        const systemTheme = window.matchMedia('(prefers-color-scheme: dark)')\r\n          .matches\r\n          ? 'dark'\r\n          : 'light';\r\n\r\n        root.classList.add(systemTheme);\r\n        return;\r\n      }\r\n\r\n      root.classList.add(theme);\r\n    } catch (error) {\r\n      // window is not available during server-side rendering\r\n      console.error('window is not available:', error);\r\n    }\r\n  }, [theme]);\r\n\r\n  const value = {\r\n    theme,\r\n    setTheme: (theme: Theme) => {\r\n      try {\r\n        localStorage.setItem(storageKey, theme);\r\n      } catch (error) {\r\n        // localStorage is not available during server-side rendering\r\n        console.error('localStorage is not available:', error);\r\n      }\r\n      setTheme(theme);\r\n    },\r\n  };\r\n\r\n  return (\r\n    <ThemeProviderContext.Provider {...props} value={value}>\r\n      {children}\r\n    </ThemeProviderContext.Provider>\r\n  );\r\n}\r\n\r\nexport const useTheme = () => {\r\n  const context = useContext(ThemeProviderContext);\r\n\r\n  if (context === undefined)\r\n    throw new Error('useTheme must be used within a ThemeProvider');\r\n\r\n  return context;\r\n};"], "names": [], "mappings": ";;;;;AAEA;;;AAFA;;AAiBA,MAAM,eAAmC;IACvC,OAAO;IACP,UAAU,IAAM;AAClB;AAEA,MAAM,qCAAuB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAsB;AAExD,SAAS,cAAc,KAKT;QALS,EAC5B,QAAQ,EACR,eAAe,QAAQ,EACvB,aAAa,OAAO,EACpB,GAAG,OACgB,GALS;;IAM5B,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS;IAE1C,mEAAmE;IACnE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI;gBACF,MAAM,cAAc,aAAa,OAAO,CAAC;gBACzC,IAAI,aAAa;oBACf,SAAS;gBACX;YACF,EAAE,OAAO,OAAO;gBACd,6DAA6D;gBAC7D,QAAQ,KAAK,CAAC,kCAAkC;YAClD;QACF;kCAAG;QAAC;KAAW;IAEf,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI;gBACF,MAAM,OAAO,OAAO,QAAQ,CAAC,eAAe;gBAE5C,KAAK,SAAS,CAAC,MAAM,CAAC,SAAS;gBAE/B,IAAI,UAAU,UAAU;oBACtB,MAAM,cAAc,OAAO,UAAU,CAAC,gCACnC,OAAO,GACN,SACA;oBAEJ,KAAK,SAAS,CAAC,GAAG,CAAC;oBACnB;gBACF;gBAEA,KAAK,SAAS,CAAC,GAAG,CAAC;YACrB,EAAE,OAAO,OAAO;gBACd,uDAAuD;gBACvD,QAAQ,KAAK,CAAC,4BAA4B;YAC5C;QACF;kCAAG;QAAC;KAAM;IAEV,MAAM,QAAQ;QACZ;QACA,UAAU,CAAC;YACT,IAAI;gBACF,aAAa,OAAO,CAAC,YAAY;YACnC,EAAE,OAAO,OAAO;gBACd,6DAA6D;gBAC7D,QAAQ,KAAK,CAAC,kCAAkC;YAClD;YACA,SAAS;QACX;IACF;IAEA,qBACE,6LAAC,qBAAqB,QAAQ;QAAE,GAAG,KAAK;QAAE,OAAO;kBAC9C;;;;;;AAGP;GA9DgB;KAAA;AAgET,MAAM,WAAW;;IACtB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAE3B,IAAI,YAAY,WACd,MAAM,IAAI,MAAM;IAElB,OAAO;AACT;IAPa", "debugId": null}}, {"offset": {"line": 563, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/backend/lc-workflow-frontend/src/components/ToasterClient.tsx"], "sourcesContent": ["'use client';\n\nimport { Toaster } from 'react-hot-toast';\nimport { useEffect, useState } from 'react';\n\nexport function ToasterClient() {\n  const [mounted, setMounted] = useState(false);\n\n  useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  if (!mounted) {\n    return null;\n  }\n\n  return (\n    <Toaster\n      position=\"top-right\"\n      toastOptions={{\n        duration: 4000,\n        style: {\n          background: '#363636',\n          color: '#fff',\n        },\n      }}\n    />\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKO,SAAS;;IACd,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,WAAW;QACb;kCAAG,EAAE;IAEL,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,qBACE,6LAAC,0JAAA,CAAA,UAAO;QACN,UAAS;QACT,cAAc;YACZ,UAAU;YACV,OAAO;gBACL,YAAY;gBACZ,OAAO;YACT;QACF;;;;;;AAGN;GAvBgB;KAAA", "debugId": null}}]}