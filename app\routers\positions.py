from typing import List, Optional
from uuid import UUID
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy import select, func, update as sql_update, delete as sql_delete
from sqlalchemy.exc import IntegrityError
from sqlalchemy.ext.asyncio import AsyncSession
from app import models, schemas
from app.database import get_db

router = APIRouter(
    prefix="/positions",
    tags=["positions"],
)

@router.post("/", response_model=schemas.Position, status_code=status.HTTP_201_CREATED)
async def create_position(
    position: schemas.PositionCreate,
    db: AsyncSession = Depends(get_db),
):
    db_position = models.Position(**position.model_dump())
    db.add(db_position)
    try:
        await db.commit()
    except IntegrityError as e:
        await db.rollback()
        # Unique constraint on name -> conflict
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail="Position with this name already exists",
        )
    await db.refresh(db_position)
    return db_position

@router.get("/", response_model=List[schemas.Position])
async def read_positions(
    skip: int = 0,
    limit: int = 100,
    search: Optional[str] = Query(default=None),
    is_active: Optional[bool] = Query(default=None),
    db: AsyncSession = Depends(get_db),
):
    stmt = select(models.Position)
    # Filtering
    if search:
        stmt = stmt.where(models.Position.name.ilike(f"%{search}%"))
    if is_active is not None:
        stmt = stmt.where(models.Position.is_active == is_active)
    stmt = stmt.offset(skip).limit(limit)
    result = await db.execute(stmt)
    positions = result.scalars().all()
    return positions

@router.get("/{position_id}", response_model=schemas.Position)
async def read_position(position_id: UUID, db: AsyncSession = Depends(get_db)):
    result = await db.execute(
        select(models.Position).where(models.Position.id == position_id)
    )
    position = result.scalar_one_or_none()
    if position is None:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Position not found")
    return position

@router.put("/{position_id}", response_model=schemas.Position)
async def update_position(
    position_id: UUID,
    position: schemas.PositionUpdate,
    db: AsyncSession = Depends(get_db),
):
    # Fetch existing
    result = await db.execute(
        select(models.Position).where(models.Position.id == position_id)
    )
    db_position = result.scalar_one_or_none()
    if db_position is None:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Position not found")

    for key, value in position.model_dump(exclude_unset=True).items():
        setattr(db_position, key, value)

    try:
        await db.commit()
    except IntegrityError:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail="Position with this name already exists",
        )
    await db.refresh(db_position)
    return db_position

@router.delete("/{position_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_position(position_id: UUID, db: AsyncSession = Depends(get_db)):
    result = await db.execute(
        select(models.Position).where(models.Position.id == position_id)
    )
    db_position = result.scalar_one_or_none()
    if db_position is None:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Position not found")
    await db.delete(db_position)
    try:
        await db.commit()
    except IntegrityError:
        await db.rollback()
        # Likely FK constraint from users.position_id
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot delete position because it is referenced by other records",
        )
    return