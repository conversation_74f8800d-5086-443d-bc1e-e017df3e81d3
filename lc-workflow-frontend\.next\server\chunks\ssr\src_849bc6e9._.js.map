{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/backend/lc-workflow-frontend/src/components/layout/Sidebar.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport {\n  HomeIcon,\n  DocumentTextIcon,\n  UsersIcon,\n  BuildingOfficeIcon,\n  MapPinIcon,\n  Cog6ToothIcon,\n  FolderIcon,\n  ChevronDownIcon,\n  ChevronRightIcon,\n  XMarkIcon,\n  Bars3Icon,\n  BriefcaseIcon,\n} from '@heroicons/react/24/outline';\nimport { useAuthContext } from '@/providers/AuthProvider';\n\ninterface NavItem {\n  name: string;\n  href: string;\n  icon: React.ComponentType<{ className?: string }>;\n  requiredRoles?: string[];\n}\n\nconst navigation: NavItem[] = [\n  { name: 'Dashboard', href: '/dashboard', icon: HomeIcon },\n  { name: 'Applications', href: '/applications', icon: DocumentTextIcon },\n  { name: 'Files', href: '/files', icon: FolderIcon },\n];\n\nconst adminNavigation: NavItem[] = [\n  { name: 'Departments', href: '/departments', icon: BuildingOfficeIcon, requiredRoles: ['admin', 'manager'] },\n  { name: 'Positions', href: '/positions', icon: BriefcaseIcon, requiredRoles: ['admin', 'manager'] },\n  { name: 'Users', href: '/users', icon: UsersIcon, requiredRoles: ['admin', 'manager'] },\n  { name: 'Branches', href: '/branches', icon: MapPinIcon, requiredRoles: ['admin', 'manager'] },\n  { name: 'Settings', href: '/settings', icon: Cog6ToothIcon, requiredRoles: ['admin'] },\n];\n\ninterface SidebarProps {\n  isOpen: boolean;\n  onClose: () => void;\n}\n\nexport function Sidebar({ isOpen, onClose }: SidebarProps) {\n  const pathname = usePathname();\n  const { user, isAdmin, isManager } = useAuthContext();\n\n  const hasRequiredRole = (requiredRoles?: string[]) => {\n    if (!requiredRoles) return true;\n    return isAdmin || (isManager && requiredRoles.includes('manager'));\n  };\n\n  const allNavigation = [...navigation, ...adminNavigation];\n\n  const NavLink = ({ item }: { item: NavItem }) => {\n    const isActive = pathname === item.href;\n\n    if (!hasRequiredRole(item.requiredRoles)) return null;\n\n    return (\n      <Link\n        href={item.href}\n        className={`group flex items-center gap-x-3 rounded-md p-2 text-sm font-semibold leading-6 transition-all duration-200 ${isActive\n            ? 'bg-blue-600 text-white'\n            : 'text-gray-700 hover:bg-gray-50 hover:text-blue-600'\n          }`}\n        onClick={onClose}\n      >\n        <item.icon className=\"h-5 w-5 shrink-0\" />\n        {item.name}\n      </Link>\n    );\n  };\n\n  return (\n    <>\n      {/* Mobile sidebar */}\n      <div\n        className={`fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:hidden ${isOpen ? 'translate-x-0' : '-translate-x-full'\n          }`}\n      >\n        <div className=\"flex h-16 items-center justify-between px-4\">\n          <div className=\"text-xl font-bold text-gray-900\">LC Workflow</div>\n          <button\n            type=\"button\"\n            className=\"-m-2.5 p-2.5 text-gray-700\"\n            onClick={onClose}\n          >\n            <XMarkIcon className=\"h-6 w-6\" />\n          </button>\n        </div>\n        <nav className=\"flex flex-1 flex-col gap-y-2 p-4\">\n          {allNavigation.map((item) => (\n            <NavLink key={item.name} item={item} />\n          ))}\n        </nav>\n      </div>\n\n      {/* Desktop sidebar */}\n      <div className=\"hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-64 lg:flex-col\">\n        <div className=\"flex grow flex-col gap-y-5 overflow-y-auto bg-white px-6 ring-1 ring-gray-200\">\n          <div className=\"flex h-16 shrink-0 items-center\">\n            <div className=\"text-xl font-bold text-gray-900\">LC Workflow</div>\n          </div>\n          <nav className=\"flex flex-1 flex-col gap-y-2\">\n            {allNavigation.map((item) => (\n              <NavLink key={item.name} item={item} />\n            ))}\n          </nav>\n\n          {/* User info */}\n          {user && (\n            <div className=\"mb-4\">\n              <div className=\"flex items-center gap-x-3 rounded-md p-2 text-sm font-medium text-gray-700\">\n                <div className=\"h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center\">\n                  <span className=\"text-sm font-medium text-gray-600\">\n                    {user.first_name.charAt(0)}{user.last_name.charAt(0)}\n                  </span>\n                </div>\n                <div>\n                  <div className=\"font-medium\">{user.first_name} {user.last_name}</div>\n                  <div className=\"text-xs text-gray-500\">{user.role}</div>\n                </div>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </>\n  );\n}"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;AAnBA;;;;;;AA4BA,MAAM,aAAwB;IAC5B;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,+MAAA,CAAA,WAAQ;IAAC;IACxD;QAAE,MAAM;QAAgB,MAAM;QAAiB,MAAM,+NAAA,CAAA,mBAAgB;IAAC;IACtE;QAAE,MAAM;QAAS,MAAM;QAAU,MAAM,mNAAA,CAAA,aAAU;IAAC;CACnD;AAED,MAAM,kBAA6B;IACjC;QAAE,MAAM;QAAe,MAAM;QAAgB,MAAM,mOAAA,CAAA,qBAAkB;QAAE,eAAe;YAAC;YAAS;SAAU;IAAC;IAC3G;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,yNAAA,CAAA,gBAAa;QAAE,eAAe;YAAC;YAAS;SAAU;IAAC;IAClG;QAAE,MAAM;QAAS,MAAM;QAAU,MAAM,iNAAA,CAAA,YAAS;QAAE,eAAe;YAAC;YAAS;SAAU;IAAC;IACtF;QAAE,MAAM;QAAY,MAAM;QAAa,MAAM,mNAAA,CAAA,aAAU;QAAE,eAAe;YAAC;YAAS;SAAU;IAAC;IAC7F;QAAE,MAAM;QAAY,MAAM;QAAa,MAAM,yNAAA,CAAA,gBAAa;QAAE,eAAe;YAAC;SAAQ;IAAC;CACtF;AAOM,SAAS,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAgB;IACvD,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IAElD,MAAM,kBAAkB,CAAC;QACvB,IAAI,CAAC,eAAe,OAAO;QAC3B,OAAO,WAAY,aAAa,cAAc,QAAQ,CAAC;IACzD;IAEA,MAAM,gBAAgB;WAAI;WAAe;KAAgB;IAEzD,MAAM,UAAU,CAAC,EAAE,IAAI,EAAqB;QAC1C,MAAM,WAAW,aAAa,KAAK,IAAI;QAEvC,IAAI,CAAC,gBAAgB,KAAK,aAAa,GAAG,OAAO;QAEjD,qBACE,8OAAC,4JAAA,CAAA,UAAI;YACH,MAAM,KAAK,IAAI;YACf,WAAW,CAAC,2GAA2G,EAAE,WACnH,2BACA,sDACF;YACJ,SAAS;;8BAET,8OAAC,KAAK,IAAI;oBAAC,WAAU;;;;;;gBACpB,KAAK,IAAI;;;;;;;IAGhB;IAEA,qBACE;;0BAEE,8OAAC;gBACC,WAAW,CAAC,sHAAsH,EAAE,SAAS,kBAAkB,qBAC3J;;kCAEJ,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAkC;;;;;;0CACjD,8OAAC;gCACC,MAAK;gCACL,WAAU;gCACV,SAAS;0CAET,cAAA,8OAAC,iNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAGzB,8OAAC;wBAAI,WAAU;kCACZ,cAAc,GAAG,CAAC,CAAC,qBAClB,8OAAC;gCAAwB,MAAM;+BAAjB,KAAK,IAAI;;;;;;;;;;;;;;;;0BAM7B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CAAkC;;;;;;;;;;;sCAEnD,8OAAC;4BAAI,WAAU;sCACZ,cAAc,GAAG,CAAC,CAAC,qBAClB,8OAAC;oCAAwB,MAAM;mCAAjB,KAAK,IAAI;;;;;;;;;;wBAK1B,sBACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;;gDACb,KAAK,UAAU,CAAC,MAAM,CAAC;gDAAI,KAAK,SAAS,CAAC,MAAM,CAAC;;;;;;;;;;;;kDAGtD,8OAAC;;0DACC,8OAAC;gDAAI,WAAU;;oDAAe,KAAK,UAAU;oDAAC;oDAAE,KAAK,SAAS;;;;;;;0DAC9D,8OAAC;gDAAI,WAAU;0DAAyB,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASnE", "debugId": null}}, {"offset": {"line": 296, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/backend/lc-workflow-frontend/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useRef, useEffect } from 'react';\r\nimport { Bars3Icon, BellIcon, UserCircleIcon, ArrowRightOnRectangleIcon, Cog6ToothIcon } from '@heroicons/react/24/outline';\r\nimport { useLogout } from '@/hooks/useAuth';\r\nimport { useAuthContext } from '@/providers/AuthProvider';\r\nimport Link from 'next/link';\r\n\r\ninterface HeaderProps {\r\n  onMenuClick: () => void;\r\n}\r\n\r\nexport function Header({ onMenuClick }: HeaderProps) {\r\n  const logout = useLogout();\r\n  const { user } = useAuthContext();\r\n  const [profileDropdownOpen, setProfileDropdownOpen] = useState(false);\r\n  const dropdownRef = useRef<HTMLDivElement>(null);\r\n\r\n  // Close dropdown when clicking outside\r\n  useEffect(() => {\r\n    function handleClickOutside(event: MouseEvent) {\r\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\r\n        setProfileDropdownOpen(false);\r\n      }\r\n    }\r\n\r\n    document.addEventListener('mousedown', handleClickOutside);\r\n    return () => {\r\n      document.removeEventListener('mousedown', handleClickOutside);\r\n    };\r\n  }, []);\r\n\r\n  return (\r\n    <div className=\"sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 bg-white px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8\">\r\n      <button\r\n        type=\"button\"\r\n        className=\"-m-2.5 p-2.5 text-gray-700 lg:hidden\"\r\n        onClick={onMenuClick}\r\n      >\r\n        <span className=\"sr-only\">Open sidebar</span>\r\n        <Bars3Icon className=\"h-6 w-6\" aria-hidden=\"true\" />\r\n      </button>\r\n\r\n      {/* Separator */}\r\n      <div className=\"h-6 w-px bg-gray-900/10 lg:hidden\" aria-hidden=\"true\" />\r\n\r\n      <div className=\"flex flex-1 gap-x-4 self-stretch lg:gap-x-6\">\r\n        <div className=\"flex flex-1\" />\r\n\r\n        <div className=\"flex items-center gap-x-4 lg:gap-x-6\">\r\n          <button\r\n            type=\"button\"\r\n            className=\"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500\"\r\n          >\r\n            <span className=\"sr-only\">View notifications</span>\r\n            <BellIcon className=\"h-6 w-6\" aria-hidden=\"true\" />\r\n          </button>\r\n\r\n          {/* Separator */}\r\n          <div className=\"hidden lg:block lg:h-6 lg:w-px lg:bg-gray-900/10\" aria-hidden=\"true\" />\r\n\r\n          {/* Profile dropdown */}\r\n          <div className=\"relative\">\r\n            <button\r\n              type=\"button\"\r\n              className=\"-m-1.5 flex items-center p-1.5\"\r\n              id=\"user-menu-button\"\r\n              aria-expanded=\"false\"\r\n              aria-haspopup=\"true\"\r\n              onClick={() => setProfileDropdownOpen(!profileDropdownOpen)}\r\n            >\r\n              <span className=\"sr-only\">Open user menu</span>\r\n              <div className=\"h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center\">\r\n                <span className=\"text-sm font-medium text-gray-600\">\r\n                  {user?.first_name?.charAt(0)}{user?.last_name?.charAt(0)}\r\n                </span>\r\n              </div>\r\n              <span className=\"hidden lg:flex lg:items-center\">\r\n                <span className=\"ml-4 text-sm leading-6 text-gray-900\" aria-hidden=\"true\">\r\n                  <span className=\"font-semibold\">{user?.first_name} {user?.last_name}</span>\r\n                  {user?.position?.title ? (\r\n                    <span className=\"ml-2 text-gray-500 truncate max-w-[10rem]\">\r\n                      • {user?.position?.title}\r\n                    </span>\r\n                  ) : null}\r\n                </span>\r\n              </span>\r\n            </button>\r\n\r\n            {/* Dropdown menu */}\r\n            {profileDropdownOpen && (\r\n              <div\r\n                className=\"absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none\"\r\n                role=\"menu\"\r\n                aria-orientation=\"vertical\"\r\n                aria-labelledby=\"user-menu-button\"\r\n                ref={dropdownRef}\r\n              >\r\n                <div className=\"px-4 py-3\">\r\n                  <p className=\"text-sm\">Signed in as</p>\r\n                  <p className=\"truncate text-sm font-medium text-gray-900\">{user?.email}</p>\r\n                </div>\r\n                <div className=\"border-t border-gray-100\"></div>\r\n                <Link\r\n                  href=\"/profile\"\r\n                  className=\"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\r\n                  role=\"menuitem\"\r\n                  onClick={() => setProfileDropdownOpen(false)}\r\n                >\r\n                  <UserCircleIcon className=\"mr-3 h-5 w-5 text-gray-400\" aria-hidden=\"true\" />\r\n                  Your Profile\r\n                </Link>\r\n                <Link\r\n                  href=\"/settings\"\r\n                  className=\"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\r\n                  role=\"menuitem\"\r\n                  onClick={() => setProfileDropdownOpen(false)}\r\n                >\r\n                  <Cog6ToothIcon className=\"mr-3 h-5 w-5 text-gray-400\" aria-hidden=\"true\" />\r\n                  Settings\r\n                </Link>\r\n                <div className=\"border-t border-gray-100\"></div>\r\n                <button\r\n                  type=\"button\"\r\n                  className=\"flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\r\n                  role=\"menuitem\"\r\n                  onClick={() => {\r\n                    setProfileDropdownOpen(false);\r\n                    logout.mutate();\r\n                  }}\r\n                >\r\n                  <ArrowRightOnRectangleIcon className=\"mr-3 h-5 w-5 text-gray-400\" aria-hidden=\"true\" />\r\n                  Sign out\r\n                </button>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AANA;;;;;;;AAYO,SAAS,OAAO,EAAE,WAAW,EAAe;IACjD,MAAM,SAAS,CAAA,GAAA,uHAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IAC9B,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE3C,uCAAuC;IACvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS,mBAAmB,KAAiB;YAC3C,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;gBAC9E,uBAAuB;YACzB;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;QAC5C;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBACC,MAAK;gBACL,WAAU;gBACV,SAAS;;kCAET,8OAAC;wBAAK,WAAU;kCAAU;;;;;;kCAC1B,8OAAC,iNAAA,CAAA,YAAS;wBAAC,WAAU;wBAAU,eAAY;;;;;;;;;;;;0BAI7C,8OAAC;gBAAI,WAAU;gBAAoC,eAAY;;;;;;0BAE/D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCAEf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,MAAK;gCACL,WAAU;;kDAEV,8OAAC;wCAAK,WAAU;kDAAU;;;;;;kDAC1B,8OAAC,+MAAA,CAAA,WAAQ;wCAAC,WAAU;wCAAU,eAAY;;;;;;;;;;;;0CAI5C,8OAAC;gCAAI,WAAU;gCAAmD,eAAY;;;;;;0CAG9E,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,WAAU;wCACV,IAAG;wCACH,iBAAc;wCACd,iBAAc;wCACd,SAAS,IAAM,uBAAuB,CAAC;;0DAEvC,8OAAC;gDAAK,WAAU;0DAAU;;;;;;0DAC1B,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;;wDACb,MAAM,YAAY,OAAO;wDAAI,MAAM,WAAW,OAAO;;;;;;;;;;;;0DAG1D,8OAAC;gDAAK,WAAU;0DACd,cAAA,8OAAC;oDAAK,WAAU;oDAAuC,eAAY;;sEACjE,8OAAC;4DAAK,WAAU;;gEAAiB,MAAM;gEAAW;gEAAE,MAAM;;;;;;;wDACzD,MAAM,UAAU,sBACf,8OAAC;4DAAK,WAAU;;gEAA4C;gEACvD,MAAM,UAAU;;;;;;mEAEnB;;;;;;;;;;;;;;;;;;oCAMT,qCACC,8OAAC;wCACC,WAAU;wCACV,MAAK;wCACL,oBAAiB;wCACjB,mBAAgB;wCAChB,KAAK;;0DAEL,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAU;;;;;;kEACvB,8OAAC;wDAAE,WAAU;kEAA8C,MAAM;;;;;;;;;;;;0DAEnE,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,MAAK;gDACL,SAAS,IAAM,uBAAuB;;kEAEtC,8OAAC,2NAAA,CAAA,iBAAc;wDAAC,WAAU;wDAA6B,eAAY;;;;;;oDAAS;;;;;;;0DAG9E,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,MAAK;gDACL,SAAS,IAAM,uBAAuB;;kEAEtC,8OAAC,yNAAA,CAAA,gBAAa;wDAAC,WAAU;wDAA6B,eAAY;;;;;;oDAAS;;;;;;;0DAG7E,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDACC,MAAK;gDACL,WAAU;gDACV,MAAK;gDACL,SAAS;oDACP,uBAAuB;oDACvB,OAAO,MAAM;gDACf;;kEAEA,8OAAC,iPAAA,CAAA,4BAAyB;wDAAC,WAAU;wDAA6B,eAAY;;;;;;oDAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUzG", "debugId": null}}, {"offset": {"line": 648, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/backend/lc-workflow-frontend/src/components/layout/Layout.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Sidebar } from './Sidebar';\nimport { Header } from './Header';\n\ninterface LayoutProps {\n  children: React.ReactNode;\n}\n\nexport function Layout({ children }: LayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n\n  return (\n    <>\n      <div className=\"min-h-full\">\n        <Sidebar \n          isOpen={sidebarOpen} \n          onClose={() => setSidebarOpen(false)} \n        />\n        \n        <div className=\"lg:pl-64\">\n          <Header onMenuClick={() => setSidebarOpen(true)} />\n          \n          <main className=\"py-10\">\n            <div className=\"px-4 sm:px-6 lg:px-8\">\n              {children}\n            </div>\n          </main>\n        </div>\n      </div>\n    </>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAUO,SAAS,OAAO,EAAE,QAAQ,EAAe;IAC9C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,qBACE;kBACE,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,uIAAA,CAAA,UAAO;oBACN,QAAQ;oBACR,SAAS,IAAM,eAAe;;;;;;8BAGhC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,sIAAA,CAAA,SAAM;4BAAC,aAAa,IAAM,eAAe;;;;;;sCAE1C,8OAAC;4BAAK,WAAU;sCACd,cAAA,8OAAC;gCAAI,WAAU;0CACZ;;;;;;;;;;;;;;;;;;;;;;;;AAOf", "debugId": null}}, {"offset": {"line": 717, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/backend/lc-workflow-frontend/src/components/auth/ProtectedRoute.tsx"], "sourcesContent": ["'use client';\n\nimport { useRouter } from 'next/navigation';\nimport { useAuthContext } from '@/providers/AuthProvider';\nimport { useEffect } from 'react';\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode;\n  requiredRoles?: string[];\n}\n\nexport function ProtectedRoute({ children, requiredRoles }: ProtectedRouteProps) {\n  const router = useRouter();\n  const { isAuthenticated, isLoading, user, role } = useAuthContext();\n\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      if (!isLoading) {\n        if (!isAuthenticated) {\n          router.push('/login');\n        } else if (requiredRoles && requiredRoles.length > 0 && !requiredRoles.includes(role || '')) {\n          router.push('/unauthorized');\n        }\n      }\n    }, 1000); // Wait 1 second for auth state to stabilize\n\n    return () => clearTimeout(timer);\n  }, [isAuthenticated, isLoading, requiredRoles, role, router]);\n\n  if (isLoading || !isAuthenticated) {\n    return (\n      <div className=\"flex min-h-screen items-center justify-center\">\n        <div className=\"h-8 w-8 animate-spin rounded-full border-4 border-blue-600 border-t-transparent\"></div>\n      </div>\n    );\n  }\n\n  if (requiredRoles && requiredRoles.length > 0 && !requiredRoles.includes(role || '')) {\n    return null;\n  }\n\n  return <>{children}</>;\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAWO,SAAS,eAAe,EAAE,QAAQ,EAAE,aAAa,EAAuB;IAC7E,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IAEhE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,WAAW;YACvB,IAAI,CAAC,WAAW;gBACd,IAAI,CAAC,iBAAiB;oBACpB,OAAO,IAAI,CAAC;gBACd,OAAO,IAAI,iBAAiB,cAAc,MAAM,GAAG,KAAK,CAAC,cAAc,QAAQ,CAAC,QAAQ,KAAK;oBAC3F,OAAO,IAAI,CAAC;gBACd;YACF;QACF,GAAG,OAAO,4CAA4C;QAEtD,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;QAAiB;QAAW;QAAe;QAAM;KAAO;IAE5D,IAAI,aAAa,CAAC,iBAAiB;QACjC,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,iBAAiB,cAAc,MAAM,GAAG,KAAK,CAAC,cAAc,QAAQ,CAAC,QAAQ,KAAK;QACpF,OAAO;IACT;IAEA,qBAAO;kBAAG;;AACZ", "debugId": null}}, {"offset": {"line": 777, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/backend/lc-workflow-frontend/src/hooks/useDashboard.ts"], "sourcesContent": ["import { useQuery } from '@tanstack/react-query';\r\nimport { apiClient } from '@/lib/api';\r\n\r\n// Dashboard query keys\r\nexport const dashboardKeys = {\r\n  all: ['dashboard'] as const,\r\n  stats: () => [...dashboardKeys.all, 'stats'] as const,\r\n  recentApplications: (limit?: number) => [...dashboardKeys.all, 'recent-applications', limit] as const,\r\n  activityTimeline: (days?: number) => [...dashboardKeys.all, 'activity-timeline', days] as const,\r\n  performanceMetrics: () => [...dashboardKeys.all, 'performance-metrics'] as const,\r\n};\r\n\r\n// Dashboard statistics interface\r\nexport interface DashboardStats {\r\n  applications: {\r\n    total: number;\r\n    draft: number;\r\n    submitted: number;\r\n    pending: number;\r\n    under_review: number;\r\n    approved: number;\r\n    rejected: number;\r\n  };\r\n  users: {\r\n    total: number;\r\n    active: number;\r\n    inactive: number;\r\n    admins: number;\r\n    managers: number;\r\n    officers: number;\r\n    viewers: number;\r\n  };\r\n  departments: {\r\n    total: number;\r\n    active: number;\r\n  };\r\n  branches: {\r\n    total: number;\r\n    active: number;\r\n  };\r\n  files: {\r\n    total: number;\r\n    total_size: number;\r\n  };\r\n}\r\n\r\n// Recent application interface\r\nexport interface RecentApplication {\r\n  phone: any;\r\n  portfolio_officer_name: any;\r\n  id: string;\r\n  full_name_latin?: string;\r\n  full_name_khmer?: string;\r\n  requested_amount?: number;\r\n  status: string;\r\n  created_at: string;\r\n  user_id: string;\r\n}\r\n\r\n// Activity timeline interface\r\nexport interface ActivityItem {\r\n  id: string;\r\n  type: 'application' | 'user';\r\n  action: string;\r\n  title: string;\r\n  description: string;\r\n  status: string;\r\n  timestamp: string;\r\n  user_id: string;\r\n}\r\n\r\n// Performance metrics interface\r\nexport interface PerformanceMetrics {\r\n  applications_processed_30d: number;\r\n  average_processing_time_days: number;\r\n  approval_rate_percentage: number;\r\n  active_users_today: number;\r\n}\r\n\r\n// Dashboard hooks\r\nexport const useDashboardStats = () => {\r\n  return useQuery({\r\n    queryKey: dashboardKeys.stats(),\r\n    queryFn: (): Promise<DashboardStats> => apiClient.get('/dashboard/stats'),\r\n    staleTime: 2 * 60 * 1000, // 2 minutes\r\n    gcTime: 5 * 60 * 1000, // 5 minutes (formerly cacheTime)\r\n    refetchOnWindowFocus: true,\r\n    refetchInterval: 5 * 60 * 1000, // Refetch every 5 minutes\r\n  });\r\n};\r\n\r\nexport const useRecentApplications = (limit: number = 10) => {\r\n  return useQuery({\r\n    queryKey: dashboardKeys.recentApplications(limit),\r\n    queryFn: (): Promise<RecentApplication[]> => \r\n      apiClient.get(`/dashboard/recent-applications?limit=${limit}`),\r\n    staleTime: 1 * 60 * 1000, // 1 minute\r\n    gcTime: 3 * 60 * 1000, // 3 minutes\r\n    refetchOnWindowFocus: true,\r\n  });\r\n};\r\n\r\nexport const useActivityTimeline = (days: number = 7) => {\r\n  return useQuery({\r\n    queryKey: dashboardKeys.activityTimeline(days),\r\n    queryFn: (): Promise<ActivityItem[]> => \r\n      apiClient.get(`/dashboard/activity-timeline?days=${days}`),\r\n    staleTime: 2 * 60 * 1000, // 2 minutes\r\n    gcTime: 5 * 60 * 1000, // 5 minutes\r\n    refetchOnWindowFocus: true,\r\n  });\r\n};\r\n\r\nexport const usePerformanceMetrics = () => {\r\n  return useQuery({\r\n    queryKey: dashboardKeys.performanceMetrics(),\r\n    queryFn: (): Promise<PerformanceMetrics> => apiClient.get('/dashboard/performance-metrics'),\r\n    staleTime: 5 * 60 * 1000, // 5 minutes\r\n    gcTime: 10 * 60 * 1000, // 10 minutes\r\n    refetchOnWindowFocus: false, // Don't refetch on window focus for performance metrics\r\n    refetchInterval: 10 * 60 * 1000, // Refetch every 10 minutes\r\n  });\r\n};\r\n\r\n// Legacy hooks for backward compatibility\r\nexport const useApplicationStats = () => {\r\n  const { data: dashboardStats, ...rest } = useDashboardStats();\r\n  \r\n  return {\r\n    data: dashboardStats?.applications,\r\n    ...rest,\r\n  };\r\n};\r\n\r\nexport const useUserStats = () => {\r\n  const { data: dashboardStats, ...rest } = useDashboardStats();\r\n  \r\n  return {\r\n    data: {\r\n      total_users: dashboardStats?.users.total,\r\n      total_departments: dashboardStats?.departments.total,\r\n      total_branches: dashboardStats?.branches.total,\r\n      ...dashboardStats?.users,\r\n    },\r\n    ...rest,\r\n  };\r\n};"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;AAGO,MAAM,gBAAgB;IAC3B,KAAK;QAAC;KAAY;IAClB,OAAO,IAAM;eAAI,cAAc,GAAG;YAAE;SAAQ;IAC5C,oBAAoB,CAAC,QAAmB;eAAI,cAAc,GAAG;YAAE;YAAuB;SAAM;IAC5F,kBAAkB,CAAC,OAAkB;eAAI,cAAc,GAAG;YAAE;YAAqB;SAAK;IACtF,oBAAoB,IAAM;eAAI,cAAc,GAAG;YAAE;SAAsB;AACzE;AAsEO,MAAM,oBAAoB;IAC/B,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,cAAc,KAAK;QAC7B,SAAS,IAA+B,iHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACtD,WAAW,IAAI,KAAK;QACpB,QAAQ,IAAI,KAAK;QACjB,sBAAsB;QACtB,iBAAiB,IAAI,KAAK;IAC5B;AACF;AAEO,MAAM,wBAAwB,CAAC,QAAgB,EAAE;IACtD,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,cAAc,kBAAkB,CAAC;QAC3C,SAAS,IACP,iHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,qCAAqC,EAAE,OAAO;QAC/D,WAAW,IAAI,KAAK;QACpB,QAAQ,IAAI,KAAK;QACjB,sBAAsB;IACxB;AACF;AAEO,MAAM,sBAAsB,CAAC,OAAe,CAAC;IAClD,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,cAAc,gBAAgB,CAAC;QACzC,SAAS,IACP,iHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,kCAAkC,EAAE,MAAM;QAC3D,WAAW,IAAI,KAAK;QACpB,QAAQ,IAAI,KAAK;QACjB,sBAAsB;IACxB;AACF;AAEO,MAAM,wBAAwB;IACnC,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,cAAc,kBAAkB;QAC1C,SAAS,IAAmC,iHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QAC1D,WAAW,IAAI,KAAK;QACpB,QAAQ,KAAK,KAAK;QAClB,sBAAsB;QACtB,iBAAiB,KAAK,KAAK;IAC7B;AACF;AAGO,MAAM,sBAAsB;IACjC,MAAM,EAAE,MAAM,cAAc,EAAE,GAAG,MAAM,GAAG;IAE1C,OAAO;QACL,MAAM,gBAAgB;QACtB,GAAG,IAAI;IACT;AACF;AAEO,MAAM,eAAe;IAC1B,MAAM,EAAE,MAAM,cAAc,EAAE,GAAG,MAAM,GAAG;IAE1C,OAAO;QACL,MAAM;YACJ,aAAa,gBAAgB,MAAM;YACnC,mBAAmB,gBAAgB,YAAY;YAC/C,gBAAgB,gBAAgB,SAAS;YACzC,GAAG,gBAAgB,KAAK;QAC1B;QACA,GAAG,IAAI;IACT;AACF", "debugId": null}}, {"offset": {"line": 874, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/backend/lc-workflow-frontend/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\nexport function formatBytes(bytes: number, decimals = 2): string {\n  if (bytes === 0) return '0 Bytes';\n\n  const k = 1024;\n  const dm = decimals < 0 ? 0 : decimals;\n  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];\n\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];\n}\n\nexport function formatDate(dateString: string): string {\n  const date = new Date(dateString);\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  }).format(date);\n}\n\nexport function formatCurrency(amount: number, currency = 'USD'): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency,\n  }).format(amount);\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text;\n  return text.slice(0, maxLength) + '...';\n}\n\nexport function getFileIcon(mimeType: string): string {\n  if (mimeType.startsWith('image/')) return '🖼️';\n  if (mimeType === 'application/pdf') return '📄';\n  if (mimeType.includes('word')) return '📝';\n  if (mimeType.includes('excel') || mimeType.includes('spreadsheet')) return '📊';\n  if (mimeType.startsWith('text/')) return '📄';\n  return '📎';\n}\n\nexport function validateEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\nexport function validatePhone(phone: string): boolean {\n  const phoneRegex = /^\\+?[\\d\\s\\-\\(\\)]+$/;\n  return phoneRegex.test(phone) && phone.replace(/\\D/g, '').length >= 10;\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9);\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean;\n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args);\n      inThrottle = true;\n      setTimeout(() => (inThrottle = false), limit);\n    }\n  };\n}\n\nexport function isValidUUID(uuid: string): boolean {\n  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;\n  return uuidRegex.test(uuid);\n}\n\nexport function validateUUID(uuid: string): string {\n  if (!isValidUUID(uuid)) {\n    throw new Error('Invalid UUID format');\n  }\n  return uuid;\n}\n\nexport function sanitizeUUID(uuid: string): string {\n  return uuid.replace(/[^a-f0-9-]/gi, '');\n}"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,YAAY,KAAa,EAAE,WAAW,CAAC;IACrD,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,KAAK,WAAW,IAAI,IAAI;IAC9B,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;KAAK;IAEvE,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,OAAO,MAAM,KAAK,CAAC,EAAE;AAC1E;AAEO,SAAS,WAAW,UAAkB;IAC3C,MAAM,OAAO,IAAI,KAAK;IACtB,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,eAAe,MAAc,EAAE,WAAW,KAAK;IAC7D,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP;IACF,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,aAAa;AACpC;AAEO,SAAS,YAAY,QAAgB;IAC1C,IAAI,SAAS,UAAU,CAAC,WAAW,OAAO;IAC1C,IAAI,aAAa,mBAAmB,OAAO;IAC3C,IAAI,SAAS,QAAQ,CAAC,SAAS,OAAO;IACtC,IAAI,SAAS,QAAQ,CAAC,YAAY,SAAS,QAAQ,CAAC,gBAAgB,OAAO;IAC3E,IAAI,SAAS,UAAU,CAAC,UAAU,OAAO;IACzC,OAAO;AACT;AAEO,SAAS,cAAc,KAAa;IACzC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,cAAc,KAAa;IACzC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC,UAAU,MAAM,OAAO,CAAC,OAAO,IAAI,MAAM,IAAI;AACtE;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,SACd,IAAO,EACP,KAAa;IAEb,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW,IAAO,aAAa,OAAQ;QACzC;IACF;AACF;AAEO,SAAS,YAAY,IAAY;IACtC,MAAM,YAAY;IAClB,OAAO,UAAU,IAAI,CAAC;AACxB;AAEO,SAAS,aAAa,IAAY;IACvC,IAAI,CAAC,YAAY,OAAO;QACtB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAEO,SAAS,aAAa,IAAY;IACvC,OAAO,KAAK,OAAO,CAAC,gBAAgB;AACtC", "debugId": null}}, {"offset": {"line": 988, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/backend/lc-workflow-frontend/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client';\n\nimport { Layout } from '@/components/layout/Layout';\nimport { ProtectedRoute } from '@/components/auth/ProtectedRoute';\nimport { \n  useDashboardStats, \n  useRecentApplications, \n  useActivityTimeline, \n  usePerformanceMetrics \n} from '@/hooks/useDashboard';\nimport { useAuth } from '@/hooks/useAuth';\nimport { \n  DocumentTextIcon, \n  UsersIcon, \n  BuildingOfficeIcon, \n  MapPinIcon,\n  CheckCircleIcon,\n  ClockIcon,\n  XCircleIcon,\n  FolderIcon,\n  ArrowTrendingUpIcon,\n  CalendarIcon,\n  ChartBarIcon,\n  ArrowPathIcon,\n  CurrencyDollarIcon,\n  PhoneIcon,\n  UserIcon,\n  PlusIcon\n} from '@heroicons/react/24/outline';\nimport { formatCurrency, formatBytes, formatDate } from '@/lib/utils';\nimport Link from 'next/link';\n\nexport default function DashboardPage() {\n  const { user } = useAuth();\n  const { data: dashboardStats, isLoading: statsLoading, error: statsError, refetch: refetchStats } = useDashboardStats();\n  const { data: recentApplications, isLoading: appsLoading } = useRecentApplications(5);\n  const { data: activityTimeline, isLoading: activityLoading } = useActivityTimeline(7);\n  const { data: performanceMetrics, isLoading: metricsLoading } = usePerformanceMetrics();\n\n  const stats = [\n    {\n      name: 'Total Applications',\n      value: dashboardStats?.applications.total || 0,\n      change: '+12%',\n      changeType: 'positive',\n      icon: DocumentTextIcon,\n      color: 'text-blue-600',\n      bgColor: 'bg-blue-50',\n      href: '/applications',\n    },\n    {\n      name: 'Pending Applications',\n      value: dashboardStats?.applications.pending || 0,\n      change: '+5%',\n      changeType: 'neutral',\n      icon: ClockIcon,\n      color: 'text-yellow-600',\n      bgColor: 'bg-yellow-50',\n      href: '/applications?status=submitted',\n    },\n    {\n      name: 'Approved Applications',\n      value: dashboardStats?.applications.approved || 0,\n      change: '+8%',\n      changeType: 'positive',\n      icon: CheckCircleIcon,\n      color: 'text-green-600',\n      bgColor: 'bg-green-50',\n      href: '/applications?status=approved',\n    },\n    {\n      name: 'Rejected Applications',\n      value: dashboardStats?.applications.rejected || 0,\n      change: '-2%',\n      changeType: 'negative',\n      icon: XCircleIcon,\n      color: 'text-red-600',\n      bgColor: 'bg-red-50',\n      href: '/applications?status=rejected',\n    },\n  ];\n\n  const systemStats = [\n    {\n      name: 'Total Users',\n      value: dashboardStats?.users.total || 0,\n      icon: UsersIcon,\n      href: '/users',\n    },\n    {\n      name: 'Departments',\n      value: dashboardStats?.departments.total || 0,\n      icon: BuildingOfficeIcon,\n      href: '/departments',\n    },\n    {\n      name: 'Branches',\n      value: dashboardStats?.branches.total || 0,\n      icon: MapPinIcon,\n      href: '/branches',\n    },\n    {\n      name: 'Files',\n      value: dashboardStats?.files.total || 0,\n      icon: FolderIcon,\n      href: '/files',\n    },\n  ];\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'approved': return 'text-green-600 bg-green-50';\n      case 'rejected': return 'text-red-600 bg-red-50';\n      case 'submitted': return 'text-blue-600 bg-blue-50';\n      case 'under_review': return 'text-yellow-600 bg-yellow-50';\n      case 'draft': return 'text-gray-600 bg-gray-50';\n      default: return 'text-gray-600 bg-gray-50';\n    }\n  };\n\n  const getActivityIcon = (type: string) => {\n    switch (type) {\n      case 'application': return DocumentTextIcon;\n      case 'user': return UsersIcon;\n      default: return DocumentTextIcon;\n    }\n  };\n\n  if (statsError) {\n    return (\n      <ProtectedRoute>\n        <Layout>\n          <div className=\"flex items-center justify-center h-64\">\n            <div className=\"text-center\">\n              <h2 className=\"text-xl font-bold text-gray-900 mb-2\">Error loading dashboard</h2>\n              <p className=\"text-gray-600 mb-4\">Unable to load dashboard statistics</p>\n              <button\n                onClick={() => refetchStats()}\n                className=\"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700\"\n              >\n                <ArrowPathIcon className=\"h-4 w-4 mr-2\" />\n                Retry\n              </button>\n            </div>\n          </div>\n        </Layout>\n      </ProtectedRoute>\n    );\n  }\n\n  return (\n    <ProtectedRoute>\n      <Layout>\n        <div className=\"space-y-6\">\n          {/* Header */}\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h1 className=\"text-3xl font-bold text-gray-900\">Dashboard</h1>\n              <p className=\"text-gray-600\">\n                Welcome back, {user?.first_name}! Here's what's happening with your applications.\n              </p>\n            </div>\n            <div className=\"text-sm text-gray-500\">\n              Last updated: {new Date().toLocaleTimeString()}\n            </div>\n          </div>\n\n          {/* Main Stats Grid */}\n          <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4\">\n            {stats.map((stat) => (\n              <Link key={stat.name} href={stat.href}>\n                <div className=\"overflow-hidden rounded-lg bg-white px-4 py-5 shadow hover:shadow-md transition-shadow cursor-pointer sm:p-6\">\n                  <div className=\"flex items-center\">\n                    <div className={`flex-shrink-0 rounded-md p-3 ${stat.bgColor}`}>\n                      <stat.icon className={`h-6 w-6 ${stat.color}`} aria-hidden=\"true\" />\n                    </div>\n                    <div className=\"ml-5 w-0 flex-1\">\n                      <dl>\n                        <dt className=\"truncate text-sm font-medium text-gray-500\">{stat.name}</dt>\n                        <dd className=\"flex items-baseline\">\n                          <div className=\"text-2xl font-semibold text-gray-900\">\n                            {statsLoading ? (\n                              <div className=\"animate-pulse bg-gray-200 h-8 w-16 rounded\"></div>\n                            ) : (\n                              stat.value.toLocaleString()\n                            )}\n                          </div>\n                          {stat.change && (\n                            <div className={`ml-2 flex items-baseline text-sm font-semibold ${\n                              stat.changeType === 'positive' ? 'text-green-600' : \n                              stat.changeType === 'negative' ? 'text-red-600' : 'text-gray-600'\n                            }`}>\n                              {stat.change}\n                            </div>\n                          )}\n                        </dd>\n                      </dl>\n                    </div>\n                  </div>\n                </div>\n              </Link>\n            ))}\n          </div>\n\n          {/* Performance Metrics */}\n          {performanceMetrics && (\n            <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4\">\n              <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n                <div className=\"p-5\">\n                  <div className=\"flex items-center\">\n                    <div className=\"flex-shrink-0\">\n                      <ChartBarIcon className=\"h-6 w-6 text-gray-400\" />\n                    </div>\n                    <div className=\"ml-5 w-0 flex-1\">\n                      <dl>\n                        <dt className=\"text-sm font-medium text-gray-500 truncate\">Processed (30d)</dt>\n                        <dd className=\"text-lg font-medium text-gray-900\">\n                          {metricsLoading ? (\n                            <div className=\"animate-pulse bg-gray-200 h-6 w-12 rounded\"></div>\n                          ) : (\n                            performanceMetrics.applications_processed_30d\n                          )}\n                        </dd>\n                      </dl>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n                <div className=\"p-5\">\n                  <div className=\"flex items-center\">\n                    <div className=\"flex-shrink-0\">\n                      <ClockIcon className=\"h-6 w-6 text-gray-400\" />\n                    </div>\n                    <div className=\"ml-5 w-0 flex-1\">\n                      <dl>\n                        <dt className=\"text-sm font-medium text-gray-500 truncate\">Avg. Processing</dt>\n                        <dd className=\"text-lg font-medium text-gray-900\">\n                          {metricsLoading ? (\n                            <div className=\"animate-pulse bg-gray-200 h-6 w-16 rounded\"></div>\n                          ) : (\n                            `${performanceMetrics.average_processing_time_days} days`\n                          )}\n                        </dd>\n                      </dl>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n                <div className=\"p-5\">\n                  <div className=\"flex items-center\">\n                    <div className=\"flex-shrink-0\">\n                      <ArrowTrendingUpIcon className=\"h-6 w-6 text-gray-400\" />\n                    </div>\n                    <div className=\"ml-5 w-0 flex-1\">\n                      <dl>\n                        <dt className=\"text-sm font-medium text-gray-500 truncate\">Approval Rate</dt>\n                        <dd className=\"text-lg font-medium text-gray-900\">\n                          {metricsLoading ? (\n                            <div className=\"animate-pulse bg-gray-200 h-6 w-12 rounded\"></div>\n                          ) : (\n                            `${performanceMetrics.approval_rate_percentage}%`\n                          )}\n                        </dd>\n                      </dl>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n                <div className=\"p-5\">\n                  <div className=\"flex items-center\">\n                    <div className=\"flex-shrink-0\">\n                      <FolderIcon className=\"h-6 w-6 text-gray-400\" />\n                    </div>\n                    <div className=\"ml-5 w-0 flex-1\">\n                      <dl>\n                        <dt className=\"text-sm font-medium text-gray-500 truncate\">Storage Used</dt>\n                        <dd className=\"text-lg font-medium text-gray-900\">\n                          {statsLoading ? (\n                            <div className=\"animate-pulse bg-gray-200 h-6 w-16 rounded\"></div>\n                          ) : (\n                            formatBytes(dashboardStats?.files.total_size || 0)\n                          )}\n                        </dd>\n                      </dl>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* Content Grid */}\n          <div className=\"grid grid-cols-1 gap-6 lg:grid-cols-3\">\n            {/* Recent Applications */}\n            <div className=\"lg:col-span-2\">\n              <div className=\"bg-white shadow rounded-lg\">\n                <div className=\"px-4 py-5 sm:p-6\">\n                  <div className=\"flex items-center justify-between mb-4\">\n                    <h3 className=\"text-lg font-medium text-gray-900\">ពាក្យសុំកម្ចីថ្មីៗ</h3>\n                    <Link href=\"/applications\" className=\"text-sm text-blue-600 hover:text-blue-800\">\n                      មើលទាំងអស់\n                    </Link>\n                  </div>\n                  \n                  {appsLoading ? (\n                    <div className=\"space-y-3\">\n                      {[...Array(3)].map((_, i) => (\n                        <div key={i} className=\"animate-pulse flex items-center space-x-4\">\n                          <div className=\"rounded-full bg-gray-200 h-10 w-10\"></div>\n                          <div className=\"flex-1 space-y-2\">\n                            <div className=\"h-4 bg-gray-200 rounded w-3/4\"></div>\n                            <div className=\"h-3 bg-gray-200 rounded w-1/2\"></div>\n                          </div>\n                        </div>\n                      ))}\n                    </div>\n                  ) : recentApplications && recentApplications.length > 0 ? (\n                    <div className=\"space-y-4\">\n                      {recentApplications.map((app) => (\n                        <Link key={app.id} href={`/applications/${app.id}`}>\n                          <div className=\"flex items-center justify-between p-4 hover:bg-gray-50 rounded-lg border border-gray-100 hover:border-blue-200 transition-all cursor-pointer\">\n                            <div className=\"flex items-center space-x-4\">\n                              <div className=\"flex-shrink-0\">\n                                <div className=\"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center\">\n                                  <DocumentTextIcon className=\"h-6 w-6 text-blue-600\" />\n                                </div>\n                              </div>\n                              <div className=\"flex-1\">\n                                <div className=\"flex items-center space-x-2 mb-1\">\n                                  <p className=\"text-sm font-semibold text-gray-900\">\n                                    {app.full_name_khmer || app.full_name_latin || 'មិនបានបញ្ជាក់ឈ្មោះ'}\n                                  </p>\n                                  {app.full_name_khmer && app.full_name_latin && (\n                                    <span className=\"text-xs text-gray-500\">({app.full_name_latin})</span>\n                                  )}\n                                </div>\n                                <div className=\"flex items-center space-x-4 text-sm text-gray-600\">\n                                  <span className=\"flex items-center\">\n                                    <CurrencyDollarIcon className=\"w-4 h-4 mr-1\" />\n                                    {app.requested_amount ? formatCurrency(app.requested_amount) : 'មិនបានបញ្ជាក់'}\n                                  </span>\n                                  {app.phone && (\n                                    <span className=\"flex items-center\">\n                                      <PhoneIcon className=\"w-4 h-4 mr-1\" />\n                                      {app.phone}\n                                    </span>\n                                  )}\n                                  {app.portfolio_officer_name && (\n                                    <span className=\"flex items-center\">\n                                      <UserIcon className=\"w-4 h-4 mr-1\" />\n                                      {app.portfolio_officer_name}\n                                    </span>\n                                  )}\n                                </div>\n                              </div>\n                            </div>\n                            <div className=\"flex items-center space-x-3\">\n                              <div className=\"text-right\">\n                                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(app.status)}`}>\n                                  {app.status === 'draft' && 'ព្រាង'}\n                                  {app.status === 'submitted' && 'បានដាក់ស្នើ'}\n                                  {app.status === 'under_review' && 'កំពុងពិនិត្យ'}\n                                  {app.status === 'approved' && 'អនុម័ត'}\n                                  {app.status === 'rejected' && 'បដិសេធ'}\n                                </span>\n                                <p className=\"text-xs text-gray-500 mt-1\">\n                                  {formatDate(app.created_at)}\n                                </p>\n                              </div>\n                              <ArrowTrendingUpIcon className=\"w-4 h-4 text-gray-400\" />\n                            </div>\n                          </div>\n                        </Link>\n                      ))}\n                    </div>\n                  ) : (\n                    <div className=\"text-center py-8\">\n                      <DocumentTextIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n                      <h3 className=\"mt-2 text-sm font-medium text-gray-900\">មិនមានពាក្យសុំថ្មី</h3>\n                      <p className=\"mt-1 text-sm text-gray-500\">\n                        ពាក្យសុំនឹងបង្ហាញនៅទីនេះនៅពេលដែលត្រូវបានដាក់ស្នើ\n                      </p>\n                      <div className=\"mt-4\">\n                        <Link\n                          href=\"/applications/new\"\n                          className=\"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 text-sm\"\n                        >\n                          <PlusIcon className=\"w-4 h-4 mr-2\" />\n                          បង្កើតពាក្យសុំថ្មី\n                        </Link>\n                      </div>\n                    </div>\n                  )}\n                </div>\n              </div>\n            </div>\n\n            {/* System Overview & Activity */}\n            <div className=\"space-y-6\">\n              {/* System Overview */}\n              <div className=\"bg-white shadow rounded-lg\">\n                <div className=\"px-4 py-5 sm:p-6\">\n                  <h3 className=\"text-lg font-medium text-gray-900 mb-4\">System Overview</h3>\n                  <div className=\"space-y-4\">\n                    {systemStats.map((stat) => (\n                      <Link key={stat.name} href={stat.href}>\n                        <div className=\"flex items-center justify-between hover:bg-gray-50 p-2 rounded cursor-pointer\">\n                          <div className=\"flex items-center\">\n                            <stat.icon className=\"h-5 w-5 text-gray-400 mr-3\" />\n                            <span className=\"text-sm font-medium text-gray-900\">{stat.name}</span>\n                          </div>\n                          <span className=\"text-sm text-gray-600\">\n                            {statsLoading ? (\n                              <div className=\"animate-pulse bg-gray-200 h-4 w-8 rounded\"></div>\n                            ) : (\n                              stat.value.toLocaleString()\n                            )}\n                          </span>\n                        </div>\n                      </Link>\n                    ))}\n                  </div>\n                </div>\n              </div>\n\n              {/* Recent Activity */}\n              <div className=\"bg-white shadow rounded-lg\">\n                <div className=\"px-4 py-5 sm:p-6\">\n                  <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Recent Activity</h3>\n                  \n                  {activityLoading ? (\n                    <div className=\"space-y-3\">\n                      {[...Array(4)].map((_, i) => (\n                        <div key={i} className=\"animate-pulse flex items-start space-x-3\">\n                          <div className=\"rounded-full bg-gray-200 h-8 w-8\"></div>\n                          <div className=\"flex-1 space-y-2\">\n                            <div className=\"h-3 bg-gray-200 rounded w-3/4\"></div>\n                            <div className=\"h-3 bg-gray-200 rounded w-1/2\"></div>\n                          </div>\n                        </div>\n                      ))}\n                    </div>\n                  ) : activityTimeline && activityTimeline.length > 0 ? (\n                    <div className=\"space-y-4\">\n                      {activityTimeline.slice(0, 5).map((activity) => {\n                        const ActivityIcon = getActivityIcon(activity.type);\n                        return (\n                          <div key={activity.id} className=\"flex items-start space-x-3\">\n                            <div className=\"flex-shrink-0\">\n                              <ActivityIcon className=\"h-6 w-6 text-gray-400\" />\n                            </div>\n                            <div className=\"min-w-0 flex-1\">\n                              <p className=\"text-sm font-medium text-gray-900\">{activity.title}</p>\n                              <p className=\"text-sm text-gray-500\">{activity.description}</p>\n                              <p className=\"text-xs text-gray-400 mt-1\">\n                                {formatDate(activity.timestamp)}\n                              </p>\n                            </div>\n                          </div>\n                        );\n                      })}\n                    </div>\n                  ) : (\n                    <div className=\"text-center py-6\">\n                      <CalendarIcon className=\"mx-auto h-8 w-8 text-gray-400\" />\n                      <p className=\"mt-2 text-sm text-gray-500\">No recent activity</p>\n                    </div>\n                  )}\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </Layout>\n    </ProtectedRoute>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAMA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAkBA;AACA;AA9BA;;;;;;;;;AAgCe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD;IACvB,MAAM,EAAE,MAAM,cAAc,EAAE,WAAW,YAAY,EAAE,OAAO,UAAU,EAAE,SAAS,YAAY,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,oBAAiB,AAAD;IACpH,MAAM,EAAE,MAAM,kBAAkB,EAAE,WAAW,WAAW,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,wBAAqB,AAAD,EAAE;IACnF,MAAM,EAAE,MAAM,gBAAgB,EAAE,WAAW,eAAe,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,sBAAmB,AAAD,EAAE;IACnF,MAAM,EAAE,MAAM,kBAAkB,EAAE,WAAW,cAAc,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,wBAAqB,AAAD;IAEpF,MAAM,QAAQ;QACZ;YACE,MAAM;YACN,OAAO,gBAAgB,aAAa,SAAS;YAC7C,QAAQ;YACR,YAAY;YACZ,MAAM,+NAAA,CAAA,mBAAgB;YACtB,OAAO;YACP,SAAS;YACT,MAAM;QACR;QACA;YACE,MAAM;YACN,OAAO,gBAAgB,aAAa,WAAW;YAC/C,QAAQ;YACR,YAAY;YACZ,MAAM,iNAAA,CAAA,YAAS;YACf,OAAO;YACP,SAAS;YACT,MAAM;QACR;QACA;YACE,MAAM;YACN,OAAO,gBAAgB,aAAa,YAAY;YAChD,QAAQ;YACR,YAAY;YACZ,MAAM,6NAAA,CAAA,kBAAe;YACrB,OAAO;YACP,SAAS;YACT,MAAM;QACR;QACA;YACE,MAAM;YACN,OAAO,gBAAgB,aAAa,YAAY;YAChD,QAAQ;YACR,YAAY;YACZ,MAAM,qNAAA,CAAA,cAAW;YACjB,OAAO;YACP,SAAS;YACT,MAAM;QACR;KACD;IAED,MAAM,cAAc;QAClB;YACE,MAAM;YACN,OAAO,gBAAgB,MAAM,SAAS;YACtC,MAAM,iNAAA,CAAA,YAAS;YACf,MAAM;QACR;QACA;YACE,MAAM;YACN,OAAO,gBAAgB,YAAY,SAAS;YAC5C,MAAM,mOAAA,CAAA,qBAAkB;YACxB,MAAM;QACR;QACA;YACE,MAAM;YACN,OAAO,gBAAgB,SAAS,SAAS;YACzC,MAAM,mNAAA,CAAA,aAAU;YAChB,MAAM;QACR;QACA;YACE,MAAM;YACN,OAAO,gBAAgB,MAAM,SAAS;YACtC,MAAM,mNAAA,CAAA,aAAU;YAChB,MAAM;QACR;KACD;IAED,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAgB,OAAO;YAC5B,KAAK;gBAAS,OAAO;YACrB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAQ;YACN,KAAK;gBAAe,OAAO,+NAAA,CAAA,mBAAgB;YAC3C,KAAK;gBAAQ,OAAO,iNAAA,CAAA,YAAS;YAC7B;gBAAS,OAAO,+NAAA,CAAA,mBAAgB;QAClC;IACF;IAEA,IAAI,YAAY;QACd,qBACE,8OAAC,4IAAA,CAAA,iBAAc;sBACb,cAAA,8OAAC,sIAAA,CAAA,SAAM;0BACL,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAuC;;;;;;0CACrD,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAClC,8OAAC;gCACC,SAAS,IAAM;gCACf,WAAU;;kDAEV,8OAAC,yNAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQxD;IAEA,qBACE,8OAAC,4IAAA,CAAA,iBAAc;kBACb,cAAA,8OAAC,sIAAA,CAAA,SAAM;sBACL,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAmC;;;;;;kDACjD,8OAAC;wCAAE,WAAU;;4CAAgB;4CACZ,MAAM;4CAAW;;;;;;;;;;;;;0CAGpC,8OAAC;gCAAI,WAAU;;oCAAwB;oCACtB,IAAI,OAAO,kBAAkB;;;;;;;;;;;;;kCAKhD,8OAAC;wBAAI,WAAU;kCACZ,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC,4JAAA,CAAA,UAAI;gCAAiB,MAAM,KAAK,IAAI;0CACnC,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAW,CAAC,6BAA6B,EAAE,KAAK,OAAO,EAAE;0DAC5D,cAAA,8OAAC,KAAK,IAAI;oDAAC,WAAW,CAAC,QAAQ,EAAE,KAAK,KAAK,EAAE;oDAAE,eAAY;;;;;;;;;;;0DAE7D,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAA8C,KAAK,IAAI;;;;;;sEACrE,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;oEAAI,WAAU;8EACZ,6BACC,8OAAC;wEAAI,WAAU;;;;;+EAEf,KAAK,KAAK,CAAC,cAAc;;;;;;gEAG5B,KAAK,MAAM,kBACV,8OAAC;oEAAI,WAAW,CAAC,+CAA+C,EAC9D,KAAK,UAAU,KAAK,aAAa,mBACjC,KAAK,UAAU,KAAK,aAAa,iBAAiB,iBAClD;8EACC,KAAK,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;+BAtBjB,KAAK,IAAI;;;;;;;;;;oBAmCvB,oCACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,uNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;;;;;;0DAE1B,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAA6C;;;;;;sEAC3D,8OAAC;4DAAG,WAAU;sEACX,+BACC,8OAAC;gEAAI,WAAU;;;;;uEAEf,mBAAmB,0BAA0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAS3D,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,iNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;;;;;;0DAEvB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAA6C;;;;;;sEAC3D,8OAAC;4DAAG,WAAU;sEACX,+BACC,8OAAC;gEAAI,WAAU;;;;;uEAEf,GAAG,mBAAmB,4BAA4B,CAAC,KAAK,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CASvE,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,qOAAA,CAAA,sBAAmB;oDAAC,WAAU;;;;;;;;;;;0DAEjC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAA6C;;;;;;sEAC3D,8OAAC;4DAAG,WAAU;sEACX,+BACC,8OAAC;gEAAI,WAAU;;;;;uEAEf,GAAG,mBAAmB,wBAAwB,CAAC,CAAC,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAS/D,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,mNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;0DAExB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAA6C;;;;;;sEAC3D,8OAAC;4DAAG,WAAU;sEACX,6BACC,8OAAC;gEAAI,WAAU;;;;;uEAEf,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE,gBAAgB,MAAM,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAYlE,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAoC;;;;;;kEAClD,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAgB,WAAU;kEAA4C;;;;;;;;;;;;4CAKlF,4BACC,8OAAC;gDAAI,WAAU;0DACZ;uDAAI,MAAM;iDAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;wDAAY,WAAU;;0EACrB,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;;;;;kFACf,8OAAC;wEAAI,WAAU;;;;;;;;;;;;;uDAJT;;;;;;;;;uDASZ,sBAAsB,mBAAmB,MAAM,GAAG,kBACpD,8OAAC;gDAAI,WAAU;0DACZ,mBAAmB,GAAG,CAAC,CAAC,oBACvB,8OAAC,4JAAA,CAAA,UAAI;wDAAc,MAAM,CAAC,cAAc,EAAE,IAAI,EAAE,EAAE;kEAChD,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;sFACb,cAAA,8OAAC;gFAAI,WAAU;0FACb,cAAA,8OAAC,+NAAA,CAAA,mBAAgB;oFAAC,WAAU;;;;;;;;;;;;;;;;sFAGhC,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAI,WAAU;;sGACb,8OAAC;4FAAE,WAAU;sGACV,IAAI,eAAe,IAAI,IAAI,eAAe,IAAI;;;;;;wFAEhD,IAAI,eAAe,IAAI,IAAI,eAAe,kBACzC,8OAAC;4FAAK,WAAU;;gGAAwB;gGAAE,IAAI,eAAe;gGAAC;;;;;;;;;;;;;8FAGlE,8OAAC;oFAAI,WAAU;;sGACb,8OAAC;4FAAK,WAAU;;8GACd,8OAAC,mOAAA,CAAA,qBAAkB;oGAAC,WAAU;;;;;;gGAC7B,IAAI,gBAAgB,GAAG,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,IAAI,gBAAgB,IAAI;;;;;;;wFAEhE,IAAI,KAAK,kBACR,8OAAC;4FAAK,WAAU;;8GACd,8OAAC,iNAAA,CAAA,YAAS;oGAAC,WAAU;;;;;;gGACpB,IAAI,KAAK;;;;;;;wFAGb,IAAI,sBAAsB,kBACzB,8OAAC;4FAAK,WAAU;;8GACd,8OAAC,+MAAA,CAAA,WAAQ;oGAAC,WAAU;;;;;;gGACnB,IAAI,sBAAsB;;;;;;;;;;;;;;;;;;;;;;;;;8EAMrC,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAK,WAAW,CAAC,wEAAwE,EAAE,eAAe,IAAI,MAAM,GAAG;;wFACrH,IAAI,MAAM,KAAK,WAAW;wFAC1B,IAAI,MAAM,KAAK,eAAe;wFAC9B,IAAI,MAAM,KAAK,kBAAkB;wFACjC,IAAI,MAAM,KAAK,cAAc;wFAC7B,IAAI,MAAM,KAAK,cAAc;;;;;;;8FAEhC,8OAAC;oFAAE,WAAU;8FACV,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,IAAI,UAAU;;;;;;;;;;;;sFAG9B,8OAAC,qOAAA,CAAA,sBAAmB;4EAAC,WAAU;;;;;;;;;;;;;;;;;;uDAlD1B,IAAI,EAAE;;;;;;;;;qEAyDrB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,+NAAA,CAAA,mBAAgB;wDAAC,WAAU;;;;;;kEAC5B,8OAAC;wDAAG,WAAU;kEAAyC;;;;;;kEACvD,8OAAC;wDAAE,WAAU;kEAA6B;;;;;;kEAG1C,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4DACH,MAAK;4DACL,WAAU;;8EAEV,8OAAC,+MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAWnD,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAyC;;;;;;8DACvD,8OAAC;oDAAI,WAAU;8DACZ,YAAY,GAAG,CAAC,CAAC,qBAChB,8OAAC,4JAAA,CAAA,UAAI;4DAAiB,MAAM,KAAK,IAAI;sEACnC,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,KAAK,IAAI;gFAAC,WAAU;;;;;;0FACrB,8OAAC;gFAAK,WAAU;0FAAqC,KAAK,IAAI;;;;;;;;;;;;kFAEhE,8OAAC;wEAAK,WAAU;kFACb,6BACC,8OAAC;4EAAI,WAAU;;;;;mFAEf,KAAK,KAAK,CAAC,cAAc;;;;;;;;;;;;2DAVtB,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;kDAqB5B,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAyC;;;;;;gDAEtD,gCACC,8OAAC;oDAAI,WAAU;8DACZ;2DAAI,MAAM;qDAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;4DAAY,WAAU;;8EACrB,8OAAC;oEAAI,WAAU;;;;;;8EACf,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;;;;;;sFACf,8OAAC;4EAAI,WAAU;;;;;;;;;;;;;2DAJT;;;;;;;;;2DASZ,oBAAoB,iBAAiB,MAAM,GAAG,kBAChD,8OAAC;oDAAI,WAAU;8DACZ,iBAAiB,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;wDACjC,MAAM,eAAe,gBAAgB,SAAS,IAAI;wDAClD,qBACE,8OAAC;4DAAsB,WAAU;;8EAC/B,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEAAa,WAAU;;;;;;;;;;;8EAE1B,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAE,WAAU;sFAAqC,SAAS,KAAK;;;;;;sFAChE,8OAAC;4EAAE,WAAU;sFAAyB,SAAS,WAAW;;;;;;sFAC1D,8OAAC;4EAAE,WAAU;sFACV,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,SAAS,SAAS;;;;;;;;;;;;;2DAR1B,SAAS,EAAE;;;;;oDAazB;;;;;yEAGF,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,uNAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;sEACxB,8OAAC;4DAAE,WAAU;sEAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWhE", "debugId": null}}]}