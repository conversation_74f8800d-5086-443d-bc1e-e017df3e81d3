{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/backend/lc-workflow-frontend/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\nexport function formatBytes(bytes: number, decimals = 2): string {\n  if (bytes === 0) return '0 Bytes';\n\n  const k = 1024;\n  const dm = decimals < 0 ? 0 : decimals;\n  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];\n\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];\n}\n\nexport function formatDate(dateString: string): string {\n  const date = new Date(dateString);\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  }).format(date);\n}\n\nexport function formatCurrency(amount: number, currency = 'USD'): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency,\n  }).format(amount);\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text;\n  return text.slice(0, maxLength) + '...';\n}\n\nexport function getFileIcon(mimeType: string): string {\n  if (mimeType.startsWith('image/')) return '🖼️';\n  if (mimeType === 'application/pdf') return '📄';\n  if (mimeType.includes('word')) return '📝';\n  if (mimeType.includes('excel') || mimeType.includes('spreadsheet')) return '📊';\n  if (mimeType.startsWith('text/')) return '📄';\n  return '📎';\n}\n\nexport function validateEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\nexport function validatePhone(phone: string): boolean {\n  const phoneRegex = /^\\+?[\\d\\s\\-\\(\\)]+$/;\n  return phoneRegex.test(phone) && phone.replace(/\\D/g, '').length >= 10;\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9);\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean;\n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args);\n      inThrottle = true;\n      setTimeout(() => (inThrottle = false), limit);\n    }\n  };\n}\n\nexport function isValidUUID(uuid: string): boolean {\n  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;\n  return uuidRegex.test(uuid);\n}\n\nexport function validateUUID(uuid: string): string {\n  if (!isValidUUID(uuid)) {\n    throw new Error('Invalid UUID format');\n  }\n  return uuid;\n}\n\nexport function sanitizeUUID(uuid: string): string {\n  return uuid.replace(/[^a-f0-9-]/gi, '');\n}"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,YAAY,KAAa,EAAE,WAAW,CAAC;IACrD,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,KAAK,WAAW,IAAI,IAAI;IAC9B,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;KAAK;IAEvE,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,OAAO,MAAM,KAAK,CAAC,EAAE;AAC1E;AAEO,SAAS,WAAW,UAAkB;IAC3C,MAAM,OAAO,IAAI,KAAK;IACtB,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,eAAe,MAAc,EAAE,WAAW,KAAK;IAC7D,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP;IACF,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,aAAa;AACpC;AAEO,SAAS,YAAY,QAAgB;IAC1C,IAAI,SAAS,UAAU,CAAC,WAAW,OAAO;IAC1C,IAAI,aAAa,mBAAmB,OAAO;IAC3C,IAAI,SAAS,QAAQ,CAAC,SAAS,OAAO;IACtC,IAAI,SAAS,QAAQ,CAAC,YAAY,SAAS,QAAQ,CAAC,gBAAgB,OAAO;IAC3E,IAAI,SAAS,UAAU,CAAC,UAAU,OAAO;IACzC,OAAO;AACT;AAEO,SAAS,cAAc,KAAa;IACzC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,cAAc,KAAa;IACzC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC,UAAU,MAAM,OAAO,CAAC,OAAO,IAAI,MAAM,IAAI;AACtE;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,SACd,IAAO,EACP,KAAa;IAEb,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW,IAAO,aAAa,OAAQ;QACzC;IACF;AACF;AAEO,SAAS,YAAY,IAAY;IACtC,MAAM,YAAY;IAClB,OAAO,UAAU,IAAI,CAAC;AACxB;AAEO,SAAS,aAAa,IAAY;IACvC,IAAI,CAAC,YAAY,OAAO;QACtB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAEO,SAAS,aAAa,IAAY;IACvC,OAAO,KAAK,OAAO,CAAC,gBAAgB;AACtC", "debugId": null}}, {"offset": {"line": 119, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/backend/lc-workflow-frontend/src/hooks/useUsers.ts"], "sourcesContent": ["import { useMutation, useQuery, useQueryClient, useInfiniteQuery } from '@tanstack/react-query';\r\nimport { apiClient } from '@/lib/api';\r\nimport { User, UserCreate, UserUpdate, PaginatedResponse } from '@/types/models';\r\nimport toast from 'react-hot-toast';\r\nimport { isValidUUID, validateUUID } from '@/lib/utils';\r\n\r\n// User query keys\r\nexport const userKeys = {\r\n  all: ['users'] as const,\r\n  lists: () => [...userKeys.all, 'list'] as const,\r\n  list: (filters: any) => [...userKeys.lists(), filters] as const,\r\n  details: () => [...userKeys.all, 'detail'] as const,\r\n  detail: (id: string) => [...userKeys.details(), id] as const,\r\n};\r\n\r\n// User hooks\r\nexport const useUsers = (filters: {\r\n  page?: number;\r\n  size?: number;\r\n  role?: string;\r\n  department_id?: string;\r\n  branch_id?: string;\r\n  search?: string;\r\n  status?: string;\r\n} = {}) => {\r\n  return useQuery({\r\n    queryKey: userKeys.list(filters),\r\n    queryFn: () => apiClient.get<PaginatedResponse<User>>('/users', {\r\n      params: filters,\r\n    }),\r\n    staleTime: 60 * 1000, // 1 minute\r\n  });\r\n};\r\n\r\nexport const useInfiniteUsers = (filters: {\r\n  size?: number;\r\n  role?: string;\r\n  department_id?: string;\r\n  branch_id?: string;\r\n  search?: string;\r\n  status?: string;\r\n} = {}) => {\r\n  return useInfiniteQuery({\r\n    queryKey: userKeys.list(filters),\r\n    queryFn: ({ pageParam = 1 }) => \r\n      apiClient.get<PaginatedResponse<User>>('/users', {\r\n        params: { ...filters, page: pageParam },\r\n      }),\r\n    getNextPageParam: (lastPage) => {\r\n      if (lastPage.page < lastPage.pages) {\r\n        return lastPage.page + 1;\r\n      }\r\n      return undefined;\r\n    },\r\n    initialPageParam: 1,\r\n    staleTime: 60 * 1000,\r\n  });\r\n};\r\n\r\nexport const useUser = (id: string) => {\r\n  return useQuery({\r\n    queryKey: userKeys.detail(id),\r\n    queryFn: () => apiClient.get<User>(`/users/${id}`),\r\n    staleTime: 5 * 60 * 1000, // 5 minutes\r\n    enabled: !!id && isValidUUID(id),\r\n  });\r\n};\r\n\r\nexport const useCreateUser = () => {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (data: UserCreate) => apiClient.post<User>('/users', data),\r\n    onSuccess: (newUser) => {\r\n      queryClient.invalidateQueries({ queryKey: userKeys.lists() });\r\n      toast.success('User created successfully!');\r\n    },\r\n    onError: (error: any) => {\r\n      const message = error.response?.data?.detail || 'Failed to create user';\r\n      toast.error(message);\r\n    },\r\n  });\r\n};\r\n\r\nexport const useUpdateUser = (id: string) => {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (data: UserUpdate) => {\r\n      validateUUID(id, 'User');\r\n      return apiClient.patch<User>(`/users/${id}`, data);\r\n    },\r\n    onSuccess: (updatedUser) => {\r\n      queryClient.setQueryData(userKeys.detail(id), updatedUser);\r\n      queryClient.invalidateQueries({ queryKey: userKeys.lists() });\r\n      toast.success('User updated successfully!');\r\n    },\r\n    onError: (error: any) => {\r\n      const message = error.response?.data?.detail || 'Failed to update user';\r\n      toast.error(message);\r\n    },\r\n  });\r\n};\r\n\r\nexport const useDeleteUser = () => {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (id: string) => {\r\n      validateUUID(id, 'User');\r\n      return apiClient.delete(`/users/${id}`);\r\n    },\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: userKeys.lists() });\r\n      toast.success('User deleted successfully!');\r\n    },\r\n    onError: (error: any) => {\r\n      const message = error.response?.data?.detail || 'Failed to delete user';\r\n      toast.error(message);\r\n    },\r\n  });\r\n};\r\n\r\n// User profile hooks\r\nexport const useUpdateProfile = (userId: string) => {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (data: Partial<UserUpdate>) => \r\n      apiClient.patch<User>(`/users/${userId}`, data),\r\n    onSuccess: (updatedUser) => {\r\n      queryClient.setQueryData(['auth', 'user'], updatedUser);\r\n      toast.success('Profile updated successfully!');\r\n    },\r\n    onError: (error: any) => {\r\n      const message = error.response?.data?.detail || 'Failed to update profile';\r\n      toast.error(message);\r\n    },\r\n  });\r\n};\r\n\r\nexport const useChangePassword = () => {\r\n  return useMutation({\r\n    mutationFn: ({ old_password, new_password }: { old_password: string; new_password: string }) =>\r\n      apiClient.patch('/users/me/change-password', { old_password, new_password }),\r\n    onSuccess: () => {\r\n      toast.success('Password changed successfully!');\r\n    },\r\n    onError: (error: any) => {\r\n      const message = error.response?.data?.detail || 'Failed to change password';\r\n      toast.error(message);\r\n    },\r\n  });\r\n};\r\n\r\n// User statistics hook\r\nexport const useUserStats = () => {\r\n  return useQuery({\r\n    queryKey: [...userKeys.all, 'stats'],\r\n    queryFn: () => apiClient.get('/users/stats'),\r\n    staleTime: 5 * 60 * 1000,\r\n  });\r\n};"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;;;;;AAGO,MAAM,WAAW;IACtB,KAAK;QAAC;KAAQ;IACd,OAAO,IAAM;eAAI,SAAS,GAAG;YAAE;SAAO;IACtC,MAAM,CAAC,UAAiB;eAAI,SAAS,KAAK;YAAI;SAAQ;IACtD,SAAS,IAAM;eAAI,SAAS,GAAG;YAAE;SAAS;IAC1C,QAAQ,CAAC,KAAe;eAAI,SAAS,OAAO;YAAI;SAAG;AACrD;AAGO,MAAM,WAAW,CAAC,UAQrB,CAAC,CAAC;IACJ,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,SAAS,IAAI,CAAC;QACxB,SAAS,IAAM,iHAAA,CAAA,YAAS,CAAC,GAAG,CAA0B,UAAU;gBAC9D,QAAQ;YACV;QACA,WAAW,KAAK;IAClB;AACF;AAEO,MAAM,mBAAmB,CAAC,UAO7B,CAAC,CAAC;IACJ,OAAO,CAAA,GAAA,mLAAA,CAAA,mBAAgB,AAAD,EAAE;QACtB,UAAU,SAAS,IAAI,CAAC;QACxB,SAAS,CAAC,EAAE,YAAY,CAAC,EAAE,GACzB,iHAAA,CAAA,YAAS,CAAC,GAAG,CAA0B,UAAU;gBAC/C,QAAQ;oBAAE,GAAG,OAAO;oBAAE,MAAM;gBAAU;YACxC;QACF,kBAAkB,CAAC;YACjB,IAAI,SAAS,IAAI,GAAG,SAAS,KAAK,EAAE;gBAClC,OAAO,SAAS,IAAI,GAAG;YACzB;YACA,OAAO;QACT;QACA,kBAAkB;QAClB,WAAW,KAAK;IAClB;AACF;AAEO,MAAM,UAAU,CAAC;IACtB,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,SAAS,MAAM,CAAC;QAC1B,SAAS,IAAM,iHAAA,CAAA,YAAS,CAAC,GAAG,CAAO,CAAC,OAAO,EAAE,IAAI;QACjD,WAAW,IAAI,KAAK;QACpB,SAAS,CAAC,CAAC,MAAM,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE;IAC/B;AACF;AAEO,MAAM,gBAAgB;IAC3B,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC,OAAqB,iHAAA,CAAA,YAAS,CAAC,IAAI,CAAO,UAAU;QACjE,WAAW,CAAC;YACV,YAAY,iBAAiB,CAAC;gBAAE,UAAU,SAAS,KAAK;YAAG;YAC3D,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QAChB;QACA,SAAS,CAAC;YACR,MAAM,UAAU,MAAM,QAAQ,EAAE,MAAM,UAAU;YAChD,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd;IACF;AACF;AAEO,MAAM,gBAAgB,CAAC;IAC5B,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC;YACX,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE,IAAI;YACjB,OAAO,iHAAA,CAAA,YAAS,CAAC,KAAK,CAAO,CAAC,OAAO,EAAE,IAAI,EAAE;QAC/C;QACA,WAAW,CAAC;YACV,YAAY,YAAY,CAAC,SAAS,MAAM,CAAC,KAAK;YAC9C,YAAY,iBAAiB,CAAC;gBAAE,UAAU,SAAS,KAAK;YAAG;YAC3D,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QAChB;QACA,SAAS,CAAC;YACR,MAAM,UAAU,MAAM,QAAQ,EAAE,MAAM,UAAU;YAChD,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd;IACF;AACF;AAEO,MAAM,gBAAgB;IAC3B,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC;YACX,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE,IAAI;YACjB,OAAO,iHAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,IAAI;QACxC;QACA,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU,SAAS,KAAK;YAAG;YAC3D,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QAChB;QACA,SAAS,CAAC;YACR,MAAM,UAAU,MAAM,QAAQ,EAAE,MAAM,UAAU;YAChD,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd;IACF;AACF;AAGO,MAAM,mBAAmB,CAAC;IAC/B,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC,OACX,iHAAA,CAAA,YAAS,CAAC,KAAK,CAAO,CAAC,OAAO,EAAE,QAAQ,EAAE;QAC5C,WAAW,CAAC;YACV,YAAY,YAAY,CAAC;gBAAC;gBAAQ;aAAO,EAAE;YAC3C,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QAChB;QACA,SAAS,CAAC;YACR,MAAM,UAAU,MAAM,QAAQ,EAAE,MAAM,UAAU;YAChD,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd;IACF;AACF;AAEO,MAAM,oBAAoB;IAC/B,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC,EAAE,YAAY,EAAE,YAAY,EAAkD,GACzF,iHAAA,CAAA,YAAS,CAAC,KAAK,CAAC,6BAA6B;gBAAE;gBAAc;YAAa;QAC5E,WAAW;YACT,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QAChB;QACA,SAAS,CAAC;YACR,MAAM,UAAU,MAAM,QAAQ,EAAE,MAAM,UAAU;YAChD,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd;IACF;AACF;AAGO,MAAM,eAAe;IAC1B,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;eAAI,SAAS,GAAG;YAAE;SAAQ;QACpC,SAAS,IAAM,iHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QAC7B,WAAW,IAAI,KAAK;IACtB;AACF", "debugId": null}}, {"offset": {"line": 300, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/backend/lc-workflow-frontend/src/hooks/useDepartments.ts"], "sourcesContent": ["import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';\nimport { apiClient } from '@/lib/api';\nimport { Department, DepartmentWithRelations, PaginatedResponse } from '@/types/models';\nimport toast from 'react-hot-toast';\nimport { isValidUUID, validateUUID } from '@/lib/utils';\n\n// Department query keys\nexport const departmentKeys = {\n  all: ['departments'] as const,\n  lists: () => [...departmentKeys.all, 'list'] as const,\n  list: (filters: any) => [...departmentKeys.lists(), filters] as const,\n  details: () => [...departmentKeys.all, 'detail'] as const,\n  detail: (id: string) => [...departmentKeys.details(), id] as const,\n  withRelations: (id: string) => [...departmentKeys.detail(id), 'relations'] as const,\n  stats: () => [...departmentKeys.all, 'stats'] as const,\n};\n\n// Department hooks\nexport const useDepartments = (filters: {\n  page?: number;\n  size?: number;\n  search?: string;\n  is_active?: boolean;\n} = {}) => {\n  return useQuery({\n    queryKey: departmentKeys.list(filters),\n    queryFn: () => apiClient.get<PaginatedResponse<Department>>('/departments', {\n      params: filters,\n    }),\n    staleTime: 5 * 60 * 1000, // 5 minutes\n  });\n};\n\nexport const useActiveDepartments = () => {\n  return useQuery({\n    queryKey: departmentKeys.list({ is_active: true }),\n    queryFn: () => apiClient.get<Department[]>('/departments/active'),\n    staleTime: 5 * 60 * 1000,\n  });\n};\n\nexport const useDepartment = (id: string) => {\n  return useQuery({\n    queryKey: departmentKeys.detail(id),\n    queryFn: () => apiClient.get<Department>(`/departments/${id}`),\n    staleTime: 5 * 60 * 1000,\n    enabled: !!id && isValidUUID(id),\n  });\n};\n\nexport const useCreateDepartment = () => {\n  const queryClient = useQueryClient();\n\n  return useMutation({\n    mutationFn: (data: { name: string; code: string; description?: string; manager_id?: string }) =>\n      apiClient.post<Department>('/departments', data),\n    onSuccess: () => {\n      queryClient.invalidateQueries({ queryKey: departmentKeys.lists() });\n      toast.success('Department created successfully!');\n    },\n    onError: (error: any) => {\n      const message = error.response?.data?.detail || 'Failed to create department';\n      toast.error(message);\n    },\n  });\n};\n\nexport const useUpdateDepartment = (id: string) => {\n  const queryClient = useQueryClient();\n\n  return useMutation({\n    mutationFn: (data: { name?: string; code?: string; description?: string; manager_id?: string; is_active?: boolean }) => {\n      validateUUID(id, 'Department');\n      return apiClient.patch<Department>(`/departments/${id}`, data);\n    },\n    onSuccess: (updatedDepartment) => {\n      queryClient.setQueryData(departmentKeys.detail(id), updatedDepartment);\n      queryClient.invalidateQueries({ queryKey: departmentKeys.lists() });\n      toast.success('Department updated successfully!');\n    },\n    onError: (error: any) => {\n      const message = error.response?.data?.detail || 'Failed to update department';\n      toast.error(message);\n    },\n  });\n};\n\nexport const useDeleteDepartment = () => {\n  const queryClient = useQueryClient();\n\n  return useMutation({\n    mutationFn: (id: string) => {\n      validateUUID(id, 'Department');\n      return apiClient.delete(`/departments/${id}`);\n    },\n    onSuccess: () => {\n      queryClient.invalidateQueries({ queryKey: departmentKeys.lists() });\n      toast.success('Department deleted successfully!');\n    },\n    onError: (error: any) => {\n      const message = error.response?.data?.detail || 'Failed to delete department';\n      toast.error(message);\n    },\n  });\n};\n\n// Enhanced hooks with relationship queries\n\n/**\n * Hook to fetch departments with counts optimized for list views\n * Uses the enhanced backend API with include_counts parameter\n */\nexport const useDepartmentsWithCounts = (filters: {\n  page?: number;\n  size?: number;\n  search?: string;\n  is_active?: boolean;\n} = {}) => {\n  return useQuery({\n    queryKey: departmentKeys.list({ ...filters, include_counts: true }),\n    queryFn: () => apiClient.get<PaginatedResponse<Department>>('/departments', {\n      params: { ...filters, include_counts: true },\n    }),\n    staleTime: 5 * 60 * 1000,\n  });\n};\n\n/**\n * Hook to fetch a single department with all its relationships\n * Includes manager, users, branches with full details\n */\nexport const useDepartmentWithRelations = (id: string) => {\n  return useQuery({\n    queryKey: departmentKeys.withRelations(id),\n    queryFn: async () => {\n      try {\n        // First try the enhanced endpoint with relations if it exists\n        // Note: apiClient.get already extracts response.data, so departmentData is the actual data\n        const departmentData = await apiClient.get<DepartmentWithRelations>(`/departments/${id}/with-relations`);\n        \n        return {\n          ...departmentData,\n          users: departmentData.users || [],\n          branches: departmentData.branches || [],\n          user_count: departmentData.user_count || 0,\n          branch_count: departmentData.branch_count || 0,\n          active_user_count: departmentData.active_user_count || 0,\n        };\n      } catch (error: any) {\n        console.error('API Error:', error);\n        // Fallback to multiple API calls if the enhanced endpoint doesn't exist\n        if (error.response?.status === 404) {\n          // Fetch department basic info\n          const departmentResponse = await apiClient.get<Department>(`/departments/${id}`);\n          \n          // Fetch department statistics (includes user count)\n          const statsResponse = await apiClient.get(`/departments/${id}/stats`);\n          \n          // Fetch users of department\n          const usersResponse = await apiClient.get(`/departments/${id}/users`);\n\n          // Create extended department object\n          const departmentWithRelations: DepartmentWithRelations = {\n            ...departmentResponse,\n            users: usersResponse || [],\n            branches: [], // We'll implement branches later if needed\n            user_count: (statsResponse as any)?.user_count || 0,\n            branch_count: (statsResponse as any)?.branch_count || 0,\n            active_user_count: (usersResponse as any)?.filter((user: any) => user.status === 'active')?.length || 0,\n          };\n\n          return departmentWithRelations;\n        }\n        throw error;\n      }\n    },\n    staleTime: 5 * 60 * 1000,\n    enabled: !!id && isValidUUID(id),\n  });\n};\n\n/**\n * Hook to fetch department statistics\n * Useful for dashboard views\n */\nexport const useDepartmentStats = () => {\n  return useQuery({\n    queryKey: departmentKeys.stats(),\n    queryFn: () => apiClient.get('/departments/stats'),\n    staleTime: 10 * 60 * 1000, // 10 minutes\n  });\n};\n\n/**\n * Hook to fetch department summary with key metrics\n * Optimized for dashboard cards and overview displays\n */\nexport const useDepartmentSummary = (id: string) => {\n  return useQuery({\n    queryKey: [...departmentKeys.detail(id), 'summary'],\n    queryFn: () => apiClient.get(`/departments/${id}/summary`),\n    staleTime: 5 * 60 * 1000,\n    enabled: !!id && isValidUUID(id),\n  });\n};\n\n// Mutation hook with relationship invalidation\nexport const useUpdateDepartmentWithRelations = () => {\n  const queryClient = useQueryClient();\n\n  return useMutation({\n    mutationFn: ({ departmentId, departmentData }: {\n      departmentId: string;\n      departmentData: { name?: string; code?: string; description?: string; manager_id?: string; is_active?: boolean };\n    }) => {\n      validateUUID(departmentId, 'Department');\n      return apiClient.patch<Department>(`/departments/${departmentId}`, departmentData);\n    },\n    onSuccess: (updatedDepartment, variables) => {\n      const { departmentId } = variables;\n      \n      // Update all related queries\n      queryClient.setQueryData(departmentKeys.detail(departmentId), updatedDepartment);\n      queryClient.invalidateQueries({ queryKey: departmentKeys.withRelations(departmentId) });\n      queryClient.invalidateQueries({ queryKey: departmentKeys.lists() });\n      queryClient.invalidateQueries({ queryKey: departmentKeys.stats() });\n      \n      toast.success('Department updated successfully!');\n    },\n    onError: (error: any) => {\n      const message = error.response?.data?.detail || 'Failed to update department';\n      toast.error(message);\n    },\n  });\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;AAAA;AACA;AAEA;AACA;;;;;AAGO,MAAM,iBAAiB;IAC5B,KAAK;QAAC;KAAc;IACpB,OAAO,IAAM;eAAI,eAAe,GAAG;YAAE;SAAO;IAC5C,MAAM,CAAC,UAAiB;eAAI,eAAe,KAAK;YAAI;SAAQ;IAC5D,SAAS,IAAM;eAAI,eAAe,GAAG;YAAE;SAAS;IAChD,QAAQ,CAAC,KAAe;eAAI,eAAe,OAAO;YAAI;SAAG;IACzD,eAAe,CAAC,KAAe;eAAI,eAAe,MAAM,CAAC;YAAK;SAAY;IAC1E,OAAO,IAAM;eAAI,eAAe,GAAG;YAAE;SAAQ;AAC/C;AAGO,MAAM,iBAAiB,CAAC,UAK3B,CAAC,CAAC;IACJ,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,eAAe,IAAI,CAAC;QAC9B,SAAS,IAAM,iHAAA,CAAA,YAAS,CAAC,GAAG,CAAgC,gBAAgB;gBAC1E,QAAQ;YACV;QACA,WAAW,IAAI,KAAK;IACtB;AACF;AAEO,MAAM,uBAAuB;IAClC,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,eAAe,IAAI,CAAC;YAAE,WAAW;QAAK;QAChD,SAAS,IAAM,iHAAA,CAAA,YAAS,CAAC,GAAG,CAAe;QAC3C,WAAW,IAAI,KAAK;IACtB;AACF;AAEO,MAAM,gBAAgB,CAAC;IAC5B,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,eAAe,MAAM,CAAC;QAChC,SAAS,IAAM,iHAAA,CAAA,YAAS,CAAC,GAAG,CAAa,CAAC,aAAa,EAAE,IAAI;QAC7D,WAAW,IAAI,KAAK;QACpB,SAAS,CAAC,CAAC,MAAM,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE;IAC/B;AACF;AAEO,MAAM,sBAAsB;IACjC,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC,OACX,iHAAA,CAAA,YAAS,CAAC,IAAI,CAAa,gBAAgB;QAC7C,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU,eAAe,KAAK;YAAG;YACjE,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QAChB;QACA,SAAS,CAAC;YACR,MAAM,UAAU,MAAM,QAAQ,EAAE,MAAM,UAAU;YAChD,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd;IACF;AACF;AAEO,MAAM,sBAAsB,CAAC;IAClC,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC;YACX,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE,IAAI;YACjB,OAAO,iHAAA,CAAA,YAAS,CAAC,KAAK,CAAa,CAAC,aAAa,EAAE,IAAI,EAAE;QAC3D;QACA,WAAW,CAAC;YACV,YAAY,YAAY,CAAC,eAAe,MAAM,CAAC,KAAK;YACpD,YAAY,iBAAiB,CAAC;gBAAE,UAAU,eAAe,KAAK;YAAG;YACjE,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QAChB;QACA,SAAS,CAAC;YACR,MAAM,UAAU,MAAM,QAAQ,EAAE,MAAM,UAAU;YAChD,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd;IACF;AACF;AAEO,MAAM,sBAAsB;IACjC,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC;YACX,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE,IAAI;YACjB,OAAO,iHAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,aAAa,EAAE,IAAI;QAC9C;QACA,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU,eAAe,KAAK;YAAG;YACjE,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QAChB;QACA,SAAS,CAAC;YACR,MAAM,UAAU,MAAM,QAAQ,EAAE,MAAM,UAAU;YAChD,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd;IACF;AACF;AAQO,MAAM,2BAA2B,CAAC,UAKrC,CAAC,CAAC;IACJ,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,eAAe,IAAI,CAAC;YAAE,GAAG,OAAO;YAAE,gBAAgB;QAAK;QACjE,SAAS,IAAM,iHAAA,CAAA,YAAS,CAAC,GAAG,CAAgC,gBAAgB;gBAC1E,QAAQ;oBAAE,GAAG,OAAO;oBAAE,gBAAgB;gBAAK;YAC7C;QACA,WAAW,IAAI,KAAK;IACtB;AACF;AAMO,MAAM,6BAA6B,CAAC;IACzC,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,eAAe,aAAa,CAAC;QACvC,SAAS;YACP,IAAI;gBACF,8DAA8D;gBAC9D,2FAA2F;gBAC3F,MAAM,iBAAiB,MAAM,iHAAA,CAAA,YAAS,CAAC,GAAG,CAA0B,CAAC,aAAa,EAAE,GAAG,eAAe,CAAC;gBAEvG,OAAO;oBACL,GAAG,cAAc;oBACjB,OAAO,eAAe,KAAK,IAAI,EAAE;oBACjC,UAAU,eAAe,QAAQ,IAAI,EAAE;oBACvC,YAAY,eAAe,UAAU,IAAI;oBACzC,cAAc,eAAe,YAAY,IAAI;oBAC7C,mBAAmB,eAAe,iBAAiB,IAAI;gBACzD;YACF,EAAE,OAAO,OAAY;gBACnB,QAAQ,KAAK,CAAC,cAAc;gBAC5B,wEAAwE;gBACxE,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;oBAClC,8BAA8B;oBAC9B,MAAM,qBAAqB,MAAM,iHAAA,CAAA,YAAS,CAAC,GAAG,CAAa,CAAC,aAAa,EAAE,IAAI;oBAE/E,oDAAoD;oBACpD,MAAM,gBAAgB,MAAM,iHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,aAAa,EAAE,GAAG,MAAM,CAAC;oBAEpE,4BAA4B;oBAC5B,MAAM,gBAAgB,MAAM,iHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,aAAa,EAAE,GAAG,MAAM,CAAC;oBAEpE,oCAAoC;oBACpC,MAAM,0BAAmD;wBACvD,GAAG,kBAAkB;wBACrB,OAAO,iBAAiB,EAAE;wBAC1B,UAAU,EAAE;wBACZ,YAAY,AAAC,eAAuB,cAAc;wBAClD,cAAc,AAAC,eAAuB,gBAAgB;wBACtD,mBAAmB,AAAC,eAAuB,OAAO,CAAC,OAAc,KAAK,MAAM,KAAK,WAAW,UAAU;oBACxG;oBAEA,OAAO;gBACT;gBACA,MAAM;YACR;QACF;QACA,WAAW,IAAI,KAAK;QACpB,SAAS,CAAC,CAAC,MAAM,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE;IAC/B;AACF;AAMO,MAAM,qBAAqB;IAChC,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,eAAe,KAAK;QAC9B,SAAS,IAAM,iHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QAC7B,WAAW,KAAK,KAAK;IACvB;AACF;AAMO,MAAM,uBAAuB,CAAC;IACnC,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;eAAI,eAAe,MAAM,CAAC;YAAK;SAAU;QACnD,SAAS,IAAM,iHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,aAAa,EAAE,GAAG,QAAQ,CAAC;QACzD,WAAW,IAAI,KAAK;QACpB,SAAS,CAAC,CAAC,MAAM,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE;IAC/B;AACF;AAGO,MAAM,mCAAmC;IAC9C,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC,EAAE,YAAY,EAAE,cAAc,EAG1C;YACC,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE,cAAc;YAC3B,OAAO,iHAAA,CAAA,YAAS,CAAC,KAAK,CAAa,CAAC,aAAa,EAAE,cAAc,EAAE;QACrE;QACA,WAAW,CAAC,mBAAmB;YAC7B,MAAM,EAAE,YAAY,EAAE,GAAG;YAEzB,6BAA6B;YAC7B,YAAY,YAAY,CAAC,eAAe,MAAM,CAAC,eAAe;YAC9D,YAAY,iBAAiB,CAAC;gBAAE,UAAU,eAAe,aAAa,CAAC;YAAc;YACrF,YAAY,iBAAiB,CAAC;gBAAE,UAAU,eAAe,KAAK;YAAG;YACjE,YAAY,iBAAiB,CAAC;gBAAE,UAAU,eAAe,KAAK;YAAG;YAEjE,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QAChB;QACA,SAAS,CAAC;YACR,MAAM,UAAU,MAAM,QAAQ,EAAE,MAAM,UAAU;YAChD,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd;IACF;AACF", "debugId": null}}, {"offset": {"line": 543, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/backend/lc-workflow-frontend/src/hooks/useBranches.ts"], "sourcesContent": ["import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';\nimport { apiClient } from '@/lib/api';\nimport { Branch, PaginatedResponse } from '@/types/models';\nimport toast from 'react-hot-toast';\n\n// Branch query keys\nexport const branchKeys = {\n  all: ['branches'] as const,\n  lists: () => [...branchKeys.all, 'list'] as const,\n  list: (filters: any) => [...branchKeys.lists(), filters] as const,\n  details: () => [...branchKeys.all, 'detail'] as const,\n  detail: (id: string) => [...branchKeys.details(), id] as const,\n};\n\n// Branch hooks\nexport const useBranches = (filters: {\n  page?: number;\n  size?: number;\n  search?: string;\n  is_active?: boolean;\n} = {}) => {\n  return useQuery({\n    queryKey: branchKeys.list(filters),\n    queryFn: () => apiClient.get<PaginatedResponse<Branch>>('/branches', {\n      params: filters,\n    }),\n    staleTime: 5 * 60 * 1000, // 5 minutes\n  });\n};\n\nexport const useActiveBranches = () => {\n  return useQuery({\n    queryKey: branchKeys.list({ is_active: true }),\n    queryFn: () => apiClient.get<Branch[]>('/branches/active'),\n    staleTime: 5 * 60 * 1000,\n  });\n};\n\nexport const useBranch = (id: string) => {\n  return useQuery({\n    queryKey: branchKeys.detail(id),\n    queryFn: () => apiClient.get<Branch>(`/branches/${id}`),\n    staleTime: 5 * 60 * 1000,\n    enabled: !!id && id !== 'undefined' && /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/.test(id),\n  });\n};\n\nexport const useCreateBranch = () => {\n  const queryClient = useQueryClient();\n\n  return useMutation({\n    mutationFn: (data: {\n      name: string;\n      code: string;\n      address: string;\n      phone_number?: string;\n      email?: string;\n      manager_id?: string;\n      latitude?: number;\n      longitude?: number;\n    }) => apiClient.post<Branch>('/branches', data),\n    onSuccess: () => {\n      queryClient.invalidateQueries({ queryKey: branchKeys.lists() });\n      toast.success('Branch created successfully!');\n    },\n    onError: (error: any) => {\n      const message = error.response?.data?.detail || 'Failed to create branch';\n      toast.error(message);\n    },\n  });\n};\n\nexport const useUpdateBranch = (id: string) => {\n  const queryClient = useQueryClient();\n\n  return useMutation({\n    mutationFn: (data: {\n      name?: string;\n      code?: string;\n      address?: string;\n      phone_number?: string;\n      email?: string;\n      manager_id?: string;\n      latitude?: number;\n      longitude?: number;\n      is_active?: boolean;\n    }) => {\n      if (!id || id === 'undefined' || !/^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/.test(id)) {\n        throw new Error('Invalid branch ID format');\n      }\n      return apiClient.patch<Branch>(`/branches/${id}`, data);\n    },\n    onSuccess: (updatedBranch) => {\n      queryClient.setQueryData(branchKeys.detail(id), updatedBranch);\n      queryClient.invalidateQueries({ queryKey: branchKeys.lists() });\n      toast.success('Branch updated successfully!');\n    },\n    onError: (error: any) => {\n      const message = error.response?.data?.detail || 'Failed to update branch';\n      toast.error(message);\n    },\n  });\n};\n\nexport const useDeleteBranch = () => {\n  const queryClient = useQueryClient();\n\n  return useMutation({\n    mutationFn: (id: string) => {\n      if (!id || id === 'undefined' || !/^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/.test(id)) {\n        throw new Error('Invalid branch ID format');\n      }\n      return apiClient.delete(`/branches/${id}`);\n    },\n    onSuccess: () => {\n      queryClient.invalidateQueries({ queryKey: branchKeys.lists() });\n      toast.success('Branch deleted successfully!');\n    },\n    onError: (error: any) => {\n      const message = error.response?.data?.detail || 'Failed to delete branch';\n      toast.error(message);\n    },\n  });\n};"], "names": [], "mappings": ";;;;;;;;;AAAA;AAAA;AAAA;AACA;AAEA;;;;AAGO,MAAM,aAAa;IACxB,KAAK;QAAC;KAAW;IACjB,OAAO,IAAM;eAAI,WAAW,GAAG;YAAE;SAAO;IACxC,MAAM,CAAC,UAAiB;eAAI,WAAW,KAAK;YAAI;SAAQ;IACxD,SAAS,IAAM;eAAI,WAAW,GAAG;YAAE;SAAS;IAC5C,QAAQ,CAAC,KAAe;eAAI,WAAW,OAAO;YAAI;SAAG;AACvD;AAGO,MAAM,cAAc,CAAC,UAKxB,CAAC,CAAC;IACJ,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,WAAW,IAAI,CAAC;QAC1B,SAAS,IAAM,iHAAA,CAAA,YAAS,CAAC,GAAG,CAA4B,aAAa;gBACnE,QAAQ;YACV;QACA,WAAW,IAAI,KAAK;IACtB;AACF;AAEO,MAAM,oBAAoB;IAC/B,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,WAAW,IAAI,CAAC;YAAE,WAAW;QAAK;QAC5C,SAAS,IAAM,iHAAA,CAAA,YAAS,CAAC,GAAG,CAAW;QACvC,WAAW,IAAI,KAAK;IACtB;AACF;AAEO,MAAM,YAAY,CAAC;IACxB,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,WAAW,MAAM,CAAC;QAC5B,SAAS,IAAM,iHAAA,CAAA,YAAS,CAAC,GAAG,CAAS,CAAC,UAAU,EAAE,IAAI;QACtD,WAAW,IAAI,KAAK;QACpB,SAAS,CAAC,CAAC,MAAM,OAAO,eAAe,gFAAgF,IAAI,CAAC;IAC9H;AACF;AAEO,MAAM,kBAAkB;IAC7B,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC,OASP,iHAAA,CAAA,YAAS,CAAC,IAAI,CAAS,aAAa;QAC1C,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU,WAAW,KAAK;YAAG;YAC7D,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QAChB;QACA,SAAS,CAAC;YACR,MAAM,UAAU,MAAM,QAAQ,EAAE,MAAM,UAAU;YAChD,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd;IACF;AACF;AAEO,MAAM,kBAAkB,CAAC;IAC9B,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC;YAWX,IAAI,CAAC,MAAM,OAAO,eAAe,CAAC,gFAAgF,IAAI,CAAC,KAAK;gBAC1H,MAAM,IAAI,MAAM;YAClB;YACA,OAAO,iHAAA,CAAA,YAAS,CAAC,KAAK,CAAS,CAAC,UAAU,EAAE,IAAI,EAAE;QACpD;QACA,WAAW,CAAC;YACV,YAAY,YAAY,CAAC,WAAW,MAAM,CAAC,KAAK;YAChD,YAAY,iBAAiB,CAAC;gBAAE,UAAU,WAAW,KAAK;YAAG;YAC7D,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QAChB;QACA,SAAS,CAAC;YACR,MAAM,UAAU,MAAM,QAAQ,EAAE,MAAM,UAAU;YAChD,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd;IACF;AACF;AAEO,MAAM,kBAAkB;IAC7B,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC;YACX,IAAI,CAAC,MAAM,OAAO,eAAe,CAAC,gFAAgF,IAAI,CAAC,KAAK;gBAC1H,MAAM,IAAI,MAAM;YAClB;YACA,OAAO,iHAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,UAAU,EAAE,IAAI;QAC3C;QACA,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU,WAAW,KAAK;YAAG;YAC7D,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QAChB;QACA,SAAS,CAAC;YACR,MAAM,UAAU,MAAM,QAAQ,EAAE,MAAM,UAAU;YAChD,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd;IACF;AACF", "debugId": null}}, {"offset": {"line": 670, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/backend/lc-workflow-frontend/src/components/layout/Sidebar.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { \n  HomeIcon, \n  DocumentTextIcon, \n  UsersIcon, \n  BuildingOfficeIcon, \n  MapPinIcon, \n  Cog6ToothIcon,\n  FolderIcon,\n  ChevronDownIcon,\n  ChevronRightIcon,\n  XMarkIcon,\n  Bars3Icon,\n} from '@heroicons/react/24/outline';\nimport { useAuthContext } from '@/providers/AuthProvider';\n\ninterface NavItem {\n  name: string;\n  href: string;\n  icon: React.ComponentType<{ className?: string }>;\n  requiredRoles?: string[];\n}\n\nconst navigation: NavItem[] = [\n  { name: 'Dashboard', href: '/dashboard', icon: HomeIcon },\n  { name: 'Applications', href: '/applications', icon: DocumentTextIcon },\n  { name: 'Files', href: '/files', icon: FolderIcon },\n];\n\nconst adminNavigation: NavItem[] = [\n  { name: 'Users', href: '/users', icon: UsersIcon, requiredRoles: ['admin', 'manager'] },\n  { name: 'Departments', href: '/departments', icon: BuildingOfficeIcon, requiredRoles: ['admin', 'manager'] },\n  { name: 'Branches', href: '/branches', icon: MapPinIcon, requiredRoles: ['admin', 'manager'] },\n  { name: 'Settings', href: '/settings', icon: Cog6ToothIcon, requiredRoles: ['admin'] },\n];\n\ninterface SidebarProps {\n  isOpen: boolean;\n  onClose: () => void;\n}\n\nexport function Sidebar({ isOpen, onClose }: SidebarProps) {\n  const pathname = usePathname();\n  const { user, isAdmin, isManager } = useAuthContext();\n\n  const hasRequiredRole = (requiredRoles?: string[]) => {\n    if (!requiredRoles) return true;\n    return isAdmin || (isManager && requiredRoles.includes('manager'));\n  };\n\n  const allNavigation = [...navigation, ...adminNavigation];\n\n  const NavLink = ({ item }: { item: NavItem }) => {\n    const isActive = pathname === item.href;\n    \n    if (!hasRequiredRole(item.requiredRoles)) return null;\n\n    return (\n      <Link\n        href={item.href}\n        className={`group flex items-center gap-x-3 rounded-md p-2 text-sm font-semibold leading-6 transition-all duration-200 ${\n          isActive\n            ? 'bg-blue-600 text-white'\n            : 'text-gray-700 hover:bg-gray-50 hover:text-blue-600'\n        }`}\n        onClick={onClose}\n      >\n        <item.icon className=\"h-5 w-5 shrink-0\" />\n        {item.name}\n      </Link>\n    );\n  };\n\n  return (\n    <>\n      {/* Mobile sidebar */}\n      <div\n        className={`fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:hidden ${\n          isOpen ? 'translate-x-0' : '-translate-x-full'\n        }`}\n      >\n        <div className=\"flex h-16 items-center justify-between px-4\">\n          <div className=\"text-xl font-bold text-gray-900\">LC Workflow</div>\n          <button\n            type=\"button\"\n            className=\"-m-2.5 p-2.5 text-gray-700\"\n            onClick={onClose}\n          >\n            <XMarkIcon className=\"h-6 w-6\" />\n          </button>\n        </div>\n        <nav className=\"flex flex-1 flex-col gap-y-2 p-4\">\n          {allNavigation.map((item) => (\n            <NavLink key={item.name} item={item} />\n          ))}\n        </nav>\n      </div>\n\n      {/* Desktop sidebar */}\n      <div className=\"hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-64 lg:flex-col\">\n        <div className=\"flex grow flex-col gap-y-5 overflow-y-auto bg-white px-6 ring-1 ring-gray-200\">\n          <div className=\"flex h-16 shrink-0 items-center\">\n            <div className=\"text-xl font-bold text-gray-900\">LC Workflow</div>\n          </div>\n          <nav className=\"flex flex-1 flex-col gap-y-2\">\n            {allNavigation.map((item) => (\n              <NavLink key={item.name} item={item} />\n            ))}\n          </nav>\n          \n          {/* User info */}\n          {user && (\n            <div className=\"mb-4\">\n              <div className=\"flex items-center gap-x-3 rounded-md p-2 text-sm font-medium text-gray-700\">\n                <div className=\"h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center\">\n                  <span className=\"text-sm font-medium text-gray-600\">\n                    {user.first_name.charAt(0)}{user.last_name.charAt(0)}\n                  </span>\n                </div>\n                <div>\n                  <div className=\"font-medium\">{user.first_name} {user.last_name}</div>\n                  <div className=\"text-xs text-gray-500\">{user.role}</div>\n                </div>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </>\n  );\n}"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AAlBA;;;;;;AA2BA,MAAM,aAAwB;IAC5B;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,+MAAA,CAAA,WAAQ;IAAC;IACxD;QAAE,MAAM;QAAgB,MAAM;QAAiB,MAAM,+NAAA,CAAA,mBAAgB;IAAC;IACtE;QAAE,MAAM;QAAS,MAAM;QAAU,MAAM,mNAAA,CAAA,aAAU;IAAC;CACnD;AAED,MAAM,kBAA6B;IACjC;QAAE,MAAM;QAAS,MAAM;QAAU,MAAM,iNAAA,CAAA,YAAS;QAAE,eAAe;YAAC;YAAS;SAAU;IAAC;IACtF;QAAE,MAAM;QAAe,MAAM;QAAgB,MAAM,mOAAA,CAAA,qBAAkB;QAAE,eAAe;YAAC;YAAS;SAAU;IAAC;IAC3G;QAAE,MAAM;QAAY,MAAM;QAAa,MAAM,mNAAA,CAAA,aAAU;QAAE,eAAe;YAAC;YAAS;SAAU;IAAC;IAC7F;QAAE,MAAM;QAAY,MAAM;QAAa,MAAM,yNAAA,CAAA,gBAAa;QAAE,eAAe;YAAC;SAAQ;IAAC;CACtF;AAOM,SAAS,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAgB;IACvD,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IAElD,MAAM,kBAAkB,CAAC;QACvB,IAAI,CAAC,eAAe,OAAO;QAC3B,OAAO,WAAY,aAAa,cAAc,QAAQ,CAAC;IACzD;IAEA,MAAM,gBAAgB;WAAI;WAAe;KAAgB;IAEzD,MAAM,UAAU,CAAC,EAAE,IAAI,EAAqB;QAC1C,MAAM,WAAW,aAAa,KAAK,IAAI;QAEvC,IAAI,CAAC,gBAAgB,KAAK,aAAa,GAAG,OAAO;QAEjD,qBACE,8OAAC,4JAAA,CAAA,UAAI;YACH,MAAM,KAAK,IAAI;YACf,WAAW,CAAC,2GAA2G,EACrH,WACI,2BACA,sDACJ;YACF,SAAS;;8BAET,8OAAC,KAAK,IAAI;oBAAC,WAAU;;;;;;gBACpB,KAAK,IAAI;;;;;;;IAGhB;IAEA,qBACE;;0BAEE,8OAAC;gBACC,WAAW,CAAC,sHAAsH,EAChI,SAAS,kBAAkB,qBAC3B;;kCAEF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAkC;;;;;;0CACjD,8OAAC;gCACC,MAAK;gCACL,WAAU;gCACV,SAAS;0CAET,cAAA,8OAAC,iNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAGzB,8OAAC;wBAAI,WAAU;kCACZ,cAAc,GAAG,CAAC,CAAC,qBAClB,8OAAC;gCAAwB,MAAM;+BAAjB,KAAK,IAAI;;;;;;;;;;;;;;;;0BAM7B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CAAkC;;;;;;;;;;;sCAEnD,8OAAC;4BAAI,WAAU;sCACZ,cAAc,GAAG,CAAC,CAAC,qBAClB,8OAAC;oCAAwB,MAAM;mCAAjB,KAAK,IAAI;;;;;;;;;;wBAK1B,sBACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;;gDACb,KAAK,UAAU,CAAC,MAAM,CAAC;gDAAI,KAAK,SAAS,CAAC,MAAM,CAAC;;;;;;;;;;;;kDAGtD,8OAAC;;0DACC,8OAAC;gDAAI,WAAU;;oDAAe,KAAK,UAAU;oDAAC;oDAAE,KAAK,SAAS;;;;;;;0DAC9D,8OAAC;gDAAI,WAAU;0DAAyB,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASnE", "debugId": null}}, {"offset": {"line": 951, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/backend/lc-workflow-frontend/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useRef, useEffect } from 'react';\r\nimport { Bars3Icon, BellIcon, UserCircleIcon, ArrowRightOnRectangleIcon, Cog6ToothIcon } from '@heroicons/react/24/outline';\r\nimport { useLogout } from '@/hooks/useAuth';\r\nimport { useAuthContext } from '@/providers/AuthProvider';\r\nimport Link from 'next/link';\r\n\r\ninterface HeaderProps {\r\n  onMenuClick: () => void;\r\n}\r\n\r\nexport function Header({ onMenuClick }: HeaderProps) {\r\n  const logout = useLogout();\r\n  const { user } = useAuthContext();\r\n  const [profileDropdownOpen, setProfileDropdownOpen] = useState(false);\r\n  const dropdownRef = useRef<HTMLDivElement>(null);\r\n\r\n  // Close dropdown when clicking outside\r\n  useEffect(() => {\r\n    function handleClickOutside(event: MouseEvent) {\r\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\r\n        setProfileDropdownOpen(false);\r\n      }\r\n    }\r\n\r\n    document.addEventListener('mousedown', handleClickOutside);\r\n    return () => {\r\n      document.removeEventListener('mousedown', handleClickOutside);\r\n    };\r\n  }, []);\r\n\r\n  return (\r\n    <div className=\"sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 bg-white px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8\">\r\n      <button\r\n        type=\"button\"\r\n        className=\"-m-2.5 p-2.5 text-gray-700 lg:hidden\"\r\n        onClick={onMenuClick}\r\n      >\r\n        <span className=\"sr-only\">Open sidebar</span>\r\n        <Bars3Icon className=\"h-6 w-6\" aria-hidden=\"true\" />\r\n      </button>\r\n\r\n      {/* Separator */}\r\n      <div className=\"h-6 w-px bg-gray-900/10 lg:hidden\" aria-hidden=\"true\" />\r\n\r\n      <div className=\"flex flex-1 gap-x-4 self-stretch lg:gap-x-6\">\r\n        <div className=\"flex flex-1\" />\r\n\r\n        <div className=\"flex items-center gap-x-4 lg:gap-x-6\">\r\n          <button\r\n            type=\"button\"\r\n            className=\"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500\"\r\n          >\r\n            <span className=\"sr-only\">View notifications</span>\r\n            <BellIcon className=\"h-6 w-6\" aria-hidden=\"true\" />\r\n          </button>\r\n\r\n          {/* Separator */}\r\n          <div className=\"hidden lg:block lg:h-6 lg:w-px lg:bg-gray-900/10\" aria-hidden=\"true\" />\r\n\r\n          {/* Profile dropdown */}\r\n          <div className=\"relative\">\r\n            <button\r\n              type=\"button\"\r\n              className=\"-m-1.5 flex items-center p-1.5\"\r\n              id=\"user-menu-button\"\r\n              aria-expanded=\"false\"\r\n              aria-haspopup=\"true\"\r\n              onClick={() => setProfileDropdownOpen(!profileDropdownOpen)}\r\n            >\r\n              <span className=\"sr-only\">Open user menu</span>\r\n              <div className=\"h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center\">\r\n                <span className=\"text-sm font-medium text-gray-600\">\r\n                  {user?.first_name?.charAt(0)}{user?.last_name?.charAt(0)}\r\n                </span>\r\n              </div>\r\n              <span className=\"hidden lg:flex lg:items-center\">\r\n                <span className=\"ml-4 text-sm leading-6 text-gray-900\" aria-hidden=\"true\">\r\n                  <span className=\"font-semibold\">{user?.first_name} {user?.last_name}</span>\r\n                  {user?.position?.title ? (\r\n                    <span className=\"ml-2 text-gray-500 truncate max-w-[10rem]\">\r\n                      • {user?.position?.title}\r\n                    </span>\r\n                  ) : null}\r\n                </span>\r\n              </span>\r\n            </button>\r\n\r\n            {/* Dropdown menu */}\r\n            {profileDropdownOpen && (\r\n              <div\r\n                className=\"absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none\"\r\n                role=\"menu\"\r\n                aria-orientation=\"vertical\"\r\n                aria-labelledby=\"user-menu-button\"\r\n                ref={dropdownRef}\r\n              >\r\n                <div className=\"px-4 py-3\">\r\n                  <p className=\"text-sm\">Signed in as</p>\r\n                  <p className=\"truncate text-sm font-medium text-gray-900\">{user?.email}</p>\r\n                </div>\r\n                <div className=\"border-t border-gray-100\"></div>\r\n                <Link\r\n                  href=\"/profile\"\r\n                  className=\"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\r\n                  role=\"menuitem\"\r\n                  onClick={() => setProfileDropdownOpen(false)}\r\n                >\r\n                  <UserCircleIcon className=\"mr-3 h-5 w-5 text-gray-400\" aria-hidden=\"true\" />\r\n                  Your Profile\r\n                </Link>\r\n                <Link\r\n                  href=\"/settings\"\r\n                  className=\"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\r\n                  role=\"menuitem\"\r\n                  onClick={() => setProfileDropdownOpen(false)}\r\n                >\r\n                  <Cog6ToothIcon className=\"mr-3 h-5 w-5 text-gray-400\" aria-hidden=\"true\" />\r\n                  Settings\r\n                </Link>\r\n                <div className=\"border-t border-gray-100\"></div>\r\n                <button\r\n                  type=\"button\"\r\n                  className=\"flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\r\n                  role=\"menuitem\"\r\n                  onClick={() => {\r\n                    setProfileDropdownOpen(false);\r\n                    logout.mutate();\r\n                  }}\r\n                >\r\n                  <ArrowRightOnRectangleIcon className=\"mr-3 h-5 w-5 text-gray-400\" aria-hidden=\"true\" />\r\n                  Sign out\r\n                </button>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AANA;;;;;;;AAYO,SAAS,OAAO,EAAE,WAAW,EAAe;IACjD,MAAM,SAAS,CAAA,GAAA,uHAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IAC9B,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE3C,uCAAuC;IACvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS,mBAAmB,KAAiB;YAC3C,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;gBAC9E,uBAAuB;YACzB;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;QAC5C;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBACC,MAAK;gBACL,WAAU;gBACV,SAAS;;kCAET,8OAAC;wBAAK,WAAU;kCAAU;;;;;;kCAC1B,8OAAC,iNAAA,CAAA,YAAS;wBAAC,WAAU;wBAAU,eAAY;;;;;;;;;;;;0BAI7C,8OAAC;gBAAI,WAAU;gBAAoC,eAAY;;;;;;0BAE/D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCAEf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,MAAK;gCACL,WAAU;;kDAEV,8OAAC;wCAAK,WAAU;kDAAU;;;;;;kDAC1B,8OAAC,+MAAA,CAAA,WAAQ;wCAAC,WAAU;wCAAU,eAAY;;;;;;;;;;;;0CAI5C,8OAAC;gCAAI,WAAU;gCAAmD,eAAY;;;;;;0CAG9E,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,WAAU;wCACV,IAAG;wCACH,iBAAc;wCACd,iBAAc;wCACd,SAAS,IAAM,uBAAuB,CAAC;;0DAEvC,8OAAC;gDAAK,WAAU;0DAAU;;;;;;0DAC1B,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;;wDACb,MAAM,YAAY,OAAO;wDAAI,MAAM,WAAW,OAAO;;;;;;;;;;;;0DAG1D,8OAAC;gDAAK,WAAU;0DACd,cAAA,8OAAC;oDAAK,WAAU;oDAAuC,eAAY;;sEACjE,8OAAC;4DAAK,WAAU;;gEAAiB,MAAM;gEAAW;gEAAE,MAAM;;;;;;;wDACzD,MAAM,UAAU,sBACf,8OAAC;4DAAK,WAAU;;gEAA4C;gEACvD,MAAM,UAAU;;;;;;mEAEnB;;;;;;;;;;;;;;;;;;oCAMT,qCACC,8OAAC;wCACC,WAAU;wCACV,MAAK;wCACL,oBAAiB;wCACjB,mBAAgB;wCAChB,KAAK;;0DAEL,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAU;;;;;;kEACvB,8OAAC;wDAAE,WAAU;kEAA8C,MAAM;;;;;;;;;;;;0DAEnE,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,MAAK;gDACL,SAAS,IAAM,uBAAuB;;kEAEtC,8OAAC,2NAAA,CAAA,iBAAc;wDAAC,WAAU;wDAA6B,eAAY;;;;;;oDAAS;;;;;;;0DAG9E,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,MAAK;gDACL,SAAS,IAAM,uBAAuB;;kEAEtC,8OAAC,yNAAA,CAAA,gBAAa;wDAAC,WAAU;wDAA6B,eAAY;;;;;;oDAAS;;;;;;;0DAG7E,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDACC,MAAK;gDACL,WAAU;gDACV,MAAK;gDACL,SAAS;oDACP,uBAAuB;oDACvB,OAAO,MAAM;gDACf;;kEAEA,8OAAC,iPAAA,CAAA,4BAAyB;wDAAC,WAAU;wDAA6B,eAAY;;;;;;oDAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUzG", "debugId": null}}, {"offset": {"line": 1303, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/backend/lc-workflow-frontend/src/components/layout/Layout.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Sidebar } from './Sidebar';\nimport { Header } from './Header';\n\ninterface LayoutProps {\n  children: React.ReactNode;\n}\n\nexport function Layout({ children }: LayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n\n  return (\n    <>\n      <div className=\"min-h-full\">\n        <Sidebar \n          isOpen={sidebarOpen} \n          onClose={() => setSidebarOpen(false)} \n        />\n        \n        <div className=\"lg:pl-64\">\n          <Header onMenuClick={() => setSidebarOpen(true)} />\n          \n          <main className=\"py-10\">\n            <div className=\"px-4 sm:px-6 lg:px-8\">\n              {children}\n            </div>\n          </main>\n        </div>\n      </div>\n    </>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAUO,SAAS,OAAO,EAAE,QAAQ,EAAe;IAC9C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,qBACE;kBACE,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,uIAAA,CAAA,UAAO;oBACN,QAAQ;oBACR,SAAS,IAAM,eAAe;;;;;;8BAGhC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,sIAAA,CAAA,SAAM;4BAAC,aAAa,IAAM,eAAe;;;;;;sCAE1C,8OAAC;4BAAK,WAAU;sCACd,cAAA,8OAAC;gCAAI,WAAU;0CACZ;;;;;;;;;;;;;;;;;;;;;;;;AAOf", "debugId": null}}, {"offset": {"line": 1372, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/backend/lc-workflow-frontend/src/components/auth/ProtectedRoute.tsx"], "sourcesContent": ["'use client';\n\nimport { useRouter } from 'next/navigation';\nimport { useAuthContext } from '@/providers/AuthProvider';\nimport { useEffect } from 'react';\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode;\n  requiredRoles?: string[];\n}\n\nexport function ProtectedRoute({ children, requiredRoles }: ProtectedRouteProps) {\n  const router = useRouter();\n  const { isAuthenticated, isLoading, user, role } = useAuthContext();\n\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      if (!isLoading) {\n        if (!isAuthenticated) {\n          router.push('/login');\n        } else if (requiredRoles && requiredRoles.length > 0 && !requiredRoles.includes(role || '')) {\n          router.push('/unauthorized');\n        }\n      }\n    }, 1000); // Wait 1 second for auth state to stabilize\n\n    return () => clearTimeout(timer);\n  }, [isAuthenticated, isLoading, requiredRoles, role, router]);\n\n  if (isLoading || !isAuthenticated) {\n    return (\n      <div className=\"flex min-h-screen items-center justify-center\">\n        <div className=\"h-8 w-8 animate-spin rounded-full border-4 border-blue-600 border-t-transparent\"></div>\n      </div>\n    );\n  }\n\n  if (requiredRoles && requiredRoles.length > 0 && !requiredRoles.includes(role || '')) {\n    return null;\n  }\n\n  return <>{children}</>;\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAWO,SAAS,eAAe,EAAE,QAAQ,EAAE,aAAa,EAAuB;IAC7E,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IAEhE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,WAAW;YACvB,IAAI,CAAC,WAAW;gBACd,IAAI,CAAC,iBAAiB;oBACpB,OAAO,IAAI,CAAC;gBACd,OAAO,IAAI,iBAAiB,cAAc,MAAM,GAAG,KAAK,CAAC,cAAc,QAAQ,CAAC,QAAQ,KAAK;oBAC3F,OAAO,IAAI,CAAC;gBACd;YACF;QACF,GAAG,OAAO,4CAA4C;QAEtD,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;QAAiB;QAAW;QAAe;QAAM;KAAO;IAE5D,IAAI,aAAa,CAAC,iBAAiB;QACjC,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,iBAAiB,cAAc,MAAM,GAAG,KAAK,CAAC,cAAc,QAAQ,CAAC,QAAQ,KAAK;QACpF,OAAO;IACT;IAEA,qBAAO;kBAAG;;AACZ", "debugId": null}}, {"offset": {"line": 1432, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/backend/lc-workflow-frontend/src/app/users/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useUsers, useDeleteUser } from '@/hooks/useUsers';\nimport { useDepartments } from '@/hooks/useDepartments';\nimport { useBranches } from '@/hooks/useBranches';\nimport { User } from '@/types/models';\nimport { \n  Plus, \n  Search, \n  Filter, \n  Edit, \n  Trash2, \n  Eye,\n  ChevronLeft,\n  ChevronRight,\n  Users as UsersIcon\n} from 'lucide-react';\nimport Link from 'next/link';\nimport { useRouter } from 'next/navigation';\nimport { Layout } from '@/components/layout/Layout';\nimport { ProtectedRoute } from '@/components/auth/ProtectedRoute';\n\nexport default function UsersPage() {\n  const router = useRouter();\n  const [page, setPage] = useState(1);\n  const [search, setSearch] = useState('');\n  const [roleFilter, setRoleFilter] = useState('');\n  const [departmentFilter, setDepartmentFilter] = useState('');\n  const [branchFilter, setBranchFilter] = useState('');\n  const [showFilters, setShowFilters] = useState(false);\n\n  const { data: usersData, isLoading, error } = useUsers({\n    page,\n    size: 10,\n    search: search || undefined,\n    role: roleFilter || undefined,\n    department_id: departmentFilter || undefined,\n    branch_id: branchFilter || undefined,\n  });\n\n  const { data: departmentsData } = useDepartments({ size: 100 });\n  const { data: branchesData } = useBranches({ size: 100 });\n  const deleteUser = useDeleteUser();\n\n  const handleDelete = async (id: string) => {\n    if (window.confirm('Are you sure you want to delete this user?')) {\n      deleteUser.mutate(id);\n    }\n  };\n\n  const handleSearch = (e: React.FormEvent) => {\n    e.preventDefault();\n    setPage(1);\n  };\n\n  const clearFilters = () => {\n    setSearch('');\n    setRoleFilter('');\n    setDepartmentFilter('');\n    setBranchFilter('');\n    setPage(1);\n  };\n\n  if (error) {\n    return (\n      <ProtectedRoute>\n        <Layout>\n          <div className=\"flex items-center justify-center h-full\">\n            <div className=\"text-center\">\n              <h1 className=\"text-2xl font-bold text-gray-900 mb-2\">Error Loading Users</h1>\n              <p className=\"text-gray-600\">Please try again later.</p>\n            </div>\n          </div>\n        </Layout>\n      </ProtectedRoute>\n    );\n  }\n\n  return (\n    <ProtectedRoute>\n      <Layout>\n        {/* Header */}\n        <div className=\"mb-8\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-3\">\n              <UsersIcon className=\"h-8 w-8 text-blue-600\" />\n              <div>\n                <h1 className=\"text-3xl font-bold text-gray-900\">Users Management</h1>\n                <p className=\"text-gray-600\">Manage system users and their permissions</p>\n              </div>\n            </div>\n            <Link\n              href=\"/users/new\"\n              className=\"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n            >\n              <Plus className=\"h-5 w-5 mr-2\" />\n              Add User\n            </Link>\n          </div>\n        </div>\n\n        {/* Search and Filters */}\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6\">\n          <form onSubmit={handleSearch} className=\"space-y-4\">\n            <div className=\"flex flex-col sm:flex-row gap-4\">\n              <div className=\"flex-1\">\n                <div className=\"relative\">\n                  <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400\" />\n                  <input\n                    type=\"text\"\n                    placeholder=\"Search users by name, email, or username...\"\n                    value={search}\n                    onChange={(e) => setSearch(e.target.value)}\n                    className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  />\n                </div>\n              </div>\n              <button\n                type=\"button\"\n                onClick={() => setShowFilters(!showFilters)}\n                className=\"inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50\"\n              >\n                <Filter className=\"h-5 w-5 mr-2\" />\n                Filters\n              </button>\n            </div>\n\n            {showFilters && (\n              <div className=\"grid grid-cols-1 sm:grid-cols-3 gap-4 pt-4 border-t border-gray-200\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">Role</label>\n                  <select\n                    value={roleFilter}\n                    onChange={(e) => setRoleFilter(e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  >\n                    <option value=\"\">All Roles</option>\n                    <option value=\"admin\">Admin</option>\n                    <option value=\"manager\">Manager</option>\n                    <option value=\"officer\">Officer</option>\n                  </select>\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">Department</label>\n                  <select\n                    value={departmentFilter}\n                    onChange={(e) => setDepartmentFilter(e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  >\n                    <option value=\"\">All Departments</option>\n                    {departmentsData?.items?.map((dept) => (\n                      <option key={dept.id} value={dept.id}>\n                        {dept.name}\n                      </option>\n                    ))}\n                  </select>\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">Branch</label>\n                  <select\n                    value={branchFilter}\n                    onChange={(e) => setBranchFilter(e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  >\n                    <option value=\"\">All Branches</option>\n                    {branchesData?.items?.map((branch) => (\n                      <option key={branch.id} value={branch.id}>\n                        {branch.name}\n                      </option>\n                    ))}\n                  </select>\n                </div>\n              </div>\n            )}\n\n            <div className=\"flex justify-between\">\n              <button\n                type=\"submit\"\n                className=\"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n              >\n                Search\n              </button>\n              {(search || roleFilter || departmentFilter || branchFilter) && (\n                <button\n                  type=\"button\"\n                  onClick={clearFilters}\n                  className=\"px-4 py-2 text-gray-600 hover:text-gray-800\"\n                >\n                  Clear Filters\n                </button>\n              )}\n            </div>\n          </form>\n        </div>\n\n        {/* Users Table */}\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden\">\n          {isLoading ? (\n            <div className=\"p-8 text-center\">\n              <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto\"></div>\n              <p className=\"mt-2 text-gray-600\">Loading users...</p>\n            </div>\n          ) : usersData?.items?.length === 0 ? (\n            <div className=\"p-8 text-center\">\n              <UsersIcon className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n              <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No users found</h3>\n              <p className=\"text-gray-600\">Try adjusting your search criteria or add a new user.</p>\n            </div>\n          ) : (\n            <>\n              <div className=\"overflow-x-auto\">\n                <table className=\"min-w-full divide-y divide-gray-200\">\n                  <thead className=\"bg-gray-50\">\n                    <tr>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        User\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Role\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Department\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Status\n                      </th>\n                      <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Last Login\n                      </th>\n                      <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Actions\n                      </th>\n                    </tr>\n                  </thead>\n                  <tbody className=\"bg-white divide-y divide-gray-200\">\n                    {usersData?.items?.map((user: User) => (\n                      <tr key={user.id} className=\"hover:bg-gray-50\">\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <div className=\"flex items-center\">\n                            <div className=\"h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center\">\n                              <span className=\"text-blue-600 font-medium\">\n                                {user.first_name?.[0]}{user.last_name?.[0]}\n                              </span>\n                            </div>\n                            <div className=\"ml-4\">\n                              <div className=\"text-sm font-medium text-gray-900\">\n                                {user.first_name} {user.last_name}\n                              </div>\n                              <div className=\"text-sm text-gray-500\">{user.email}</div>\n                              <div className=\"text-xs text-gray-400\">@{user.username}</div>\n                            </div>\n                          </div>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${\n                            user.role === 'admin' \n                              ? 'bg-purple-100 text-purple-800'\n                              : user.role === 'manager'\n                              ? 'bg-blue-100 text-blue-800'\n                              : 'bg-green-100 text-green-800'\n                          }`}>\n                            {user.role}\n                          </span>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                          {user.department_id ? (\n                            departmentsData?.items?.find(d => d.id === user.department_id)?.name || 'Unknown'\n                          ) : (\n                            <span className=\"text-gray-400\">Not assigned</span>\n                          )}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${\n                            user.status === 'active' \n                              ? 'bg-green-100 text-green-800'\n                              : 'bg-red-100 text-red-800'\n                          }`}>\n                            {user.status}\n                          </span>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                          {user.last_login_at \n                            ? new Date(user.last_login_at).toLocaleDateString()\n                            : 'Never'\n                          }\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\n                          <div className=\"flex items-center justify-end space-x-2\">\n                            <button\n                              onClick={() => router.push(`/users/${user.id}`)}\n                              className=\"text-blue-600 hover:text-blue-900 p-1\"\n                              title=\"View Details\"\n                            >\n                              <Eye className=\"h-4 w-4\" />\n                            </button>\n                            <button\n                              onClick={() => router.push(`/users/${user.id}/edit`)}\n                              className=\"text-indigo-600 hover:text-indigo-900 p-1\"\n                              title=\"Edit User\"\n                            >\n                              <Edit className=\"h-4 w-4\" />\n                            </button>\n                            <button\n                              onClick={() => handleDelete(user.id)}\n                              className=\"text-red-600 hover:text-red-900 p-1\"\n                              title=\"Delete User\"\n                            >\n                              <Trash2 className=\"h-4 w-4\" />\n                            </button>\n                          </div>\n                        </td>\n                      </tr>\n                    ))}\n                  </tbody>\n                </table>\n              </div>\n\n              {/* Pagination */}\n              {usersData && usersData.pages > 1 && (\n                <div className=\"bg-white px-4 py-3 border-t border-gray-200 sm:px-6\">\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"flex-1 flex justify-between sm:hidden\">\n                      <button\n                        onClick={() => setPage(Math.max(1, page - 1))}\n                        disabled={page === 1}\n                        className=\"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50\"\n                      >\n                        Previous\n                      </button>\n                      <button\n                        onClick={() => setPage(Math.min(usersData.pages, page + 1))}\n                        disabled={page === usersData.pages}\n                        className=\"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50\"\n                      >\n                        Next\n                      </button>\n                    </div>\n                    <div className=\"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between\">\n                      <div>\n                        <p className=\"text-sm text-gray-700\">\n                          Showing{' '}\n                          <span className=\"font-medium\">{(page - 1) * 10 + 1}</span>\n                          {' '}to{' '}\n                          <span className=\"font-medium\">\n                            {Math.min(page * 10, usersData.total)}\n                          </span>\n                          {' '}of{' '}\n                          <span className=\"font-medium\">{usersData.total}</span>\n                          {' '}results\n                        </p>\n                      </div>\n                      <div>\n                        <nav className=\"relative z-0 inline-flex rounded-md shadow-sm -space-x-px\">\n                          <button\n                            onClick={() => setPage(Math.max(1, page - 1))}\n                            disabled={page === 1}\n                            className=\"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50\"\n                          >\n                            <ChevronLeft className=\"h-5 w-5\" />\n                          </button>\n                          <span className=\"relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700\">\n                            {page} of {usersData.pages}\n                          </span>\n                          <button\n                            onClick={() => setPage(Math.min(usersData.pages, page + 1))}\n                            disabled={page === usersData.pages}\n                            className=\"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50\"\n                          >\n                            <ChevronRight className=\"h-5 w-5\" />\n                          </button>\n                        </nav>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              )}\n            </>\n          )}\n        </div>\n      </Layout>\n    </ProtectedRoute>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;AACA;AACA;AArBA;;;;;;;;;;;AAuBe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,EAAE,MAAM,SAAS,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD,EAAE;QACrD;QACA,MAAM;QACN,QAAQ,UAAU;QAClB,MAAM,cAAc;QACpB,eAAe,oBAAoB;QACnC,WAAW,gBAAgB;IAC7B;IAEA,MAAM,EAAE,MAAM,eAAe,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD,EAAE;QAAE,MAAM;IAAI;IAC7D,MAAM,EAAE,MAAM,YAAY,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,cAAW,AAAD,EAAE;QAAE,MAAM;IAAI;IACvD,MAAM,aAAa,CAAA,GAAA,wHAAA,CAAA,gBAAa,AAAD;IAE/B,MAAM,eAAe,OAAO;QAC1B,IAAI,OAAO,OAAO,CAAC,+CAA+C;YAChE,WAAW,MAAM,CAAC;QACpB;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,QAAQ;IACV;IAEA,MAAM,eAAe;QACnB,UAAU;QACV,cAAc;QACd,oBAAoB;QACpB,gBAAgB;QAChB,QAAQ;IACV;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC,4IAAA,CAAA,iBAAc;sBACb,cAAA,8OAAC,sIAAA,CAAA,SAAM;0BACL,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CACtD,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;IAMzC;IAEA,qBACE,8OAAC,4IAAA,CAAA,iBAAc;kBACb,cAAA,8OAAC,sIAAA,CAAA,SAAM;;8BAEL,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oMAAA,CAAA,QAAS;wCAAC,WAAU;;;;;;kDACrB,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAmC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;;;;;;;0CAGjC,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;8BAOvC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAK,UAAU;wBAAc,WAAU;;0CACtC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;oDACC,MAAK;oDACL,aAAY;oDACZ,OAAO;oDACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;oDACzC,WAAU;;;;;;;;;;;;;;;;;kDAIhB,8OAAC;wCACC,MAAK;wCACL,SAAS,IAAM,eAAe,CAAC;wCAC/B,WAAU;;0DAEV,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;4BAKtC,6BACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAChE,8OAAC;gDACC,OAAO;gDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gDAC7C,WAAU;;kEAEV,8OAAC;wDAAO,OAAM;kEAAG;;;;;;kEACjB,8OAAC;wDAAO,OAAM;kEAAQ;;;;;;kEACtB,8OAAC;wDAAO,OAAM;kEAAU;;;;;;kEACxB,8OAAC;wDAAO,OAAM;kEAAU;;;;;;;;;;;;;;;;;;kDAG5B,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAChE,8OAAC;gDACC,OAAO;gDACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;gDACnD,WAAU;;kEAEV,8OAAC;wDAAO,OAAM;kEAAG;;;;;;oDAChB,iBAAiB,OAAO,IAAI,CAAC,qBAC5B,8OAAC;4DAAqB,OAAO,KAAK,EAAE;sEACjC,KAAK,IAAI;2DADC,KAAK,EAAE;;;;;;;;;;;;;;;;;kDAM1B,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAChE,8OAAC;gDACC,OAAO;gDACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;gDAC/C,WAAU;;kEAEV,8OAAC;wDAAO,OAAM;kEAAG;;;;;;oDAChB,cAAc,OAAO,IAAI,CAAC,uBACzB,8OAAC;4DAAuB,OAAO,OAAO,EAAE;sEACrC,OAAO,IAAI;2DADD,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;0CAShC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,WAAU;kDACX;;;;;;oCAGA,CAAC,UAAU,cAAc,oBAAoB,YAAY,mBACxD,8OAAC;wCACC,MAAK;wCACL,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;8BAST,8OAAC;oBAAI,WAAU;8BACZ,0BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;+BAElC,WAAW,OAAO,WAAW,kBAC/B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,oMAAA,CAAA,QAAS;gCAAC,WAAU;;;;;;0CACrB,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;6CAG/B;;0CACE,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAM,WAAU;;sDACf,8OAAC;4CAAM,WAAU;sDACf,cAAA,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,8OAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,8OAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,8OAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,8OAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,8OAAC;wDAAG,WAAU;kEAAkF;;;;;;;;;;;;;;;;;sDAKpG,8OAAC;4CAAM,WAAU;sDACd,WAAW,OAAO,IAAI,CAAC,qBACtB,8OAAC;oDAAiB,WAAU;;sEAC1B,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC;4EAAK,WAAU;;gFACb,KAAK,UAAU,EAAE,CAAC,EAAE;gFAAE,KAAK,SAAS,EAAE,CAAC,EAAE;;;;;;;;;;;;kFAG9C,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,WAAU;;oFACZ,KAAK,UAAU;oFAAC;oFAAE,KAAK,SAAS;;;;;;;0FAEnC,8OAAC;gFAAI,WAAU;0FAAyB,KAAK,KAAK;;;;;;0FAClD,8OAAC;gFAAI,WAAU;;oFAAwB;oFAAE,KAAK,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;sEAI5D,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC;gEAAK,WAAW,CAAC,yDAAyD,EACzE,KAAK,IAAI,KAAK,UACV,kCACA,KAAK,IAAI,KAAK,YACd,8BACA,+BACJ;0EACC,KAAK,IAAI;;;;;;;;;;;sEAGd,8OAAC;4DAAG,WAAU;sEACX,KAAK,aAAa,GACjB,iBAAiB,OAAO,KAAK,CAAA,IAAK,EAAE,EAAE,KAAK,KAAK,aAAa,GAAG,QAAQ,0BAExE,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;;;;;;sEAGpC,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC;gEAAK,WAAW,CAAC,yDAAyD,EACzE,KAAK,MAAM,KAAK,WACZ,gCACA,2BACJ;0EACC,KAAK,MAAM;;;;;;;;;;;sEAGhB,8OAAC;4DAAG,WAAU;sEACX,KAAK,aAAa,GACf,IAAI,KAAK,KAAK,aAAa,EAAE,kBAAkB,KAC/C;;;;;;sEAGN,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEACC,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;wEAC9C,WAAU;wEACV,OAAM;kFAEN,cAAA,8OAAC,gMAAA,CAAA,MAAG;4EAAC,WAAU;;;;;;;;;;;kFAEjB,8OAAC;wEACC,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,CAAC,KAAK,CAAC;wEACnD,WAAU;wEACV,OAAM;kFAEN,cAAA,8OAAC,2MAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;;;;;;kFAElB,8OAAC;wEACC,SAAS,IAAM,aAAa,KAAK,EAAE;wEACnC,WAAU;wEACV,OAAM;kFAEN,cAAA,8OAAC,0MAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;mDAvEjB,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;4BAkFvB,aAAa,UAAU,KAAK,GAAG,mBAC9B,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,SAAS,IAAM,QAAQ,KAAK,GAAG,CAAC,GAAG,OAAO;oDAC1C,UAAU,SAAS;oDACnB,WAAU;8DACX;;;;;;8DAGD,8OAAC;oDACC,SAAS,IAAM,QAAQ,KAAK,GAAG,CAAC,UAAU,KAAK,EAAE,OAAO;oDACxD,UAAU,SAAS,UAAU,KAAK;oDAClC,WAAU;8DACX;;;;;;;;;;;;sDAIH,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DACC,cAAA,8OAAC;wDAAE,WAAU;;4DAAwB;4DAC3B;0EACR,8OAAC;gEAAK,WAAU;0EAAe,CAAC,OAAO,CAAC,IAAI,KAAK;;;;;;4DAChD;4DAAI;4DAAG;0EACR,8OAAC;gEAAK,WAAU;0EACb,KAAK,GAAG,CAAC,OAAO,IAAI,UAAU,KAAK;;;;;;4DAErC;4DAAI;4DAAG;0EACR,8OAAC;gEAAK,WAAU;0EAAe,UAAU,KAAK;;;;;;4DAC7C;4DAAI;;;;;;;;;;;;8DAGT,8OAAC;8DACC,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEACC,SAAS,IAAM,QAAQ,KAAK,GAAG,CAAC,GAAG,OAAO;gEAC1C,UAAU,SAAS;gEACnB,WAAU;0EAEV,cAAA,8OAAC,oNAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;;;;;;0EAEzB,8OAAC;gEAAK,WAAU;;oEACb;oEAAK;oEAAK,UAAU,KAAK;;;;;;;0EAE5B,8OAAC;gEACC,SAAS,IAAM,QAAQ,KAAK,GAAG,CAAC,UAAU,KAAK,EAAE,OAAO;gEACxD,UAAU,SAAS,UAAU,KAAK;gEAClC,WAAU;0EAEV,cAAA,8OAAC,sNAAA,CAAA,eAAY;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAcpD", "debugId": null}}]}