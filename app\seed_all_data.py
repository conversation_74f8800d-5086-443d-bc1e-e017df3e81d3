import asyncio
from datetime import date, datetime
import random
import uuid
from typing import Optional

from faker import Faker
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.database import AsyncSessionLocal
from app.models import (
    Branch,
    CustomerApplication,
    Department,
    File,
    Position,
    Setting,
    User,
)

fake = Faker()

async def seed_departments(session: AsyncSession, count: int = 5) -> list[Department]:
    departments = []
    for _ in range(count):
        name = fake.unique.company_suffix() + " Department"
        code = "".join(random.choices("ABCDEFGHIJKLMNOPQRSTUVWXYZ", k=3)) + str(random.randint(10, 99))
        description = fake.catch_phrase()
        
        department = await session.execute(select(Department).where(Department.name == name))
        if department.scalar_one_or_none():
            continue

        new_department = Department(
            name=name,
            code=code,
            description=description,
        )
        session.add(new_department)
        departments.append(new_department)
    await session.flush()
    return departments

async def seed_branches(session: AsyncSession, count: int = 3) -> list[Branch]:
    branches = []
    for _ in range(count):
        name = fake.unique.company() + " Branch"
        code = "".join(random.choices("ABCDEFGHIJKLMNOPQRSTUVWXYZ", k=2)) + str(random.randint(100, 999))
        address = fake.address()
        phone_number = fake.phone_number()
        email = fake.unique.email()
        latitude = fake.latitude()
        longitude = fake.longitude()

        branch = await session.execute(select(Branch).where(Branch.name == name))
        if branch.scalar_one_or_none():
            continue

        new_branch = Branch(
            name=name,
            code=code,
            address=address,
            phone_number=phone_number,
            email=email,
            latitude=latitude,
            longitude=longitude,
        )
        session.add(new_branch)
        branches.append(new_branch)
    await session.flush()
    return branches

async def seed_settings(session: AsyncSession, users: list[User]) -> list[Setting]:
    settings_data = [
        {"key": "app_name", "value": "LoanFlow", "category": "general", "description": "Name of the application", "is_public": True},
        {"key": "max_loan_amount", "value": 100000.00, "category": "loan", "description": "Maximum allowed loan amount", "is_public": True},
        {"key": "min_interest_rate", "value": 0.05, "category": "loan", "description": "Minimum interest rate for loans", "is_public": True},
        {"key": "allow_new_registrations", "value": True, "category": "security", "description": "Allow new user registrations", "is_public": False},
        {"key": "default_workflow_stage", "value": "draft", "category": "workflow", "description": "Default stage for new applications", "is_public": True},
    ]

    settings = []
    for data in settings_data:
        setting = await session.execute(select(Setting).where(Setting.key == data["key"]))
        if setting.scalar_one_or_none():
            continue
        
        new_setting = Setting(
            key=data["key"],
            value=data["value"],
            category=data["category"],
            description=data["description"],
            is_public=data["is_public"],
            created_by=random.choice(users).id if users else None,
            updated_by=random.choice(users).id if users else None,
        )
        session.add(new_setting)
        settings.append(new_setting)
    await session.flush()
    return settings

async def seed_positions(session: AsyncSession, departments: list[Department], branches: list[Branch]) -> list[Position]:
    positions_data = [
        {"title": "Loan Officer", "code": "LOAN_OFFICER", "description": "Manages loan applications", "level": 2},
        {"title": "Credit Analyst", "code": "CREDIT_ANALYST", "description": "Analyzes creditworthiness", "level": 3},
        {"title": "Branch Manager", "code": "BRANCH_MANAGER", "description": "Oversees branch operations", "level": 4},
        {"title": "Department Head", "code": "DEPT_HEAD", "description": "Leads a specific department", "level": 5},
        {"title": "IT Administrator", "code": "IT_ADMIN", "description": "Administrative IT position", "level": 1},
    ]

    positions = []
    for data in positions_data:
        position = await session.execute(select(Position).where(Position.code == data["code"]))
        if position.scalar_one_or_none():
            continue

        new_position = Position(
            title=data["title"],
            code=data["code"],
            description=data["description"],
            level=data["level"],
            department_id=random.choice(departments).id if departments else None,
            branch_id=random.choice(branches).id if branches else None,
        )
        session.add(new_position)
        positions.append(new_position)
    await session.flush()
    return positions

async def seed_users(session: AsyncSession, departments: list[Department], branches: list[Branch], positions: list[Position], count: int = 10) -> list[User]:
    users = []

    # Handle the admin user with employee_id '0001'
    admin_employee_id = "0001"
    admin_user_exists = await session.execute(select(User).where(User.employee_id == admin_employee_id))
    if not admin_user_exists.scalar_one_or_none():
        # Create admin user if not exists
        admin_username = "admin"
        admin_email = "<EMAIL>" # Use a fixed email for the admin
        admin_user_obj = User(
            username=admin_username,
            email=admin_email,
            password_hash="hashed_admin_password", # Use a fixed password hash for admin
            first_name="Super",
            last_name="Admin",
            phone_number=fake.phone_number()[:20],
            employee_id=admin_employee_id,
            role="admin",
            status="active",
            department_id=random.choice(departments).id if departments else None,
            branch_id=random.choice(branches).id if branches else None,
            position_id=random.choice(positions).id if positions else None,
            profile_image_url=fake.image_url(),
            last_login_at=fake.date_time_this_year(),
        )
        session.add(admin_user_obj)
        await session.flush() # Flush to get the ID for relationships
        users.append(admin_user_obj)
    else:
        # Fetch the existing admin user
        admin_user_obj = admin_user_exists.scalar_one()
        users.append(admin_user_obj)
        print(f"Admin user with employee_id '{admin_employee_id}' already exists. Skipping creation.")

    # Seed additional users
    for _ in range(count - 1): # Adjust count for the already seeded admin
        username = fake.unique.user_name()
        email = fake.unique.email()
        password_hash = "hashed_password"
        first_name = fake.first_name()
        last_name = fake.last_name()
        phone_number = fake.phone_number()[:20]
        employee_id = fake.unique.numerify('####') # Ensure unique for other users
        role = random.choice(["officer", "reviewer", "manager"])
        status = random.choice(["active", "inactive"])
        
        # Check if username already exists to avoid UniqueViolationError
        user_exists = await session.execute(select(User).where(User.username == username))
        if user_exists.scalar_one_or_none():
            continue

        new_user = User(
            username=username,
            email=email,
            password_hash=password_hash,
            first_name=first_name,
            last_name=last_name,
            phone_number=phone_number,
            employee_id=employee_id,
            role=role,
            status=status,
            department_id=random.choice(departments).id if departments else None,
            branch_id=random.choice(branches).id if branches else None,
            position_id=random.choice(positions).id if positions else None,
            profile_image_url=fake.image_url(),
            last_login_at=fake.date_time_this_year(),
        )
        session.add(new_user)
        users.append(new_user)
    await session.flush()
    return users

async def seed_customer_applications(session: AsyncSession, users: list[User], departments: list[Department], branches: list[Branch], count: int = 20) -> list[CustomerApplication]:
    applications = []
    for _ in range(count):
        user = random.choice(users) if users else None
        if not user:
            continue

        status = random.choice(['draft', 'pending', 'approved', 'rejected'])
        id_card_type = random.choice(['national_id', 'passport', 'family_book'])
        id_number = fake.unique.ssn()
        full_name_khmer = fake.name() # Placeholder, ideally Khmer names
        full_name_latin = fake.name()
        phone = fake.phone_number()[:20]
        date_of_birth = fake.date_of_birth(minimum_age=18, maximum_age=65)
        portfolio_officer_name = fake.name()
        current_address = fake.address()
        province = fake.state()
        district = fake.city()
        commune = fake.word()
        village = fake.word()
        occupation = fake.job()
        employer_name = fake.company()
        monthly_income = random.uniform(500, 5000)
        income_source = random.choice(['salary', 'business', 'agriculture', 'rent'])
        requested_amount = random.uniform(1000, 50000)
        loan_purposes = random.sample(['business', 'agriculture', 'education', 'housing', 'vehicle', 'medical', 'other'], k=random.randint(1, 3))
        purpose_details = fake.sentence()
        product_type = random.choice(['micro_loan', 'sme_loan', 'agriculture_loan', 'housing_loan', 'education_loan'])
        desired_loan_term = random.choice(['6_months', '12_months', '24_months', '36_months'])
        requested_disbursement_date = fake.date_this_year()
        interest_rate = random.uniform(0.05, 0.20)
        guarantor_name = fake.name()
        guarantor_phone = fake.phone_number()[:20]
        guarantor_id_number = fake.unique.ssn()
        guarantor_address = fake.address()
        guarantor_relationship = random.choice(['family', 'friend', 'colleague'])
        existing_loans = [] # Simplified
        monthly_expenses = random.uniform(200, 2000)
        assets_value = random.uniform(5000, 100000)
        credit_score = random.randint(300, 850)
        risk_category = 'low' if credit_score > 700 else ('medium' if credit_score > 500 else 'high')
        assessment_notes = fake.paragraph()
        collaterals = [] # Simplified
        documents = [] # Simplified
        workflow_stage = random.choice(['initial_review', 'credit_check', 'approval_pending', 'approved', 'rejected'])
        assigned_reviewer_id = random.choice(users).id if users else None
        priority_level = random.choice(['normal', 'high', 'urgent'])
        submitted_at = fake.date_time_this_year()
        approved_at = submitted_at + fake.time_delta() if status == 'approved' else None
        approved_by_id = random.choice(users).id if status == 'approved' and users else None
        rejected_at = submitted_at + fake.time_delta() if status == 'rejected' else None
        rejected_by_id = random.choice(users).id if status == 'rejected' and users else None
        rejection_reason = fake.sentence() if status == 'rejected' else None

        new_application = CustomerApplication(
            user_id=user.id,
            status=status,
            id_card_type=id_card_type,
            id_number=id_number,
            full_name_khmer=full_name_khmer,
            full_name_latin=full_name_latin,
            phone=phone,
            date_of_birth=date_of_birth,
            portfolio_officer_name=portfolio_officer_name,
            current_address=current_address,
            province=province,
            district=district,
            commune=commune,
            village=village,
            occupation=occupation,
            employer_name=employer_name,
            monthly_income=monthly_income,
            income_source=income_source,
            requested_amount=requested_amount,
            loan_purposes=loan_purposes,
            purpose_details=purpose_details,
            product_type=product_type,
            desired_loan_term=desired_loan_term,
            requested_disbursement_date=requested_disbursement_date,
            interest_rate=interest_rate,
            guarantor_name=guarantor_name,
            guarantor_phone=guarantor_phone,
            guarantor_id_number=guarantor_id_number,
            guarantor_address=guarantor_address,
            guarantor_relationship=guarantor_relationship,
            existing_loans=existing_loans,
            monthly_expenses=monthly_expenses,
            assets_value=assets_value,
            credit_score=credit_score,
            risk_category=risk_category,
            assessment_notes=assessment_notes,
            collaterals=collaterals,
            documents=documents,
            workflow_stage=workflow_stage,
            assigned_reviewer=assigned_reviewer_id,
            priority_level=priority_level,
            submitted_at=submitted_at,
            approved_at=approved_at,
            approved_by=approved_by_id,
            rejected_at=rejected_at,
            rejected_by=rejected_by_id,
            rejection_reason=rejection_reason,
        )
        session.add(new_application)
        applications.append(new_application)
    await session.flush()
    return applications

async def seed_files(session: AsyncSession, users: list[User], applications: list[CustomerApplication], count: int = 30) -> list[File]:
    files = []
    for _ in range(count):
        user = random.choice(users) if users else None
        application = random.choice(applications) if applications else None
        if not user or not application:
            continue

        filename = fake.file_name()
        original_filename = fake.file_name()
        file_path = fake.file_path()
        file_size = random.randint(1024, 5 * 1024 * 1024) # 1KB to 5MB
        mime_type = fake.mime_type()

        new_file = File(
            filename=filename,
            original_filename=original_filename,
            file_path=file_path,
            file_size=file_size,
            mime_type=mime_type,
            uploaded_by=user.id,
            application_id=application.id,
        )
        session.add(new_file)
        files.append(new_file)
    await session.flush()
    return files

async def seed_all_data():
    async with AsyncSessionLocal() as session:
        print("Seeding departments...")
        departments = await seed_departments(session)
        print(f"Seeded {len(departments)} departments.")

        print("Seeding branches...")
        branches = await seed_branches(session)
        print(f"Seeded {len(branches)} branches.")

        print("Seeding positions...")
        positions = await seed_positions(session, departments, branches)
        print(f"Seeded {len(positions)} positions.")

        # Seed users (admin user is handled inside seed_users for idempotency)
        print("Seeding users...")
        all_users = await seed_users(session, departments, branches, positions, count=10) # Seed total 10 users including admin
        print(f"Seeded {len(all_users)} users.")

        print("Seeding settings...")
        settings = await seed_settings(session, all_users)
        print(f"Seeded {len(settings)} settings.")


        print("Seeding customer applications...")
        applications = await seed_customer_applications(session, all_users, departments, branches)
        print(f"Seeded {len(applications)} customer applications.")

        print("Seeding files...")
        files = await seed_files(session, all_users, applications)
        print(f"Seeded {len(files)} files.")

        await session.commit()
        print("All seed data generated successfully!")

if __name__ == "__main__":
    asyncio.run(seed_all_data())