{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/backend/lc-workflow-frontend/src/components/layout/Sidebar.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport {\n  HomeIcon,\n  DocumentTextIcon,\n  UsersIcon,\n  BuildingOfficeIcon,\n  MapPinIcon,\n  Cog6ToothIcon,\n  FolderIcon,\n  XMarkIcon,\n  BriefcaseIcon,\n} from '@heroicons/react/24/outline';\nimport { useAuthContext } from '@/providers/AuthProvider';\n\ninterface NavItem {\n  name: string;\n  href: string;\n  icon: React.ComponentType<{ className?: string }>;\n  requiredRoles?: string[];\n}\n\nconst navigation: NavItem[] = [\n  { name: 'Dashboard', href: '/dashboard', icon: HomeIcon },\n  { name: 'Applications', href: '/applications', icon: DocumentTextIcon },\n  { name: 'Files', href: '/files', icon: FolderIcon },\n];\n\nconst adminNavigation: NavItem[] = [\n  { name: 'Departments', href: '/departments', icon: BuildingOfficeIcon, requiredRoles: ['admin', 'manager'] },\n  { name: 'Positions', href: '/positions', icon: BriefcaseIcon, requiredRoles: ['admin', 'manager'] },\n  { name: 'Users', href: '/users', icon: UsersIcon, requiredRoles: ['admin', 'manager'] },\n  { name: 'Branches', href: '/branches', icon: MapPinIcon, requiredRoles: ['admin', 'manager'] },\n  { name: 'Settings', href: '/settings', icon: Cog6ToothIcon, requiredRoles: ['admin'] },\n];\n\ninterface SidebarProps {\n  isOpen: boolean;\n  onClose: () => void;\n}\n\nexport function Sidebar({ isOpen, onClose }: SidebarProps) {\n  const pathname = usePathname();\n  const { user, isAdmin, isManager } = useAuthContext();\n\n  const hasRequiredRole = (requiredRoles?: string[]) => {\n    if (!requiredRoles) return true;\n    return isAdmin || (isManager && requiredRoles.includes('manager'));\n  };\n\n  const allNavigation = [...navigation, ...adminNavigation];\n\n  const NavLink = ({ item }: { item: NavItem }) => {\n    const isActive = pathname === item.href;\n\n    if (!hasRequiredRole(item.requiredRoles)) return null;\n\n    return (\n      <Link\n        href={item.href}\n        className={`group flex items-center gap-x-3 rounded-md p-2 text-sm font-semibold leading-6 transition-all duration-200 ${isActive\n            ? 'bg-blue-600 text-white'\n            : 'text-gray-700 hover:bg-gray-50 hover:text-blue-600'\n          }`}\n        onClick={onClose}\n      >\n        <item.icon className=\"h-5 w-5 shrink-0\" />\n        {item.name}\n      </Link>\n    );\n  };\n\n  return (\n    <>\n      {/* Mobile sidebar */}\n      <div\n        className={`fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:hidden ${isOpen ? 'translate-x-0' : '-translate-x-full'\n          }`}\n      >\n        <div className=\"flex h-16 items-center justify-between px-4\">\n          <div className=\"text-xl font-bold text-gray-900\">LC Workflow</div>\n          <button\n            type=\"button\"\n            className=\"-m-2.5 p-2.5 text-gray-700\"\n            onClick={onClose}\n          >\n            <XMarkIcon className=\"h-6 w-6\" />\n          </button>\n        </div>\n        <nav className=\"flex flex-1 flex-col gap-y-2 p-4\">\n          {allNavigation.map((item) => (\n            <NavLink key={item.name} item={item} />\n          ))}\n        </nav>\n      </div>\n\n      {/* Desktop sidebar */}\n      <div className=\"hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-64 lg:flex-col\">\n        <div className=\"flex grow flex-col gap-y-5 overflow-y-auto bg-white px-6 ring-1 ring-gray-200\">\n          <div className=\"flex h-16 shrink-0 items-center\">\n            <div className=\"text-xl font-bold text-gray-900\">LC Workflow</div>\n          </div>\n          <nav className=\"flex flex-1 flex-col gap-y-2\">\n            {allNavigation.map((item) => (\n              <NavLink key={item.name} item={item} />\n            ))}\n          </nav>\n\n          {/* User info */}\n          {user && (\n            <div className=\"mb-4\">\n              <div className=\"flex items-center gap-x-3 rounded-md p-2 text-sm font-medium text-gray-700\">\n                <div className=\"h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center\">\n                  <span className=\"text-sm font-medium text-gray-600\">\n                    {user.first_name.charAt(0)}{user.last_name.charAt(0)}\n                  </span>\n                </div>\n                <div>\n                  <div className=\"font-medium\">{user.first_name} {user.last_name}</div>\n                  <div className=\"text-xs text-gray-500\">{user.role}</div>\n                </div>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;;;AAfA;;;;;AAwBA,MAAM,aAAwB;IAC5B;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,kNAAA,CAAA,WAAQ;IAAC;IACxD;QAAE,MAAM;QAAgB,MAAM;QAAiB,MAAM,kOAAA,CAAA,mBAAgB;IAAC;IACtE;QAAE,MAAM;QAAS,MAAM;QAAU,MAAM,sNAAA,CAAA,aAAU;IAAC;CACnD;AAED,MAAM,kBAA6B;IACjC;QAAE,MAAM;QAAe,MAAM;QAAgB,MAAM,sOAAA,CAAA,qBAAkB;QAAE,eAAe;YAAC;YAAS;SAAU;IAAC;IAC3G;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,4NAAA,CAAA,gBAAa;QAAE,eAAe;YAAC;YAAS;SAAU;IAAC;IAClG;QAAE,MAAM;QAAS,MAAM;QAAU,MAAM,oNAAA,CAAA,YAAS;QAAE,eAAe;YAAC;YAAS;SAAU;IAAC;IACtF;QAAE,MAAM;QAAY,MAAM;QAAa,MAAM,sNAAA,CAAA,aAAU;QAAE,eAAe;YAAC;YAAS;SAAU;IAAC;IAC7F;QAAE,MAAM;QAAY,MAAM;QAAa,MAAM,4NAAA,CAAA,gBAAa;QAAE,eAAe;YAAC;SAAQ;IAAC;CACtF;AAOM,SAAS,QAAQ,KAAiC;QAAjC,EAAE,MAAM,EAAE,OAAO,EAAgB,GAAjC;;IACtB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,iBAAc,AAAD;IAElD,MAAM,kBAAkB,CAAC;QACvB,IAAI,CAAC,eAAe,OAAO;QAC3B,OAAO,WAAY,aAAa,cAAc,QAAQ,CAAC;IACzD;IAEA,MAAM,gBAAgB;WAAI;WAAe;KAAgB;IAEzD,MAAM,UAAU;YAAC,EAAE,IAAI,EAAqB;QAC1C,MAAM,WAAW,aAAa,KAAK,IAAI;QAEvC,IAAI,CAAC,gBAAgB,KAAK,aAAa,GAAG,OAAO;QAEjD,qBACE,6LAAC,+JAAA,CAAA,UAAI;YACH,MAAM,KAAK,IAAI;YACf,WAAW,AAAC,8GAGT,OAHsH,WACnH,2BACA;YAEN,SAAS;;8BAET,6LAAC,KAAK,IAAI;oBAAC,WAAU;;;;;;gBACpB,KAAK,IAAI;;;;;;;IAGhB;IAEA,qBACE;;0BAEE,6LAAC;gBACC,WAAW,AAAC,yHACT,OADiI,SAAS,kBAAkB;;kCAG/J,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAAkC;;;;;;0CACjD,6LAAC;gCACC,MAAK;gCACL,WAAU;gCACV,SAAS;0CAET,cAAA,6LAAC,oNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAGzB,6LAAC;wBAAI,WAAU;kCACZ,cAAc,GAAG,CAAC,CAAC,qBAClB,6LAAC;gCAAwB,MAAM;+BAAjB,KAAK,IAAI;;;;;;;;;;;;;;;;0BAM7B,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CAAkC;;;;;;;;;;;sCAEnD,6LAAC;4BAAI,WAAU;sCACZ,cAAc,GAAG,CAAC,CAAC,qBAClB,6LAAC;oCAAwB,MAAM;mCAAjB,KAAK,IAAI;;;;;;;;;;wBAK1B,sBACC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;;gDACb,KAAK,UAAU,CAAC,MAAM,CAAC;gDAAI,KAAK,SAAS,CAAC,MAAM,CAAC;;;;;;;;;;;;kDAGtD,6LAAC;;0DACC,6LAAC;gDAAI,WAAU;;oDAAe,KAAK,UAAU;oDAAC;oDAAE,KAAK,SAAS;;;;;;;0DAC9D,6LAAC;gDAAI,WAAU;0DAAyB,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASnE;GAvFgB;;QACG,qIAAA,CAAA,cAAW;QACS,oIAAA,CAAA,iBAAc;;;KAFrC", "debugId": null}}, {"offset": {"line": 316, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/backend/lc-workflow-frontend/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useRef, useEffect } from 'react';\r\nimport { Bars3Icon, BellIcon, UserCircleIcon, ArrowRightOnRectangleIcon, Cog6ToothIcon } from '@heroicons/react/24/outline';\r\nimport { useLogout } from '@/hooks/useAuth';\r\nimport { useAuthContext } from '@/providers/AuthProvider';\r\nimport Link from 'next/link';\r\n\r\ninterface HeaderProps {\r\n  onMenuClick: () => void;\r\n}\r\n\r\nexport function Header({ onMenuClick }: HeaderProps) {\r\n  const logout = useLogout();\r\n  const { user } = useAuthContext();\r\n  const [profileDropdownOpen, setProfileDropdownOpen] = useState(false);\r\n  const dropdownRef = useRef<HTMLDivElement>(null);\r\n\r\n  // Close dropdown when clicking outside\r\n  useEffect(() => {\r\n    function handleClickOutside(event: MouseEvent) {\r\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\r\n        setProfileDropdownOpen(false);\r\n      }\r\n    }\r\n\r\n    document.addEventListener('mousedown', handleClickOutside);\r\n    return () => {\r\n      document.removeEventListener('mousedown', handleClickOutside);\r\n    };\r\n  }, []);\r\n\r\n  return (\r\n    <div className=\"sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 bg-white px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8\">\r\n      <button\r\n        type=\"button\"\r\n        className=\"-m-2.5 p-2.5 text-gray-700 lg:hidden\"\r\n        onClick={onMenuClick}\r\n      >\r\n        <span className=\"sr-only\">Open sidebar</span>\r\n        <Bars3Icon className=\"h-6 w-6\" aria-hidden=\"true\" />\r\n      </button>\r\n\r\n      {/* Separator */}\r\n      <div className=\"h-6 w-px bg-gray-900/10 lg:hidden\" aria-hidden=\"true\" />\r\n\r\n      <div className=\"flex flex-1 gap-x-4 self-stretch lg:gap-x-6\">\r\n        <div className=\"flex flex-1\" />\r\n\r\n        <div className=\"flex items-center gap-x-4 lg:gap-x-6\">\r\n          <button\r\n            type=\"button\"\r\n            className=\"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500\"\r\n          >\r\n            <span className=\"sr-only\">View notifications</span>\r\n            <BellIcon className=\"h-6 w-6\" aria-hidden=\"true\" />\r\n          </button>\r\n\r\n          {/* Separator */}\r\n          <div className=\"hidden lg:block lg:h-6 lg:w-px lg:bg-gray-900/10\" aria-hidden=\"true\" />\r\n\r\n          {/* Profile dropdown */}\r\n          <div className=\"relative\">\r\n            <button\r\n              type=\"button\"\r\n              className=\"-m-1.5 flex items-center p-1.5\"\r\n              id=\"user-menu-button\"\r\n              aria-expanded=\"false\"\r\n              aria-haspopup=\"true\"\r\n              onClick={() => setProfileDropdownOpen(!profileDropdownOpen)}\r\n            >\r\n              <span className=\"sr-only\">Open user menu</span>\r\n              <div className=\"h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center\">\r\n                <span className=\"text-sm font-medium text-gray-600\">\r\n                  {user?.first_name?.charAt(0)}{user?.last_name?.charAt(0)}\r\n                </span>\r\n              </div>\r\n              <span className=\"hidden lg:flex lg:items-center\">\r\n                <span className=\"ml-4 text-sm leading-6 text-gray-900\" aria-hidden=\"true\">\r\n                  <span className=\"font-semibold\">{user?.first_name} {user?.last_name}</span>\r\n                  {user?.position?.title ? (\r\n                    <span className=\"ml-2 text-gray-500 truncate max-w-[10rem]\">\r\n                      • {user?.position?.title}\r\n                    </span>\r\n                  ) : null}\r\n                </span>\r\n              </span>\r\n            </button>\r\n\r\n            {/* Dropdown menu */}\r\n            {profileDropdownOpen && (\r\n              <div\r\n                className=\"absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none\"\r\n                role=\"menu\"\r\n                aria-orientation=\"vertical\"\r\n                aria-labelledby=\"user-menu-button\"\r\n                ref={dropdownRef}\r\n              >\r\n                <div className=\"px-4 py-3\">\r\n                  <p className=\"text-sm\">Signed in as</p>\r\n                  <p className=\"truncate text-sm font-medium text-gray-900\">{user?.email}</p>\r\n                </div>\r\n                <div className=\"border-t border-gray-100\"></div>\r\n                <Link\r\n                  href=\"/profile\"\r\n                  className=\"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\r\n                  role=\"menuitem\"\r\n                  onClick={() => setProfileDropdownOpen(false)}\r\n                >\r\n                  <UserCircleIcon className=\"mr-3 h-5 w-5 text-gray-400\" aria-hidden=\"true\" />\r\n                  Your Profile\r\n                </Link>\r\n                <Link\r\n                  href=\"/settings\"\r\n                  className=\"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\r\n                  role=\"menuitem\"\r\n                  onClick={() => setProfileDropdownOpen(false)}\r\n                >\r\n                  <Cog6ToothIcon className=\"mr-3 h-5 w-5 text-gray-400\" aria-hidden=\"true\" />\r\n                  Settings\r\n                </Link>\r\n                <div className=\"border-t border-gray-100\"></div>\r\n                <button\r\n                  type=\"button\"\r\n                  className=\"flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\r\n                  role=\"menuitem\"\r\n                  onClick={() => {\r\n                    setProfileDropdownOpen(false);\r\n                    logout.mutate();\r\n                  }}\r\n                >\r\n                  <ArrowRightOnRectangleIcon className=\"mr-3 h-5 w-5 text-gray-400\" aria-hidden=\"true\" />\r\n                  Sign out\r\n                </button>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;AANA;;;;;;AAYO,SAAS,OAAO,KAA4B;QAA5B,EAAE,WAAW,EAAe,GAA5B;QA8DJ,kBAA6B,iBAM7B,gBAEM;;IArEvB,MAAM,SAAS,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,iBAAc,AAAD;IAC9B,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAE3C,uCAAuC;IACvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,SAAS,mBAAmB,KAAiB;gBAC3C,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;oBAC9E,uBAAuB;gBACzB;YACF;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC;oCAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;gBAC5C;;QACF;2BAAG,EAAE;IAEL,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACC,MAAK;gBACL,WAAU;gBACV,SAAS;;kCAET,6LAAC;wBAAK,WAAU;kCAAU;;;;;;kCAC1B,6LAAC,oNAAA,CAAA,YAAS;wBAAC,WAAU;wBAAU,eAAY;;;;;;;;;;;;0BAI7C,6LAAC;gBAAI,WAAU;gBAAoC,eAAY;;;;;;0BAE/D,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCAEf,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,MAAK;gCACL,WAAU;;kDAEV,6LAAC;wCAAK,WAAU;kDAAU;;;;;;kDAC1B,6LAAC,kNAAA,CAAA,WAAQ;wCAAC,WAAU;wCAAU,eAAY;;;;;;;;;;;;0CAI5C,6LAAC;gCAAI,WAAU;gCAAmD,eAAY;;;;;;0CAG9E,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,WAAU;wCACV,IAAG;wCACH,iBAAc;wCACd,iBAAc;wCACd,SAAS,IAAM,uBAAuB,CAAC;;0DAEvC,6LAAC;gDAAK,WAAU;0DAAU;;;;;;0DAC1B,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;;wDACb,iBAAA,4BAAA,mBAAA,KAAM,UAAU,cAAhB,uCAAA,iBAAkB,MAAM,CAAC;wDAAI,iBAAA,4BAAA,kBAAA,KAAM,SAAS,cAAf,sCAAA,gBAAiB,MAAM,CAAC;;;;;;;;;;;;0DAG1D,6LAAC;gDAAK,WAAU;0DACd,cAAA,6LAAC;oDAAK,WAAU;oDAAuC,eAAY;;sEACjE,6LAAC;4DAAK,WAAU;;gEAAiB,iBAAA,2BAAA,KAAM,UAAU;gEAAC;gEAAE,iBAAA,2BAAA,KAAM,SAAS;;;;;;;wDAClE,CAAA,iBAAA,4BAAA,iBAAA,KAAM,QAAQ,cAAd,qCAAA,eAAgB,KAAK,kBACpB,6LAAC;4DAAK,WAAU;;gEAA4C;gEACvD,iBAAA,4BAAA,kBAAA,KAAM,QAAQ,cAAd,sCAAA,gBAAgB,KAAK;;;;;;mEAExB;;;;;;;;;;;;;;;;;;oCAMT,qCACC,6LAAC;wCACC,WAAU;wCACV,MAAK;wCACL,oBAAiB;wCACjB,mBAAgB;wCAChB,KAAK;;0DAEL,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAU;;;;;;kEACvB,6LAAC;wDAAE,WAAU;kEAA8C,iBAAA,2BAAA,KAAM,KAAK;;;;;;;;;;;;0DAExE,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,MAAK;gDACL,SAAS,IAAM,uBAAuB;;kEAEtC,6LAAC,8NAAA,CAAA,iBAAc;wDAAC,WAAU;wDAA6B,eAAY;;;;;;oDAAS;;;;;;;0DAG9E,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,MAAK;gDACL,SAAS,IAAM,uBAAuB;;kEAEtC,6LAAC,4NAAA,CAAA,gBAAa;wDAAC,WAAU;wDAA6B,eAAY;;;;;;oDAAS;;;;;;;0DAG7E,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDACC,MAAK;gDACL,WAAU;gDACV,MAAK;gDACL,SAAS;oDACP,uBAAuB;oDACvB,OAAO,MAAM;gDACf;;kEAEA,6LAAC,oPAAA,CAAA,4BAAyB;wDAAC,WAAU;wDAA6B,eAAY;;;;;;oDAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUzG;GAjIgB;;QACC,0HAAA,CAAA,YAAS;QACP,oIAAA,CAAA,iBAAc;;;KAFjB", "debugId": null}}, {"offset": {"line": 690, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/backend/lc-workflow-frontend/src/components/layout/Layout.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Sidebar } from './Sidebar';\nimport { Header } from './Header';\n\ninterface LayoutProps {\n  children: React.ReactNode;\n}\n\nexport function Layout({ children }: LayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n\n  return (\n    <>\n      <div className=\"min-h-full\">\n        <Sidebar \n          isOpen={sidebarOpen} \n          onClose={() => setSidebarOpen(false)} \n        />\n        \n        <div className=\"lg:pl-64\">\n          <Header onMenuClick={() => setSidebarOpen(true)} />\n          \n          <main className=\"py-10\">\n            <div className=\"px-4 sm:px-6 lg:px-8\">\n              {children}\n            </div>\n          </main>\n        </div>\n      </div>\n    </>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAUO,SAAS,OAAO,KAAyB;QAAzB,EAAE,QAAQ,EAAe,GAAzB;;IACrB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,qBACE;kBACE,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,0IAAA,CAAA,UAAO;oBACN,QAAQ;oBACR,SAAS,IAAM,eAAe;;;;;;8BAGhC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,yIAAA,CAAA,SAAM;4BAAC,aAAa,IAAM,eAAe;;;;;;sCAE1C,6LAAC;4BAAK,WAAU;sCACd,cAAA,6LAAC;gCAAI,WAAU;0CACZ;;;;;;;;;;;;;;;;;;;;;;;;AAOf;GAvBgB;KAAA", "debugId": null}}, {"offset": {"line": 771, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/backend/lc-workflow-frontend/src/components/auth/ProtectedRoute.tsx"], "sourcesContent": ["'use client';\n\nimport { useRouter } from 'next/navigation';\nimport { useAuthContext } from '@/providers/AuthProvider';\nimport { useEffect } from 'react';\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode;\n  requiredRoles?: string[];\n}\n\nexport function ProtectedRoute({ children, requiredRoles }: ProtectedRouteProps) {\n  const router = useRouter();\n  const { isAuthenticated, isLoading, user, role } = useAuthContext();\n\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      if (!isLoading) {\n        if (!isAuthenticated) {\n          router.push('/login');\n        } else if (requiredRoles && requiredRoles.length > 0 && !requiredRoles.includes(role || '')) {\n          router.push('/unauthorized');\n        }\n      }\n    }, 1000); // Wait 1 second for auth state to stabilize\n\n    return () => clearTimeout(timer);\n  }, [isAuthenticated, isLoading, requiredRoles, role, router]);\n\n  if (isLoading || !isAuthenticated) {\n    return (\n      <div className=\"flex min-h-screen items-center justify-center\">\n        <div className=\"h-8 w-8 animate-spin rounded-full border-4 border-blue-600 border-t-transparent\"></div>\n      </div>\n    );\n  }\n\n  if (requiredRoles && requiredRoles.length > 0 && !requiredRoles.includes(role || '')) {\n    return null;\n  }\n\n  return <>{children}</>;\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAWO,SAAS,eAAe,KAAgD;QAAhD,EAAE,QAAQ,EAAE,aAAa,EAAuB,GAAhD;;IAC7B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,iBAAc,AAAD;IAEhE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM,QAAQ;kDAAW;oBACvB,IAAI,CAAC,WAAW;wBACd,IAAI,CAAC,iBAAiB;4BACpB,OAAO,IAAI,CAAC;wBACd,OAAO,IAAI,iBAAiB,cAAc,MAAM,GAAG,KAAK,CAAC,cAAc,QAAQ,CAAC,QAAQ,KAAK;4BAC3F,OAAO,IAAI,CAAC;wBACd;oBACF;gBACF;iDAAG,OAAO,4CAA4C;YAEtD;4CAAO,IAAM,aAAa;;QAC5B;mCAAG;QAAC;QAAiB;QAAW;QAAe;QAAM;KAAO;IAE5D,IAAI,aAAa,CAAC,iBAAiB;QACjC,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,iBAAiB,cAAc,MAAM,GAAG,KAAK,CAAC,cAAc,QAAQ,CAAC,QAAQ,KAAK;QACpF,OAAO;IACT;IAEA,qBAAO;kBAAG;;AACZ;GA/BgB;;QACC,qIAAA,CAAA,YAAS;QAC2B,oIAAA,CAAA,iBAAc;;;KAFnD", "debugId": null}}, {"offset": {"line": 854, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/backend/lc-workflow-frontend/src/components/ui/ConfirmDialog.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { ExclamationTriangleIcon } from '@heroicons/react/24/outline';\r\n\r\ninterface ConfirmDialogProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  onConfirm: () => void;\r\n  title: string;\r\n  message: string;\r\n  confirmText?: string;\r\n  cancelText?: string;\r\n  confirmButtonClass?: string;\r\n  icon?: React.ReactNode;\r\n}\r\n\r\nexport default function ConfirmDialog({\r\n  isOpen,\r\n  onClose,\r\n  onConfirm,\r\n  title,\r\n  message,\r\n  confirmText = 'Confirm',\r\n  cancelText = 'Cancel',\r\n  confirmButtonClass = 'bg-red-600 hover:bg-red-700',\r\n  icon,\r\n}: ConfirmDialogProps) {\r\n  if (!isOpen) return null;\r\n\r\n  const handleConfirm = () => {\r\n    onConfirm();\r\n    onClose();\r\n  };\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n      <div className=\"bg-white rounded-lg shadow-xl max-w-md w-full mx-4\">\r\n        <div className=\"p-6\">\r\n          <div className=\"flex items-center gap-4 mb-4\">\r\n            {icon || (\r\n              <div className=\"flex-shrink-0\">\r\n                <ExclamationTriangleIcon className=\"h-6 w-6 text-red-600\" />\r\n              </div>\r\n            )}\r\n            <div>\r\n              <h3 className=\"text-lg font-medium text-gray-900\">{title}</h3>\r\n            </div>\r\n          </div>\r\n          <p className=\"text-gray-600 mb-6\">{message}</p>\r\n          <div className=\"flex items-center justify-end gap-3\">\r\n            <button\r\n              onClick={onClose}\r\n              className=\"px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50\"\r\n            >\r\n              {cancelText}\r\n            </button>\r\n            <button\r\n              onClick={handleConfirm}\r\n              className={`px-4 py-2 text-white rounded-lg ${confirmButtonClass}`}\r\n            >\r\n              {confirmText}\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAgBe,SAAS,cAAc,KAUjB;QAViB,EACpC,MAAM,EACN,OAAO,EACP,SAAS,EACT,KAAK,EACL,OAAO,EACP,cAAc,SAAS,EACvB,aAAa,QAAQ,EACrB,qBAAqB,6BAA6B,EAClD,IAAI,EACe,GAViB;IAWpC,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,gBAAgB;QACpB;QACA;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;4BACZ,sBACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,gPAAA,CAAA,0BAAuB;oCAAC,WAAU;;;;;;;;;;;0CAGvC,6LAAC;0CACC,cAAA,6LAAC;oCAAG,WAAU;8CAAqC;;;;;;;;;;;;;;;;;kCAGvD,6LAAC;wBAAE,WAAU;kCAAsB;;;;;;kCACnC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS;gCACT,WAAU;0CAET;;;;;;0CAEH,6LAAC;gCACC,SAAS;gCACT,WAAW,AAAC,mCAAqD,OAAnB;0CAE7C;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf;KAnDwB", "debugId": null}}, {"offset": {"line": 977, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/backend/lc-workflow-frontend/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\nexport function formatBytes(bytes: number, decimals = 2): string {\n  if (bytes === 0) return '0 Bytes';\n\n  const k = 1024;\n  const dm = decimals < 0 ? 0 : decimals;\n  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];\n\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];\n}\n\nexport function formatDate(dateString: string): string {\n  const date = new Date(dateString);\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  }).format(date);\n}\n\nexport function formatCurrency(amount: number, currency = 'USD'): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency,\n  }).format(amount);\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text;\n  return text.slice(0, maxLength) + '...';\n}\n\nexport function getFileIcon(mimeType: string): string {\n  if (mimeType.startsWith('image/')) return '🖼️';\n  if (mimeType === 'application/pdf') return '📄';\n  if (mimeType.includes('word')) return '📝';\n  if (mimeType.includes('excel') || mimeType.includes('spreadsheet')) return '📊';\n  if (mimeType.startsWith('text/')) return '📄';\n  return '📎';\n}\n\nexport function validateEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\nexport function validatePhone(phone: string): boolean {\n  const phoneRegex = /^\\+?[\\d\\s\\-\\(\\)]+$/;\n  return phoneRegex.test(phone) && phone.replace(/\\D/g, '').length >= 10;\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9);\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean;\n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args);\n      inThrottle = true;\n      setTimeout(() => (inThrottle = false), limit);\n    }\n  };\n}\n\nexport function isValidUUID(uuid: string): boolean {\n  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;\n  return uuidRegex.test(uuid);\n}\n\nexport function validateUUID(uuid: string): string {\n  if (!isValidUUID(uuid)) {\n    throw new Error('Invalid UUID format');\n  }\n  return uuid;\n}\n\nexport function sanitizeUUID(uuid: string): string {\n  return uuid.replace(/[^a-f0-9-]/gi, '');\n}"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,YAAY,KAAa;QAAE,WAAA,iEAAW;IACpD,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,KAAK,WAAW,IAAI,IAAI;IAC9B,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;KAAK;IAEvE,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,OAAO,MAAM,KAAK,CAAC,EAAE;AAC1E;AAEO,SAAS,WAAW,UAAkB;IAC3C,MAAM,OAAO,IAAI,KAAK;IACtB,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,eAAe,MAAc;QAAE,WAAA,iEAAW;IACxD,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP;IACF,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,aAAa;AACpC;AAEO,SAAS,YAAY,QAAgB;IAC1C,IAAI,SAAS,UAAU,CAAC,WAAW,OAAO;IAC1C,IAAI,aAAa,mBAAmB,OAAO;IAC3C,IAAI,SAAS,QAAQ,CAAC,SAAS,OAAO;IACtC,IAAI,SAAS,QAAQ,CAAC,YAAY,SAAS,QAAQ,CAAC,gBAAgB,OAAO;IAC3E,IAAI,SAAS,UAAU,CAAC,UAAU,OAAO;IACzC,OAAO;AACT;AAEO,SAAS,cAAc,KAAa;IACzC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,cAAc,KAAa;IACzC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC,UAAU,MAAM,OAAO,CAAC,OAAO,IAAI,MAAM,IAAI;AACtE;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO;yCAAI;YAAA;;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,SACd,IAAO,EACP,KAAa;IAEb,IAAI;IACJ,OAAO;yCAAI;YAAA;;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW,IAAO,aAAa,OAAQ;QACzC;IACF;AACF;AAEO,SAAS,YAAY,IAAY;IACtC,MAAM,YAAY;IAClB,OAAO,UAAU,IAAI,CAAC;AACxB;AAEO,SAAS,aAAa,IAAY;IACvC,IAAI,CAAC,YAAY,OAAO;QACtB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAEO,SAAS,aAAa,IAAY;IACvC,OAAO,KAAK,OAAO,CAAC,gBAAgB;AACtC", "debugId": null}}, {"offset": {"line": 1107, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/backend/lc-workflow-frontend/src/hooks/usePositions.ts"], "sourcesContent": ["import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';\r\nimport { apiClient } from '@/lib/api';\r\nimport { Position, PositionCreate, PositionUpdate, PaginatedResponse } from '@/types/models';\r\nimport toast from 'react-hot-toast';\r\nimport { isValidUUID, validateUUID } from '@/lib/utils';\r\n\r\ntype PositionFilters = {\r\n    page?: number;\r\n    size?: number;\r\n    search?: string;\r\n    is_active?: boolean;\r\n};\r\n\r\n// Position query keys (mirrors pattern in other hooks)\r\nexport const positionKeys = {\r\n    all: ['positions'] as const,\r\n    lists: () => [...positionKeys.all, 'list'] as const,\r\n    list: (filters: PositionFilters) => [...positionKeys.lists(), filters] as const,\r\n    details: () => [...positionKeys.all, 'detail'] as const,\r\n    detail: (id: string) => [...positionKeys.details(), id] as const,\r\n};\r\n\r\n// List positions with optional filters (search/pagination/is_active)\r\nexport const usePositions = (filters: PositionFilters = {}) => {\r\n    return useQuery({\r\n        queryKey: positionKeys.list(filters),\r\n        queryFn: () => apiClient.get<PaginatedResponse<Position>>('/positions', {\r\n            params: filters,\r\n        }),\r\n        staleTime: 5 * 60 * 1000, // 5 minutes\r\n    });\r\n};\r\n\r\n// Get single position by id\r\nexport const usePosition = (id: string) => {\r\n    return useQuery({\r\n        queryKey: positionKeys.detail(id),\r\n        queryFn: () => apiClient.get<Position>(`/positions/${id}`),\r\n        staleTime: 5 * 60 * 1000,\r\n        enabled: !!id && isValidUUID(id),\r\n    });\r\n};\r\n\r\n// Create position\r\nexport const useCreatePosition = () => {\r\n    const queryClient = useQueryClient();\r\n\r\n    return useMutation({\r\n        mutationFn: (data: PositionCreate) => apiClient.post<Position>('/positions', data),\r\n        onSuccess: () => {\r\n            queryClient.invalidateQueries({ queryKey: positionKeys.lists() });\r\n            toast.success('Position created successfully!');\r\n        },\r\n        onError: (error: unknown) => {\r\n            // Align with project pattern without using 'any'\r\n            const err = error as { response?: { data?: { detail?: string } } };\r\n            const message = err.response?.data?.detail || 'Failed to create position';\r\n            toast.error(message);\r\n        },\r\n    });\r\n};\r\n\r\n// Update position (mirror convention: other hooks use PATCH)\r\nexport const useUpdatePosition = (id: string) => {\r\n    const queryClient = useQueryClient();\r\n\r\n    return useMutation({\r\n        mutationFn: (data: PositionUpdate) => {\r\n            validateUUID(id);\r\n            return apiClient.patch<Position>(`/positions/${id}`, data);\r\n        },\r\n        onSuccess: (updated) => {\r\n            queryClient.setQueryData(positionKeys.detail(id), updated);\r\n            queryClient.invalidateQueries({ queryKey: positionKeys.lists() });\r\n            toast.success('Position updated successfully!');\r\n        },\r\n        onError: (error: unknown) => {\r\n            const err = error as { response?: { data?: { detail?: string } } };\r\n            const message = err.response?.data?.detail || 'Failed to update position';\r\n            toast.error(message);\r\n        },\r\n    });\r\n};\r\n\r\n// Delete position\r\nexport const useDeletePosition = () => {\r\n    const queryClient = useQueryClient();\r\n\r\n    return useMutation({\r\n        mutationFn: (id: string) => {\r\n            validateUUID(id);\r\n            return apiClient.delete(`/positions/${id}`);\r\n        },\r\n        onSuccess: () => {\r\n            queryClient.invalidateQueries({ queryKey: positionKeys.lists() });\r\n            toast.success('Position deleted successfully!');\r\n        },\r\n        onError: (error: unknown) => {\r\n            const err = error as { response?: { data?: { detail?: string } } };\r\n            const message = err.response?.data?.detail || 'Failed to delete position';\r\n            toast.error(message);\r\n        },\r\n    });\r\n};"], "names": [], "mappings": ";;;;;;;;AAAA;AAAA;AAAA;AACA;AAEA;AACA;;;;;;AAUO,MAAM,eAAe;IACxB,KAAK;QAAC;KAAY;IAClB,OAAO,IAAM;eAAI,aAAa,GAAG;YAAE;SAAO;IAC1C,MAAM,CAAC,UAA6B;eAAI,aAAa,KAAK;YAAI;SAAQ;IACtE,SAAS,IAAM;eAAI,aAAa,GAAG;YAAE;SAAS;IAC9C,QAAQ,CAAC,KAAe;eAAI,aAAa,OAAO;YAAI;SAAG;AAC3D;AAGO,MAAM,eAAe;QAAC,2EAA2B,CAAC;;IACrD,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACZ,UAAU,aAAa,IAAI,CAAC;QAC5B,OAAO;qCAAE,IAAM,oHAAA,CAAA,YAAS,CAAC,GAAG,CAA8B,cAAc;oBACpE,QAAQ;gBACZ;;QACA,WAAW,IAAI,KAAK;IACxB;AACJ;GARa;;QACF,8KAAA,CAAA,WAAQ;;;AAUZ,MAAM,cAAc,CAAC;;IACxB,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACZ,UAAU,aAAa,MAAM,CAAC;QAC9B,OAAO;oCAAE,IAAM,oHAAA,CAAA,YAAS,CAAC,GAAG,CAAW,AAAC,cAAgB,OAAH;;QACrD,WAAW,IAAI,KAAK;QACpB,SAAS,CAAC,CAAC,MAAM,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE;IACjC;AACJ;IAPa;;QACF,8KAAA,CAAA,WAAQ;;;AASZ,MAAM,oBAAoB;;IAC7B,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACf,UAAU;6CAAE,CAAC,OAAyB,oHAAA,CAAA,YAAS,CAAC,IAAI,CAAW,cAAc;;QAC7E,SAAS;6CAAE;gBACP,YAAY,iBAAiB,CAAC;oBAAE,UAAU,aAAa,KAAK;gBAAG;gBAC/D,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;YAClB;;QACA,OAAO;6CAAE,CAAC;oBAGU,oBAAA;gBAFhB,iDAAiD;gBACjD,MAAM,MAAM;gBACZ,MAAM,UAAU,EAAA,gBAAA,IAAI,QAAQ,cAAZ,qCAAA,qBAAA,cAAc,IAAI,cAAlB,yCAAA,mBAAoB,MAAM,KAAI;gBAC9C,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YAChB;;IACJ;AACJ;IAhBa;;QACW,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAgBf,MAAM,oBAAoB,CAAC;;IAC9B,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACf,UAAU;6CAAE,CAAC;gBACT,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE;gBACb,OAAO,oHAAA,CAAA,YAAS,CAAC,KAAK,CAAW,AAAC,cAAgB,OAAH,KAAM;YACzD;;QACA,SAAS;6CAAE,CAAC;gBACR,YAAY,YAAY,CAAC,aAAa,MAAM,CAAC,KAAK;gBAClD,YAAY,iBAAiB,CAAC;oBAAE,UAAU,aAAa,KAAK;gBAAG;gBAC/D,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;YAClB;;QACA,OAAO;6CAAE,CAAC;oBAEU,oBAAA;gBADhB,MAAM,MAAM;gBACZ,MAAM,UAAU,EAAA,gBAAA,IAAI,QAAQ,cAAZ,qCAAA,qBAAA,cAAc,IAAI,cAAlB,yCAAA,mBAAoB,MAAM,KAAI;gBAC9C,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YAChB;;IACJ;AACJ;IAnBa;;QACW,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAmBf,MAAM,oBAAoB;;IAC7B,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACf,UAAU;6CAAE,CAAC;gBACT,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE;gBACb,OAAO,oHAAA,CAAA,YAAS,CAAC,MAAM,CAAC,AAAC,cAAgB,OAAH;YAC1C;;QACA,SAAS;6CAAE;gBACP,YAAY,iBAAiB,CAAC;oBAAE,UAAU,aAAa,KAAK;gBAAG;gBAC/D,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;YAClB;;QACA,OAAO;6CAAE,CAAC;oBAEU,oBAAA;gBADhB,MAAM,MAAM;gBACZ,MAAM,UAAU,EAAA,gBAAA,IAAI,QAAQ,cAAZ,qCAAA,qBAAA,cAAc,IAAI,cAAlB,yCAAA,mBAAoB,MAAM,KAAI;gBAC9C,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YAChB;;IACJ;AACJ;IAlBa;;QACW,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW", "debugId": null}}, {"offset": {"line": 1291, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/backend/lc-workflow-frontend/src/app/positions/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport Link from 'next/link';\r\nimport { useRouter } from 'next/navigation';\r\nimport { Layout } from '@/components/layout/Layout';\r\nimport { ProtectedRoute } from '@/components/auth/ProtectedRoute';\r\nimport ConfirmDialog from '@/components/ui/ConfirmDialog';\r\nimport { usePositions, useDeletePosition } from '@/hooks/usePositions';\r\nimport { Position } from '@/types/models';\r\nimport {\r\n    Plus,\r\n    Search,\r\n    Eye,\r\n    Edit,\r\n    Trash2,\r\n    Briefcase,\r\n    CheckCircle2,\r\n    XCircle,\r\n    ChevronLeft,\r\n    ChevronRight,\r\n} from 'lucide-react';\r\n\r\ntype PositionListItem = Omit<Position, 'id'> & { id: string };\r\n\r\nexport default function PositionsPage() {\r\n    const router = useRouter();\r\n\r\n    const [page, setPage] = useState(1);\r\n    const [search, setSearch] = useState('');\r\n    const [isActiveOnly, setIsActiveOnly] = useState<boolean | ''>('');\r\n    const [confirmDelete, setConfirmDelete] = useState<{ open: boolean; id?: string; name?: string }>({\r\n        open: false,\r\n    });\r\n\r\n    const { data: positionsData, isLoading, error } = usePositions({\r\n        page,\r\n        size: 10,\r\n        search: search || undefined,\r\n        is_active: isActiveOnly === '' ? undefined : Boolean(isActiveOnly),\r\n    }) as { data: { items: PositionListItem[]; total: number; pages: number } | undefined; isLoading: boolean; error: unknown };\r\n\r\n    const deletePosition = useDeletePosition();\r\n\r\n    const handleSearch = (e: React.FormEvent) => {\r\n        e.preventDefault();\r\n        setPage(1);\r\n    };\r\n\r\n    const handleDelete = (id: string, name?: string) => {\r\n        setConfirmDelete({\r\n            open: true,\r\n            id,\r\n            name,\r\n        });\r\n    };\r\n\r\n    const confirmDeleteAction = () => {\r\n        if (confirmDelete.id) {\r\n            deletePosition.mutate(confirmDelete.id);\r\n        }\r\n    };\r\n\r\n    const clearFilters = () => {\r\n        setSearch('');\r\n        setIsActiveOnly('');\r\n        setPage(1);\r\n    };\r\n\r\n    const totalPages = positionsData?.pages || 0;\r\n\r\n    if (error) {\r\n        return (\r\n            <ProtectedRoute>\r\n                <Layout>\r\n                    <div className=\"flex items-center justify-center h-full\">\r\n                        <div className=\"text-center\">\r\n                            <h1 className=\"text-2xl font-bold text-gray-900 mb-2\">Error Loading Positions</h1>\r\n                            <p className=\"text-gray-600\">Please try again later.</p>\r\n                        </div>\r\n                    </div>\r\n                </Layout>\r\n            </ProtectedRoute>\r\n        );\r\n    }\r\n\r\n    return (\r\n        <ProtectedRoute>\r\n            <Layout>\r\n                {/* Header */}\r\n                <div className=\"mb-8\">\r\n                    <div className=\"flex items-center justify-between\">\r\n                        <div className=\"flex items-center space-x-3\">\r\n                            <Briefcase className=\"h-8 w-8 text-blue-600\" />\r\n                            <div>\r\n                                <h1 className=\"text-3xl font-bold text-gray-900\">Positions</h1>\r\n                                <p className=\"text-gray-600\">Manage job positions</p>\r\n                            </div>\r\n                        </div>\r\n                        <Link\r\n                            href=\"/positions/new\"\r\n                            className=\"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\r\n                        >\r\n                            <Plus className=\"h-5 w-5 mr-2\" />\r\n                            New Position\r\n                        </Link>\r\n                    </div>\r\n                </div>\r\n\r\n                {/* Search and Filters */}\r\n                <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6\">\r\n                    <form onSubmit={handleSearch} className=\"space-y-4\">\r\n                        <div className=\"flex flex-col sm:flex-row gap-4\">\r\n                            <div className=\"flex-1\">\r\n                                <div className=\"relative\">\r\n                                    <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400\" />\r\n                                    <input\r\n                                        type=\"text\"\r\n                                        placeholder=\"Search positions by name...\"\r\n                                        value={search}\r\n                                        onChange={(e) => setSearch(e.target.value)}\r\n                                        className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n                                    />\r\n                                </div>\r\n                            </div>\r\n\r\n                            <div className=\"w-full sm:w-56\">\r\n                                <label className=\"block text-sm font-medium text-gray-700 mb-1\">Active Status</label>\r\n                                <select\r\n                                    value={isActiveOnly === '' ? '' : isActiveOnly ? 'true' : 'false'}\r\n                                    onChange={(e) => {\r\n                                        const val = e.target.value;\r\n                                        if (val === '') setIsActiveOnly('');\r\n                                        else setIsActiveOnly(val === 'true');\r\n                                        setPage(1);\r\n                                    }}\r\n                                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n                                >\r\n                                    <option value=\"\">All</option>\r\n                                    <option value=\"true\">Active</option>\r\n                                    <option value=\"false\">Inactive</option>\r\n                                </select>\r\n                            </div>\r\n                        </div>\r\n\r\n                        <div className=\"flex justify-between\">\r\n                            <button\r\n                                type=\"submit\"\r\n                                className=\"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\r\n                            >\r\n                                Search\r\n                            </button>\r\n                            {(search || isActiveOnly !== '') && (\r\n                                <button\r\n                                    type=\"button\"\r\n                                    onClick={clearFilters}\r\n                                    className=\"px-4 py-2 text-gray-600 hover:text-gray-800\"\r\n                                >\r\n                                    Clear Filters\r\n                                </button>\r\n                            )}\r\n                        </div>\r\n                    </form>\r\n                </div>\r\n\r\n                {/* Positions Table */}\r\n                <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden\">\r\n                    {isLoading ? (\r\n                        <div className=\"p-8 text-center\">\r\n                            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto\"></div>\r\n                            <p className=\"mt-2 text-gray-600\">Loading positions...</p>\r\n                        </div>\r\n                    ) : positionsData?.items?.length === 0 ? (\r\n                        <div className=\"p-8 text-center\">\r\n                            <Briefcase className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\r\n                            <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No positions found</h3>\r\n                            <p className=\"text-gray-600\">Try adjusting your search criteria or add a new position.</p>\r\n                        </div>\r\n                    ) : (\r\n                        <>\r\n                            <div className=\"overflow-x-auto\">\r\n                                <table className=\"min-w-full divide-y divide-gray-200\">\r\n                                    <thead className=\"bg-gray-50\">\r\n                                        <tr>\r\n                                            <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                                                Name\r\n                                            </th>\r\n                                            <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                                                Description\r\n                                            </th>\r\n                                            <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                                                Active\r\n                                            </th>\r\n                                            <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\r\n                                                Actions\r\n                                            </th>\r\n                                        </tr>\r\n                                    </thead>\r\n                                    <tbody className=\"bg-white divide-y divide-gray-200\">\r\n                                        {(positionsData?.items as unknown as PositionListItem[] | undefined)?.map((position) => (\r\n                                            <tr key={position.id} className=\"hover:bg-gray-50\">\r\n                                                <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">\r\n                                                    {position.name}\r\n                                                </td>\r\n                                                <td className=\"px-6 py-4 whitespace-pre-wrap text-sm text-gray-700\">\r\n                                                    {position.description || <span className=\"text-gray-400\">No description</span>}\r\n                                                </td>\r\n                                                <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                                                    {position.is_active ? (\r\n                                                        <span className=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800\">\r\n                                                            <CheckCircle2 className=\"h-4 w-4 mr-1\" /> Active\r\n                                                        </span>\r\n                                                    ) : (\r\n                                                        <span className=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800\">\r\n                                                            <XCircle className=\"h-4 w-4 mr-1\" /> Inactive\r\n                                                        </span>\r\n                                                    )}\r\n                                                </td>\r\n                                                <td className=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\r\n                                                    <div className=\"flex items-center justify-end space-x-2\">\r\n                                                        <button\r\n                                                            onClick={() => router.push(`/positions/${position.id}`)}\r\n                                                            className=\"text-blue-600 hover:text-blue-900 p-1\"\r\n                                                            title=\"View Details\"\r\n                                                        >\r\n                                                            <Eye className=\"h-4 w-4\" />\r\n                                                        </button>\r\n                                                        <button\r\n                                                            onClick={() => router.push(`/positions/${position.id}/edit`)}\r\n                                                            className=\"text-indigo-600 hover:text-indigo-900 p-1\"\r\n                                                            title=\"Edit Position\"\r\n                                                        >\r\n                                                            <Edit className=\"h-4 w-4\" />\r\n                                                        </button>\r\n                                                        <button\r\n                                                            onClick={() => handleDelete(position.id, position.name)}\r\n                                                            className=\"text-red-600 hover:text-red-900 p-1\"\r\n                                                            title=\"Delete Position\"\r\n                                                        >\r\n                                                            <Trash2 className=\"h-4 w-4\" />\r\n                                                        </button>\r\n                                                    </div>\r\n                                                </td>\r\n                                            </tr>\r\n                                        ))}\r\n                                    </tbody>\r\n                                </table>\r\n                            </div>\r\n\r\n                            {/* Pagination */}\r\n                            {totalPages > 1 && (\r\n                                <div className=\"bg-white px-4 py-3 border-t border-gray-200 sm:px-6\">\r\n                                    <div className=\"flex items-center justify-between\">\r\n                                        <div className=\"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between\">\r\n                                            <div>\r\n                                                <p className=\"text-sm text-gray-700\">\r\n                                                    Showing{' '}\r\n                                                    <span className=\"font-medium\">{(page - 1) * 10 + 1}</span> to{' '}\r\n                                                    <span className=\"font-medium\">\r\n                                                        {Math.min(page * 10, positionsData?.total || 0)}\r\n                                                    </span> of <span className=\"font-medium\">{positionsData?.total || 0}</span> results\r\n                                                </p>\r\n                                            </div>\r\n                                            <div>\r\n                                                <nav className=\"relative z-0 inline-flex rounded-md shadow-sm -space-x-px\">\r\n                                                    <button\r\n                                                        onClick={() => setPage(Math.max(1, page - 1))}\r\n                                                        disabled={page === 1}\r\n                                                        className=\"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50\"\r\n                                                    >\r\n                                                        <ChevronLeft className=\"h-5 w-5\" />\r\n                                                    </button>\r\n                                                    <span className=\"relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700\">\r\n                                                        {page} of {totalPages}\r\n                                                    </span>\r\n                                                    <button\r\n                                                        onClick={() => setPage(Math.min(totalPages, page + 1))}\r\n                                                        disabled={page === totalPages}\r\n                                                        className=\"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50\"\r\n                                                    >\r\n                                                        <ChevronRight className=\"h-5 w-5\" />\r\n                                                    </button>\r\n                                                </nav>\r\n                                            </div>\r\n                                        </div>\r\n                                    </div>\r\n                                </div>\r\n                            )}\r\n                        </>\r\n                    )}\r\n                </div>\r\n\r\n                <ConfirmDialog\r\n                    isOpen={confirmDelete.open}\r\n                    onClose={() => setConfirmDelete({ open: false })}\r\n                    onConfirm={confirmDeleteAction}\r\n                    title=\"Delete Position\"\r\n                    message={`Are you sure you want to delete \"${confirmDelete.name || 'this position'}\"? This action cannot be undone.`}\r\n                    confirmText={deletePosition.isPending ? 'Deleting...' : 'Delete'}\r\n                    confirmButtonClass=\"bg-red-600 hover:bg-red-700\"\r\n                />\r\n            </Layout>\r\n        </ProtectedRoute>\r\n    );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAVA;;;;;;;;;AAyBe,SAAS;QAmJA,sBA2BiB;;IA7KrC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB;IAC/D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiD;QAC9F,MAAM;IACV;IAEA,MAAM,EAAE,MAAM,aAAa,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,eAAY,AAAD,EAAE;QAC3D;QACA,MAAM;QACN,QAAQ,UAAU;QAClB,WAAW,iBAAiB,KAAK,YAAY,QAAQ;IACzD;IAEA,MAAM,iBAAiB,CAAA,GAAA,+HAAA,CAAA,oBAAiB,AAAD;IAEvC,MAAM,eAAe,CAAC;QAClB,EAAE,cAAc;QAChB,QAAQ;IACZ;IAEA,MAAM,eAAe,CAAC,IAAY;QAC9B,iBAAiB;YACb,MAAM;YACN;YACA;QACJ;IACJ;IAEA,MAAM,sBAAsB;QACxB,IAAI,cAAc,EAAE,EAAE;YAClB,eAAe,MAAM,CAAC,cAAc,EAAE;QAC1C;IACJ;IAEA,MAAM,eAAe;QACjB,UAAU;QACV,gBAAgB;QAChB,QAAQ;IACZ;IAEA,MAAM,aAAa,CAAA,0BAAA,oCAAA,cAAe,KAAK,KAAI;IAE3C,IAAI,OAAO;QACP,qBACI,6LAAC,+IAAA,CAAA,iBAAc;sBACX,cAAA,6LAAC,yIAAA,CAAA,SAAM;0BACH,cAAA,6LAAC;oBAAI,WAAU;8BACX,cAAA,6LAAC;wBAAI,WAAU;;0CACX,6LAAC;gCAAG,WAAU;0CAAwC;;;;;;0CACtD,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;IAMrD;IAEA,qBACI,6LAAC,+IAAA,CAAA,iBAAc;kBACX,cAAA,6LAAC,yIAAA,CAAA,SAAM;;8BAEH,6LAAC;oBAAI,WAAU;8BACX,cAAA,6LAAC;wBAAI,WAAU;;0CACX,6LAAC;gCAAI,WAAU;;kDACX,6LAAC,+MAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;kDACrB,6LAAC;;0DACG,6LAAC;gDAAG,WAAU;0DAAmC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;;;;;;;0CAGrC,6LAAC,+JAAA,CAAA,UAAI;gCACD,MAAK;gCACL,WAAU;;kDAEV,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;8BAO7C,6LAAC;oBAAI,WAAU;8BACX,cAAA,6LAAC;wBAAK,UAAU;wBAAc,WAAU;;0CACpC,6LAAC;gCAAI,WAAU;;kDACX,6LAAC;wCAAI,WAAU;kDACX,cAAA,6LAAC;4CAAI,WAAU;;8DACX,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,6LAAC;oDACG,MAAK;oDACL,aAAY;oDACZ,OAAO;oDACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;oDACzC,WAAU;;;;;;;;;;;;;;;;;kDAKtB,6LAAC;wCAAI,WAAU;;0DACX,6LAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAChE,6LAAC;gDACG,OAAO,iBAAiB,KAAK,KAAK,eAAe,SAAS;gDAC1D,UAAU,CAAC;oDACP,MAAM,MAAM,EAAE,MAAM,CAAC,KAAK;oDAC1B,IAAI,QAAQ,IAAI,gBAAgB;yDAC3B,gBAAgB,QAAQ;oDAC7B,QAAQ;gDACZ;gDACA,WAAU;;kEAEV,6LAAC;wDAAO,OAAM;kEAAG;;;;;;kEACjB,6LAAC;wDAAO,OAAM;kEAAO;;;;;;kEACrB,6LAAC;wDAAO,OAAM;kEAAQ;;;;;;;;;;;;;;;;;;;;;;;;0CAKlC,6LAAC;gCAAI,WAAU;;kDACX,6LAAC;wCACG,MAAK;wCACL,WAAU;kDACb;;;;;;oCAGA,CAAC,UAAU,iBAAiB,EAAE,mBAC3B,6LAAC;wCACG,MAAK;wCACL,SAAS;wCACT,WAAU;kDACb;;;;;;;;;;;;;;;;;;;;;;;8BASjB,6LAAC;oBAAI,WAAU;8BACV,0BACG,6LAAC;wBAAI,WAAU;;0CACX,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;+BAEtC,CAAA,0BAAA,qCAAA,uBAAA,cAAe,KAAK,cAApB,2CAAA,qBAAsB,MAAM,MAAK,kBACjC,6LAAC;wBAAI,WAAU;;0CACX,6LAAC,+MAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;0CACrB,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;6CAGjC;;0CACI,6LAAC;gCAAI,WAAU;0CACX,cAAA,6LAAC;oCAAM,WAAU;;sDACb,6LAAC;4CAAM,WAAU;sDACb,cAAA,6LAAC;;kEACG,6LAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,6LAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,6LAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,6LAAC;wDAAG,WAAU;kEAAkF;;;;;;;;;;;;;;;;;sDAKxG,6LAAC;4CAAM,WAAU;sDACZ,CAAA,QAAC,0BAAA,oCAAA,cAAe,KAAK,AAA6C,cAAlE,4BAAA,MAAqE,GAAG,CAAC,CAAC,yBACvE,6LAAC;oDAAqB,WAAU;;sEAC5B,6LAAC;4DAAG,WAAU;sEACT,SAAS,IAAI;;;;;;sEAElB,6LAAC;4DAAG,WAAU;sEACT,SAAS,WAAW,kBAAI,6LAAC;gEAAK,WAAU;0EAAgB;;;;;;;;;;;sEAE7D,6LAAC;4DAAG,WAAU;sEACT,SAAS,SAAS,iBACf,6LAAC;gEAAK,WAAU;;kFACZ,6LAAC,wNAAA,CAAA,eAAY;wEAAC,WAAU;;;;;;oEAAiB;;;;;;qFAG7C,6LAAC;gEAAK,WAAU;;kFACZ,6LAAC,+MAAA,CAAA,UAAO;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;sEAIhD,6LAAC;4DAAG,WAAU;sEACV,cAAA,6LAAC;gEAAI,WAAU;;kFACX,6LAAC;wEACG,SAAS,IAAM,OAAO,IAAI,CAAC,AAAC,cAAyB,OAAZ,SAAS,EAAE;wEACpD,WAAU;wEACV,OAAM;kFAEN,cAAA,6LAAC,mMAAA,CAAA,MAAG;4EAAC,WAAU;;;;;;;;;;;kFAEnB,6LAAC;wEACG,SAAS,IAAM,OAAO,IAAI,CAAC,AAAC,cAAyB,OAAZ,SAAS,EAAE,EAAC;wEACrD,WAAU;wEACV,OAAM;kFAEN,cAAA,6LAAC,8MAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;;;;;;kFAEpB,6LAAC;wEACG,SAAS,IAAM,aAAa,SAAS,EAAE,EAAE,SAAS,IAAI;wEACtD,WAAU;wEACV,OAAM;kFAEN,cAAA,6LAAC,6MAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;mDAvCzB,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;4BAkDnC,aAAa,mBACV,6LAAC;gCAAI,WAAU;0CACX,cAAA,6LAAC;oCAAI,WAAU;8CACX,cAAA,6LAAC;wCAAI,WAAU;;0DACX,6LAAC;0DACG,cAAA,6LAAC;oDAAE,WAAU;;wDAAwB;wDACzB;sEACR,6LAAC;4DAAK,WAAU;sEAAe,CAAC,OAAO,CAAC,IAAI,KAAK;;;;;;wDAAS;wDAAI;sEAC9D,6LAAC;4DAAK,WAAU;sEACX,KAAK,GAAG,CAAC,OAAO,IAAI,CAAA,0BAAA,oCAAA,cAAe,KAAK,KAAI;;;;;;wDAC1C;sEAAI,6LAAC;4DAAK,WAAU;sEAAe,CAAA,0BAAA,oCAAA,cAAe,KAAK,KAAI;;;;;;wDAAS;;;;;;;;;;;;0DAGnF,6LAAC;0DACG,cAAA,6LAAC;oDAAI,WAAU;;sEACX,6LAAC;4DACG,SAAS,IAAM,QAAQ,KAAK,GAAG,CAAC,GAAG,OAAO;4DAC1C,UAAU,SAAS;4DACnB,WAAU;sEAEV,cAAA,6LAAC,uNAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;;;;;;sEAE3B,6LAAC;4DAAK,WAAU;;gEACX;gEAAK;gEAAK;;;;;;;sEAEf,6LAAC;4DACG,SAAS,IAAM,QAAQ,KAAK,GAAG,CAAC,YAAY,OAAO;4DACnD,UAAU,SAAS;4DACnB,WAAU;sEAEV,cAAA,6LAAC,yNAAA,CAAA,eAAY;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAYhE,6LAAC,4IAAA,CAAA,UAAa;oBACV,QAAQ,cAAc,IAAI;oBAC1B,SAAS,IAAM,iBAAiB;4BAAE,MAAM;wBAAM;oBAC9C,WAAW;oBACX,OAAM;oBACN,SAAS,AAAC,oCAAyE,OAAtC,cAAc,IAAI,IAAI,iBAAgB;oBACnF,aAAa,eAAe,SAAS,GAAG,gBAAgB;oBACxD,oBAAmB;;;;;;;;;;;;;;;;;AAKvC;GAvRwB;;QACL,qIAAA,CAAA,YAAS;QAS0B,+HAAA,CAAA,eAAY;QAOvC,+HAAA,CAAA,oBAAiB;;;KAjBpB", "debugId": null}}]}