{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/backend/lc-workflow-frontend/src/components/layout/Sidebar.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport {\n  HomeIcon,\n  DocumentTextIcon,\n  UsersIcon,\n  BuildingOfficeIcon,\n  MapPinIcon,\n  Cog6ToothIcon,\n  FolderIcon,\n  ChevronDownIcon,\n  ChevronRightIcon,\n  XMarkIcon,\n  Bars3Icon,\n  BriefcaseIcon,\n} from '@heroicons/react/24/outline';\nimport { useAuthContext } from '@/providers/AuthProvider';\n\ninterface NavItem {\n  name: string;\n  href: string;\n  icon: React.ComponentType<{ className?: string }>;\n  requiredRoles?: string[];\n}\n\nconst navigation: NavItem[] = [\n  { name: 'Dashboard', href: '/dashboard', icon: HomeIcon },\n  { name: 'Applications', href: '/applications', icon: DocumentTextIcon },\n  { name: 'Files', href: '/files', icon: FolderIcon },\n];\n\nconst adminNavigation: NavItem[] = [\n  { name: 'Departments', href: '/departments', icon: BuildingOfficeIcon, requiredRoles: ['admin', 'manager'] },\n  { name: 'Positions', href: '/positions', icon: BriefcaseIcon, requiredRoles: ['admin', 'manager'] },\n  { name: 'Users', href: '/users', icon: UsersIcon, requiredRoles: ['admin', 'manager'] },\n  { name: 'Branches', href: '/branches', icon: MapPinIcon, requiredRoles: ['admin', 'manager'] },\n  { name: 'Settings', href: '/settings', icon: Cog6ToothIcon, requiredRoles: ['admin'] },\n];\n\ninterface SidebarProps {\n  isOpen: boolean;\n  onClose: () => void;\n}\n\nexport function Sidebar({ isOpen, onClose }: SidebarProps) {\n  const pathname = usePathname();\n  const { user, isAdmin, isManager } = useAuthContext();\n\n  const hasRequiredRole = (requiredRoles?: string[]) => {\n    if (!requiredRoles) return true;\n    return isAdmin || (isManager && requiredRoles.includes('manager'));\n  };\n\n  const allNavigation = [...navigation, ...adminNavigation];\n\n  const NavLink = ({ item }: { item: NavItem }) => {\n    const isActive = pathname === item.href;\n\n    if (!hasRequiredRole(item.requiredRoles)) return null;\n\n    return (\n      <Link\n        href={item.href}\n        className={`group flex items-center gap-x-3 rounded-md p-2 text-sm font-semibold leading-6 transition-all duration-200 ${isActive\n            ? 'bg-blue-600 text-white'\n            : 'text-gray-700 hover:bg-gray-50 hover:text-blue-600'\n          }`}\n        onClick={onClose}\n      >\n        <item.icon className=\"h-5 w-5 shrink-0\" />\n        {item.name}\n      </Link>\n    );\n  };\n\n  return (\n    <>\n      {/* Mobile sidebar */}\n      <div\n        className={`fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:hidden ${isOpen ? 'translate-x-0' : '-translate-x-full'\n          }`}\n      >\n        <div className=\"flex h-16 items-center justify-between px-4\">\n          <div className=\"text-xl font-bold text-gray-900\">LC Workflow</div>\n          <button\n            type=\"button\"\n            className=\"-m-2.5 p-2.5 text-gray-700\"\n            onClick={onClose}\n          >\n            <XMarkIcon className=\"h-6 w-6\" />\n          </button>\n        </div>\n        <nav className=\"flex flex-1 flex-col gap-y-2 p-4\">\n          {allNavigation.map((item) => (\n            <NavLink key={item.name} item={item} />\n          ))}\n        </nav>\n      </div>\n\n      {/* Desktop sidebar */}\n      <div className=\"hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-64 lg:flex-col\">\n        <div className=\"flex grow flex-col gap-y-5 overflow-y-auto bg-white px-6 ring-1 ring-gray-200\">\n          <div className=\"flex h-16 shrink-0 items-center\">\n            <div className=\"text-xl font-bold text-gray-900\">LC Workflow</div>\n          </div>\n          <nav className=\"flex flex-1 flex-col gap-y-2\">\n            {allNavigation.map((item) => (\n              <NavLink key={item.name} item={item} />\n            ))}\n          </nav>\n\n          {/* User info */}\n          {user && (\n            <div className=\"mb-4\">\n              <div className=\"flex items-center gap-x-3 rounded-md p-2 text-sm font-medium text-gray-700\">\n                <div className=\"h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center\">\n                  <span className=\"text-sm font-medium text-gray-600\">\n                    {user.first_name.charAt(0)}{user.last_name.charAt(0)}\n                  </span>\n                </div>\n                <div>\n                  <div className=\"font-medium\">{user.first_name} {user.last_name}</div>\n                  <div className=\"text-xs text-gray-500\">{user.role}</div>\n                </div>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </>\n  );\n}"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;;;AAnBA;;;;;AA4BA,MAAM,aAAwB;IAC5B;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,kNAAA,CAAA,WAAQ;IAAC;IACxD;QAAE,MAAM;QAAgB,MAAM;QAAiB,MAAM,kOAAA,CAAA,mBAAgB;IAAC;IACtE;QAAE,MAAM;QAAS,MAAM;QAAU,MAAM,sNAAA,CAAA,aAAU;IAAC;CACnD;AAED,MAAM,kBAA6B;IACjC;QAAE,MAAM;QAAe,MAAM;QAAgB,MAAM,sOAAA,CAAA,qBAAkB;QAAE,eAAe;YAAC;YAAS;SAAU;IAAC;IAC3G;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,4NAAA,CAAA,gBAAa;QAAE,eAAe;YAAC;YAAS;SAAU;IAAC;IAClG;QAAE,MAAM;QAAS,MAAM;QAAU,MAAM,oNAAA,CAAA,YAAS;QAAE,eAAe;YAAC;YAAS;SAAU;IAAC;IACtF;QAAE,MAAM;QAAY,MAAM;QAAa,MAAM,sNAAA,CAAA,aAAU;QAAE,eAAe;YAAC;YAAS;SAAU;IAAC;IAC7F;QAAE,MAAM;QAAY,MAAM;QAAa,MAAM,4NAAA,CAAA,gBAAa;QAAE,eAAe;YAAC;SAAQ;IAAC;CACtF;AAOM,SAAS,QAAQ,KAAiC;QAAjC,EAAE,MAAM,EAAE,OAAO,EAAgB,GAAjC;;IACtB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,iBAAc,AAAD;IAElD,MAAM,kBAAkB,CAAC;QACvB,IAAI,CAAC,eAAe,OAAO;QAC3B,OAAO,WAAY,aAAa,cAAc,QAAQ,CAAC;IACzD;IAEA,MAAM,gBAAgB;WAAI;WAAe;KAAgB;IAEzD,MAAM,UAAU;YAAC,EAAE,IAAI,EAAqB;QAC1C,MAAM,WAAW,aAAa,KAAK,IAAI;QAEvC,IAAI,CAAC,gBAAgB,KAAK,aAAa,GAAG,OAAO;QAEjD,qBACE,6LAAC,+JAAA,CAAA,UAAI;YACH,MAAM,KAAK,IAAI;YACf,WAAW,AAAC,8GAGT,OAHsH,WACnH,2BACA;YAEN,SAAS;;8BAET,6LAAC,KAAK,IAAI;oBAAC,WAAU;;;;;;gBACpB,KAAK,IAAI;;;;;;;IAGhB;IAEA,qBACE;;0BAEE,6LAAC;gBACC,WAAW,AAAC,yHACT,OADiI,SAAS,kBAAkB;;kCAG/J,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAAkC;;;;;;0CACjD,6LAAC;gCACC,MAAK;gCACL,WAAU;gCACV,SAAS;0CAET,cAAA,6LAAC,oNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAGzB,6LAAC;wBAAI,WAAU;kCACZ,cAAc,GAAG,CAAC,CAAC,qBAClB,6LAAC;gCAAwB,MAAM;+BAAjB,KAAK,IAAI;;;;;;;;;;;;;;;;0BAM7B,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CAAkC;;;;;;;;;;;sCAEnD,6LAAC;4BAAI,WAAU;sCACZ,cAAc,GAAG,CAAC,CAAC,qBAClB,6LAAC;oCAAwB,MAAM;mCAAjB,KAAK,IAAI;;;;;;;;;;wBAK1B,sBACC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;;gDACb,KAAK,UAAU,CAAC,MAAM,CAAC;gDAAI,KAAK,SAAS,CAAC,MAAM,CAAC;;;;;;;;;;;;kDAGtD,6LAAC;;0DACC,6LAAC;gDAAI,WAAU;;oDAAe,KAAK,UAAU;oDAAC;oDAAE,KAAK,SAAS;;;;;;;0DAC9D,6LAAC;gDAAI,WAAU;0DAAyB,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASnE;GAvFgB;;QACG,qIAAA,CAAA,cAAW;QACS,oIAAA,CAAA,iBAAc;;;KAFrC", "debugId": null}}, {"offset": {"line": 316, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/backend/lc-workflow-frontend/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useRef, useEffect } from 'react';\r\nimport { Bars3Icon, BellIcon, UserCircleIcon, ArrowRightOnRectangleIcon, Cog6ToothIcon } from '@heroicons/react/24/outline';\r\nimport { useLogout } from '@/hooks/useAuth';\r\nimport { useAuthContext } from '@/providers/AuthProvider';\r\nimport Link from 'next/link';\r\n\r\ninterface HeaderProps {\r\n  onMenuClick: () => void;\r\n}\r\n\r\nexport function Header({ onMenuClick }: HeaderProps) {\r\n  const logout = useLogout();\r\n  const { user } = useAuthContext();\r\n  const [profileDropdownOpen, setProfileDropdownOpen] = useState(false);\r\n  const dropdownRef = useRef<HTMLDivElement>(null);\r\n\r\n  // Close dropdown when clicking outside\r\n  useEffect(() => {\r\n    function handleClickOutside(event: MouseEvent) {\r\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\r\n        setProfileDropdownOpen(false);\r\n      }\r\n    }\r\n\r\n    document.addEventListener('mousedown', handleClickOutside);\r\n    return () => {\r\n      document.removeEventListener('mousedown', handleClickOutside);\r\n    };\r\n  }, []);\r\n\r\n  return (\r\n    <div className=\"sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 bg-white px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8\">\r\n      <button\r\n        type=\"button\"\r\n        className=\"-m-2.5 p-2.5 text-gray-700 lg:hidden\"\r\n        onClick={onMenuClick}\r\n      >\r\n        <span className=\"sr-only\">Open sidebar</span>\r\n        <Bars3Icon className=\"h-6 w-6\" aria-hidden=\"true\" />\r\n      </button>\r\n\r\n      {/* Separator */}\r\n      <div className=\"h-6 w-px bg-gray-900/10 lg:hidden\" aria-hidden=\"true\" />\r\n\r\n      <div className=\"flex flex-1 gap-x-4 self-stretch lg:gap-x-6\">\r\n        <div className=\"flex flex-1\" />\r\n\r\n        <div className=\"flex items-center gap-x-4 lg:gap-x-6\">\r\n          <button\r\n            type=\"button\"\r\n            className=\"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500\"\r\n          >\r\n            <span className=\"sr-only\">View notifications</span>\r\n            <BellIcon className=\"h-6 w-6\" aria-hidden=\"true\" />\r\n          </button>\r\n\r\n          {/* Separator */}\r\n          <div className=\"hidden lg:block lg:h-6 lg:w-px lg:bg-gray-900/10\" aria-hidden=\"true\" />\r\n\r\n          {/* Profile dropdown */}\r\n          <div className=\"relative\">\r\n            <button\r\n              type=\"button\"\r\n              className=\"-m-1.5 flex items-center p-1.5\"\r\n              id=\"user-menu-button\"\r\n              aria-expanded=\"false\"\r\n              aria-haspopup=\"true\"\r\n              onClick={() => setProfileDropdownOpen(!profileDropdownOpen)}\r\n            >\r\n              <span className=\"sr-only\">Open user menu</span>\r\n              <div className=\"h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center\">\r\n                <span className=\"text-sm font-medium text-gray-600\">\r\n                  {user?.first_name?.charAt(0)}{user?.last_name?.charAt(0)}\r\n                </span>\r\n              </div>\r\n              <span className=\"hidden lg:flex lg:items-center\">\r\n                <span className=\"ml-4 text-sm leading-6 text-gray-900\" aria-hidden=\"true\">\r\n                  <span className=\"font-semibold\">{user?.first_name} {user?.last_name}</span>\r\n                  {user?.position?.title ? (\r\n                    <span className=\"ml-2 text-gray-500 truncate max-w-[10rem]\">\r\n                      • {user?.position?.title}\r\n                    </span>\r\n                  ) : null}\r\n                </span>\r\n              </span>\r\n            </button>\r\n\r\n            {/* Dropdown menu */}\r\n            {profileDropdownOpen && (\r\n              <div\r\n                className=\"absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none\"\r\n                role=\"menu\"\r\n                aria-orientation=\"vertical\"\r\n                aria-labelledby=\"user-menu-button\"\r\n                ref={dropdownRef}\r\n              >\r\n                <div className=\"px-4 py-3\">\r\n                  <p className=\"text-sm\">Signed in as</p>\r\n                  <p className=\"truncate text-sm font-medium text-gray-900\">{user?.email}</p>\r\n                </div>\r\n                <div className=\"border-t border-gray-100\"></div>\r\n                <Link\r\n                  href=\"/profile\"\r\n                  className=\"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\r\n                  role=\"menuitem\"\r\n                  onClick={() => setProfileDropdownOpen(false)}\r\n                >\r\n                  <UserCircleIcon className=\"mr-3 h-5 w-5 text-gray-400\" aria-hidden=\"true\" />\r\n                  Your Profile\r\n                </Link>\r\n                <Link\r\n                  href=\"/settings\"\r\n                  className=\"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\r\n                  role=\"menuitem\"\r\n                  onClick={() => setProfileDropdownOpen(false)}\r\n                >\r\n                  <Cog6ToothIcon className=\"mr-3 h-5 w-5 text-gray-400\" aria-hidden=\"true\" />\r\n                  Settings\r\n                </Link>\r\n                <div className=\"border-t border-gray-100\"></div>\r\n                <button\r\n                  type=\"button\"\r\n                  className=\"flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\r\n                  role=\"menuitem\"\r\n                  onClick={() => {\r\n                    setProfileDropdownOpen(false);\r\n                    logout.mutate();\r\n                  }}\r\n                >\r\n                  <ArrowRightOnRectangleIcon className=\"mr-3 h-5 w-5 text-gray-400\" aria-hidden=\"true\" />\r\n                  Sign out\r\n                </button>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;AANA;;;;;;AAYO,SAAS,OAAO,KAA4B;QAA5B,EAAE,WAAW,EAAe,GAA5B;QA8DJ,kBAA6B,iBAM7B,gBAEM;;IArEvB,MAAM,SAAS,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,iBAAc,AAAD;IAC9B,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAE3C,uCAAuC;IACvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,SAAS,mBAAmB,KAAiB;gBAC3C,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;oBAC9E,uBAAuB;gBACzB;YACF;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC;oCAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;gBAC5C;;QACF;2BAAG,EAAE;IAEL,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACC,MAAK;gBACL,WAAU;gBACV,SAAS;;kCAET,6LAAC;wBAAK,WAAU;kCAAU;;;;;;kCAC1B,6LAAC,oNAAA,CAAA,YAAS;wBAAC,WAAU;wBAAU,eAAY;;;;;;;;;;;;0BAI7C,6LAAC;gBAAI,WAAU;gBAAoC,eAAY;;;;;;0BAE/D,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCAEf,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,MAAK;gCACL,WAAU;;kDAEV,6LAAC;wCAAK,WAAU;kDAAU;;;;;;kDAC1B,6LAAC,kNAAA,CAAA,WAAQ;wCAAC,WAAU;wCAAU,eAAY;;;;;;;;;;;;0CAI5C,6LAAC;gCAAI,WAAU;gCAAmD,eAAY;;;;;;0CAG9E,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,WAAU;wCACV,IAAG;wCACH,iBAAc;wCACd,iBAAc;wCACd,SAAS,IAAM,uBAAuB,CAAC;;0DAEvC,6LAAC;gDAAK,WAAU;0DAAU;;;;;;0DAC1B,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;;wDACb,iBAAA,4BAAA,mBAAA,KAAM,UAAU,cAAhB,uCAAA,iBAAkB,MAAM,CAAC;wDAAI,iBAAA,4BAAA,kBAAA,KAAM,SAAS,cAAf,sCAAA,gBAAiB,MAAM,CAAC;;;;;;;;;;;;0DAG1D,6LAAC;gDAAK,WAAU;0DACd,cAAA,6LAAC;oDAAK,WAAU;oDAAuC,eAAY;;sEACjE,6LAAC;4DAAK,WAAU;;gEAAiB,iBAAA,2BAAA,KAAM,UAAU;gEAAC;gEAAE,iBAAA,2BAAA,KAAM,SAAS;;;;;;;wDAClE,CAAA,iBAAA,4BAAA,iBAAA,KAAM,QAAQ,cAAd,qCAAA,eAAgB,KAAK,kBACpB,6LAAC;4DAAK,WAAU;;gEAA4C;gEACvD,iBAAA,4BAAA,kBAAA,KAAM,QAAQ,cAAd,sCAAA,gBAAgB,KAAK;;;;;;mEAExB;;;;;;;;;;;;;;;;;;oCAMT,qCACC,6LAAC;wCACC,WAAU;wCACV,MAAK;wCACL,oBAAiB;wCACjB,mBAAgB;wCAChB,KAAK;;0DAEL,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAU;;;;;;kEACvB,6LAAC;wDAAE,WAAU;kEAA8C,iBAAA,2BAAA,KAAM,KAAK;;;;;;;;;;;;0DAExE,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,MAAK;gDACL,SAAS,IAAM,uBAAuB;;kEAEtC,6LAAC,8NAAA,CAAA,iBAAc;wDAAC,WAAU;wDAA6B,eAAY;;;;;;oDAAS;;;;;;;0DAG9E,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,MAAK;gDACL,SAAS,IAAM,uBAAuB;;kEAEtC,6LAAC,4NAAA,CAAA,gBAAa;wDAAC,WAAU;wDAA6B,eAAY;;;;;;oDAAS;;;;;;;0DAG7E,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDACC,MAAK;gDACL,WAAU;gDACV,MAAK;gDACL,SAAS;oDACP,uBAAuB;oDACvB,OAAO,MAAM;gDACf;;kEAEA,6LAAC,oPAAA,CAAA,4BAAyB;wDAAC,WAAU;wDAA6B,eAAY;;;;;;oDAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUzG;GAjIgB;;QACC,0HAAA,CAAA,YAAS;QACP,oIAAA,CAAA,iBAAc;;;KAFjB", "debugId": null}}, {"offset": {"line": 690, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/backend/lc-workflow-frontend/src/components/layout/Layout.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Sidebar } from './Sidebar';\nimport { Header } from './Header';\n\ninterface LayoutProps {\n  children: React.ReactNode;\n}\n\nexport function Layout({ children }: LayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n\n  return (\n    <>\n      <div className=\"min-h-full\">\n        <Sidebar \n          isOpen={sidebarOpen} \n          onClose={() => setSidebarOpen(false)} \n        />\n        \n        <div className=\"lg:pl-64\">\n          <Header onMenuClick={() => setSidebarOpen(true)} />\n          \n          <main className=\"py-10\">\n            <div className=\"px-4 sm:px-6 lg:px-8\">\n              {children}\n            </div>\n          </main>\n        </div>\n      </div>\n    </>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAUO,SAAS,OAAO,KAAyB;QAAzB,EAAE,QAAQ,EAAe,GAAzB;;IACrB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,qBACE;kBACE,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,0IAAA,CAAA,UAAO;oBACN,QAAQ;oBACR,SAAS,IAAM,eAAe;;;;;;8BAGhC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,yIAAA,CAAA,SAAM;4BAAC,aAAa,IAAM,eAAe;;;;;;sCAE1C,6LAAC;4BAAK,WAAU;sCACd,cAAA,6LAAC;gCAAI,WAAU;0CACZ;;;;;;;;;;;;;;;;;;;;;;;;AAOf;GAvBgB;KAAA", "debugId": null}}, {"offset": {"line": 771, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/backend/lc-workflow-frontend/src/components/auth/ProtectedRoute.tsx"], "sourcesContent": ["'use client';\n\nimport { useRouter } from 'next/navigation';\nimport { useAuthContext } from '@/providers/AuthProvider';\nimport { useEffect } from 'react';\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode;\n  requiredRoles?: string[];\n}\n\nexport function ProtectedRoute({ children, requiredRoles }: ProtectedRouteProps) {\n  const router = useRouter();\n  const { isAuthenticated, isLoading, user, role } = useAuthContext();\n\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      if (!isLoading) {\n        if (!isAuthenticated) {\n          router.push('/login');\n        } else if (requiredRoles && requiredRoles.length > 0 && !requiredRoles.includes(role || '')) {\n          router.push('/unauthorized');\n        }\n      }\n    }, 1000); // Wait 1 second for auth state to stabilize\n\n    return () => clearTimeout(timer);\n  }, [isAuthenticated, isLoading, requiredRoles, role, router]);\n\n  if (isLoading || !isAuthenticated) {\n    return (\n      <div className=\"flex min-h-screen items-center justify-center\">\n        <div className=\"h-8 w-8 animate-spin rounded-full border-4 border-blue-600 border-t-transparent\"></div>\n      </div>\n    );\n  }\n\n  if (requiredRoles && requiredRoles.length > 0 && !requiredRoles.includes(role || '')) {\n    return null;\n  }\n\n  return <>{children}</>;\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAWO,SAAS,eAAe,KAAgD;QAAhD,EAAE,QAAQ,EAAE,aAAa,EAAuB,GAAhD;;IAC7B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,iBAAc,AAAD;IAEhE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM,QAAQ;kDAAW;oBACvB,IAAI,CAAC,WAAW;wBACd,IAAI,CAAC,iBAAiB;4BACpB,OAAO,IAAI,CAAC;wBACd,OAAO,IAAI,iBAAiB,cAAc,MAAM,GAAG,KAAK,CAAC,cAAc,QAAQ,CAAC,QAAQ,KAAK;4BAC3F,OAAO,IAAI,CAAC;wBACd;oBACF;gBACF;iDAAG,OAAO,4CAA4C;YAEtD;4CAAO,IAAM,aAAa;;QAC5B;mCAAG;QAAC;QAAiB;QAAW;QAAe;QAAM;KAAO;IAE5D,IAAI,aAAa,CAAC,iBAAiB;QACjC,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,iBAAiB,cAAc,MAAM,GAAG,KAAK,CAAC,cAAc,QAAQ,CAAC,QAAQ,KAAK;QACpF,OAAO;IACT;IAEA,qBAAO;kBAAG;;AACZ;GA/BgB;;QACC,qIAAA,CAAA,YAAS;QAC2B,oIAAA,CAAA,iBAAc;;;KAFnD", "debugId": null}}, {"offset": {"line": 854, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/backend/lc-workflow-frontend/src/hooks/useDashboard.ts"], "sourcesContent": ["import { useQuery } from '@tanstack/react-query';\r\nimport { apiClient } from '@/lib/api';\r\n\r\n// Dashboard query keys\r\nexport const dashboardKeys = {\r\n  all: ['dashboard'] as const,\r\n  stats: () => [...dashboardKeys.all, 'stats'] as const,\r\n  recentApplications: (limit?: number) => [...dashboardKeys.all, 'recent-applications', limit] as const,\r\n  activityTimeline: (days?: number) => [...dashboardKeys.all, 'activity-timeline', days] as const,\r\n  performanceMetrics: () => [...dashboardKeys.all, 'performance-metrics'] as const,\r\n};\r\n\r\n// Dashboard statistics interface\r\nexport interface DashboardStats {\r\n  applications: {\r\n    total: number;\r\n    draft: number;\r\n    submitted: number;\r\n    pending: number;\r\n    under_review: number;\r\n    approved: number;\r\n    rejected: number;\r\n  };\r\n  users: {\r\n    total: number;\r\n    active: number;\r\n    inactive: number;\r\n    admins: number;\r\n    managers: number;\r\n    officers: number;\r\n    viewers: number;\r\n  };\r\n  departments: {\r\n    total: number;\r\n    active: number;\r\n  };\r\n  branches: {\r\n    total: number;\r\n    active: number;\r\n  };\r\n  files: {\r\n    total: number;\r\n    total_size: number;\r\n  };\r\n}\r\n\r\n// Recent application interface\r\nexport interface RecentApplication {\r\n  phone: any;\r\n  portfolio_officer_name: any;\r\n  id: string;\r\n  full_name_latin?: string;\r\n  full_name_khmer?: string;\r\n  requested_amount?: number;\r\n  status: string;\r\n  created_at: string;\r\n  user_id: string;\r\n}\r\n\r\n// Activity timeline interface\r\nexport interface ActivityItem {\r\n  id: string;\r\n  type: 'application' | 'user';\r\n  action: string;\r\n  title: string;\r\n  description: string;\r\n  status: string;\r\n  timestamp: string;\r\n  user_id: string;\r\n}\r\n\r\n// Performance metrics interface\r\nexport interface PerformanceMetrics {\r\n  applications_processed_30d: number;\r\n  average_processing_time_days: number;\r\n  approval_rate_percentage: number;\r\n  active_users_today: number;\r\n}\r\n\r\n// Dashboard hooks\r\nexport const useDashboardStats = () => {\r\n  return useQuery({\r\n    queryKey: dashboardKeys.stats(),\r\n    queryFn: (): Promise<DashboardStats> => apiClient.get('/dashboard/stats'),\r\n    staleTime: 2 * 60 * 1000, // 2 minutes\r\n    gcTime: 5 * 60 * 1000, // 5 minutes (formerly cacheTime)\r\n    refetchOnWindowFocus: true,\r\n    refetchInterval: 5 * 60 * 1000, // Refetch every 5 minutes\r\n  });\r\n};\r\n\r\nexport const useRecentApplications = (limit: number = 10) => {\r\n  return useQuery({\r\n    queryKey: dashboardKeys.recentApplications(limit),\r\n    queryFn: (): Promise<RecentApplication[]> => \r\n      apiClient.get(`/dashboard/recent-applications?limit=${limit}`),\r\n    staleTime: 1 * 60 * 1000, // 1 minute\r\n    gcTime: 3 * 60 * 1000, // 3 minutes\r\n    refetchOnWindowFocus: true,\r\n  });\r\n};\r\n\r\nexport const useActivityTimeline = (days: number = 7) => {\r\n  return useQuery({\r\n    queryKey: dashboardKeys.activityTimeline(days),\r\n    queryFn: (): Promise<ActivityItem[]> => \r\n      apiClient.get(`/dashboard/activity-timeline?days=${days}`),\r\n    staleTime: 2 * 60 * 1000, // 2 minutes\r\n    gcTime: 5 * 60 * 1000, // 5 minutes\r\n    refetchOnWindowFocus: true,\r\n  });\r\n};\r\n\r\nexport const usePerformanceMetrics = () => {\r\n  return useQuery({\r\n    queryKey: dashboardKeys.performanceMetrics(),\r\n    queryFn: (): Promise<PerformanceMetrics> => apiClient.get('/dashboard/performance-metrics'),\r\n    staleTime: 5 * 60 * 1000, // 5 minutes\r\n    gcTime: 10 * 60 * 1000, // 10 minutes\r\n    refetchOnWindowFocus: false, // Don't refetch on window focus for performance metrics\r\n    refetchInterval: 10 * 60 * 1000, // Refetch every 10 minutes\r\n  });\r\n};\r\n\r\n// Legacy hooks for backward compatibility\r\nexport const useApplicationStats = () => {\r\n  const { data: dashboardStats, ...rest } = useDashboardStats();\r\n  \r\n  return {\r\n    data: dashboardStats?.applications,\r\n    ...rest,\r\n  };\r\n};\r\n\r\nexport const useUserStats = () => {\r\n  const { data: dashboardStats, ...rest } = useDashboardStats();\r\n  \r\n  return {\r\n    data: {\r\n      total_users: dashboardStats?.users.total,\r\n      total_departments: dashboardStats?.departments.total,\r\n      total_branches: dashboardStats?.branches.total,\r\n      ...dashboardStats?.users,\r\n    },\r\n    ...rest,\r\n  };\r\n};"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAGO,MAAM,gBAAgB;IAC3B,KAAK;QAAC;KAAY;IAClB,OAAO,IAAM;eAAI,cAAc,GAAG;YAAE;SAAQ;IAC5C,oBAAoB,CAAC,QAAmB;eAAI,cAAc,GAAG;YAAE;YAAuB;SAAM;IAC5F,kBAAkB,CAAC,OAAkB;eAAI,cAAc,GAAG;YAAE;YAAqB;SAAK;IACtF,oBAAoB,IAAM;eAAI,cAAc,GAAG;YAAE;SAAsB;AACzE;AAsEO,MAAM,oBAAoB;;IAC/B,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,cAAc,KAAK;QAC7B,OAAO;0CAAE,IAA+B,oHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;;QACtD,WAAW,IAAI,KAAK;QACpB,QAAQ,IAAI,KAAK;QACjB,sBAAsB;QACtB,iBAAiB,IAAI,KAAK;IAC5B;AACF;GATa;;QACJ,8KAAA,CAAA,WAAQ;;;AAUV,MAAM,wBAAwB;QAAC,yEAAgB;;IACpD,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,cAAc,kBAAkB,CAAC;QAC3C,OAAO;8CAAE,IACP,oHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,wCAA6C,OAAN;;QACxD,WAAW,IAAI,KAAK;QACpB,QAAQ,IAAI,KAAK;QACjB,sBAAsB;IACxB;AACF;IATa;;QACJ,8KAAA,CAAA,WAAQ;;;AAUV,MAAM,sBAAsB;QAAC,wEAAe;;IACjD,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,cAAc,gBAAgB,CAAC;QACzC,OAAO;4CAAE,IACP,oHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,qCAAyC,OAAL;;QACrD,WAAW,IAAI,KAAK;QACpB,QAAQ,IAAI,KAAK;QACjB,sBAAsB;IACxB;AACF;IATa;;QACJ,8KAAA,CAAA,WAAQ;;;AAUV,MAAM,wBAAwB;;IACnC,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,cAAc,kBAAkB;QAC1C,OAAO;8CAAE,IAAmC,oHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;;QAC1D,WAAW,IAAI,KAAK;QACpB,QAAQ,KAAK,KAAK;QAClB,sBAAsB;QACtB,iBAAiB,KAAK,KAAK;IAC7B;AACF;IATa;;QACJ,8KAAA,CAAA,WAAQ;;;AAWV,MAAM,sBAAsB;;IACjC,MAAM,EAAE,MAAM,cAAc,EAAE,GAAG,MAAM,GAAG;IAE1C,OAAO;QACL,IAAI,EAAE,2BAAA,qCAAA,eAAgB,YAAY;QAClC,GAAG,IAAI;IACT;AACF;IAPa;;QAC+B;;;AAQrC,MAAM,eAAe;;IAC1B,MAAM,EAAE,MAAM,cAAc,EAAE,GAAG,MAAM,GAAG;IAE1C,OAAO;QACL,MAAM;YACJ,WAAW,EAAE,2BAAA,qCAAA,eAAgB,KAAK,CAAC,KAAK;YACxC,iBAAiB,EAAE,2BAAA,qCAAA,eAAgB,WAAW,CAAC,KAAK;YACpD,cAAc,EAAE,2BAAA,qCAAA,eAAgB,QAAQ,CAAC,KAAK;eAC3C,2BAAA,qCAAA,eAAgB,KAAK,AAAxB;QACF;QACA,GAAG,IAAI;IACT;AACF;IAZa;;QAC+B", "debugId": null}}, {"offset": {"line": 1003, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/backend/lc-workflow-frontend/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\nexport function formatBytes(bytes: number, decimals = 2): string {\n  if (bytes === 0) return '0 Bytes';\n\n  const k = 1024;\n  const dm = decimals < 0 ? 0 : decimals;\n  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];\n\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];\n}\n\nexport function formatDate(dateString: string): string {\n  const date = new Date(dateString);\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  }).format(date);\n}\n\nexport function formatCurrency(amount: number, currency = 'USD'): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency,\n  }).format(amount);\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text;\n  return text.slice(0, maxLength) + '...';\n}\n\nexport function getFileIcon(mimeType: string): string {\n  if (mimeType.startsWith('image/')) return '🖼️';\n  if (mimeType === 'application/pdf') return '📄';\n  if (mimeType.includes('word')) return '📝';\n  if (mimeType.includes('excel') || mimeType.includes('spreadsheet')) return '📊';\n  if (mimeType.startsWith('text/')) return '📄';\n  return '📎';\n}\n\nexport function validateEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\nexport function validatePhone(phone: string): boolean {\n  const phoneRegex = /^\\+?[\\d\\s\\-\\(\\)]+$/;\n  return phoneRegex.test(phone) && phone.replace(/\\D/g, '').length >= 10;\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9);\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean;\n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args);\n      inThrottle = true;\n      setTimeout(() => (inThrottle = false), limit);\n    }\n  };\n}\n\nexport function isValidUUID(uuid: string): boolean {\n  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;\n  return uuidRegex.test(uuid);\n}\n\nexport function validateUUID(uuid: string): string {\n  if (!isValidUUID(uuid)) {\n    throw new Error('Invalid UUID format');\n  }\n  return uuid;\n}\n\nexport function sanitizeUUID(uuid: string): string {\n  return uuid.replace(/[^a-f0-9-]/gi, '');\n}"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,YAAY,KAAa;QAAE,WAAA,iEAAW;IACpD,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,KAAK,WAAW,IAAI,IAAI;IAC9B,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;KAAK;IAEvE,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,OAAO,MAAM,KAAK,CAAC,EAAE;AAC1E;AAEO,SAAS,WAAW,UAAkB;IAC3C,MAAM,OAAO,IAAI,KAAK;IACtB,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,eAAe,MAAc;QAAE,WAAA,iEAAW;IACxD,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP;IACF,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,aAAa;AACpC;AAEO,SAAS,YAAY,QAAgB;IAC1C,IAAI,SAAS,UAAU,CAAC,WAAW,OAAO;IAC1C,IAAI,aAAa,mBAAmB,OAAO;IAC3C,IAAI,SAAS,QAAQ,CAAC,SAAS,OAAO;IACtC,IAAI,SAAS,QAAQ,CAAC,YAAY,SAAS,QAAQ,CAAC,gBAAgB,OAAO;IAC3E,IAAI,SAAS,UAAU,CAAC,UAAU,OAAO;IACzC,OAAO;AACT;AAEO,SAAS,cAAc,KAAa;IACzC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,cAAc,KAAa;IACzC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC,UAAU,MAAM,OAAO,CAAC,OAAO,IAAI,MAAM,IAAI;AACtE;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO;yCAAI;YAAA;;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,SACd,IAAO,EACP,KAAa;IAEb,IAAI;IACJ,OAAO;yCAAI;YAAA;;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW,IAAO,aAAa,OAAQ;QACzC;IACF;AACF;AAEO,SAAS,YAAY,IAAY;IACtC,MAAM,YAAY;IAClB,OAAO,UAAU,IAAI,CAAC;AACxB;AAEO,SAAS,aAAa,IAAY;IACvC,IAAI,CAAC,YAAY,OAAO;QACtB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAEO,SAAS,aAAa,IAAY;IACvC,OAAO,KAAK,OAAO,CAAC,gBAAgB;AACtC", "debugId": null}}, {"offset": {"line": 1133, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/backend/lc-workflow-frontend/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client';\n\nimport { Layout } from '@/components/layout/Layout';\nimport { ProtectedRoute } from '@/components/auth/ProtectedRoute';\nimport { \n  useDashboardStats, \n  useRecentApplications, \n  useActivityTimeline, \n  usePerformanceMetrics \n} from '@/hooks/useDashboard';\nimport { useAuth } from '@/hooks/useAuth';\nimport { \n  DocumentTextIcon, \n  UsersIcon, \n  BuildingOfficeIcon, \n  MapPinIcon,\n  CheckCircleIcon,\n  ClockIcon,\n  XCircleIcon,\n  FolderIcon,\n  ArrowTrendingUpIcon,\n  CalendarIcon,\n  ChartBarIcon,\n  ArrowPathIcon,\n  CurrencyDollarIcon,\n  PhoneIcon,\n  UserIcon,\n  PlusIcon\n} from '@heroicons/react/24/outline';\nimport { formatCurrency, formatBytes, formatDate } from '@/lib/utils';\nimport Link from 'next/link';\n\nexport default function DashboardPage() {\n  const { user } = useAuth();\n  const { data: dashboardStats, isLoading: statsLoading, error: statsError, refetch: refetchStats } = useDashboardStats();\n  const { data: recentApplications, isLoading: appsLoading } = useRecentApplications(5);\n  const { data: activityTimeline, isLoading: activityLoading } = useActivityTimeline(7);\n  const { data: performanceMetrics, isLoading: metricsLoading } = usePerformanceMetrics();\n\n  const stats = [\n    {\n      name: 'Total Applications',\n      value: dashboardStats?.applications.total || 0,\n      change: '+12%',\n      changeType: 'positive',\n      icon: DocumentTextIcon,\n      color: 'text-blue-600',\n      bgColor: 'bg-blue-50',\n      href: '/applications',\n    },\n    {\n      name: 'Pending Applications',\n      value: dashboardStats?.applications.pending || 0,\n      change: '+5%',\n      changeType: 'neutral',\n      icon: ClockIcon,\n      color: 'text-yellow-600',\n      bgColor: 'bg-yellow-50',\n      href: '/applications?status=submitted',\n    },\n    {\n      name: 'Approved Applications',\n      value: dashboardStats?.applications.approved || 0,\n      change: '+8%',\n      changeType: 'positive',\n      icon: CheckCircleIcon,\n      color: 'text-green-600',\n      bgColor: 'bg-green-50',\n      href: '/applications?status=approved',\n    },\n    {\n      name: 'Rejected Applications',\n      value: dashboardStats?.applications.rejected || 0,\n      change: '-2%',\n      changeType: 'negative',\n      icon: XCircleIcon,\n      color: 'text-red-600',\n      bgColor: 'bg-red-50',\n      href: '/applications?status=rejected',\n    },\n  ];\n\n  const systemStats = [\n    {\n      name: 'Total Users',\n      value: dashboardStats?.users.total || 0,\n      icon: UsersIcon,\n      href: '/users',\n    },\n    {\n      name: 'Departments',\n      value: dashboardStats?.departments.total || 0,\n      icon: BuildingOfficeIcon,\n      href: '/departments',\n    },\n    {\n      name: 'Branches',\n      value: dashboardStats?.branches.total || 0,\n      icon: MapPinIcon,\n      href: '/branches',\n    },\n    {\n      name: 'Files',\n      value: dashboardStats?.files.total || 0,\n      icon: FolderIcon,\n      href: '/files',\n    },\n  ];\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'approved': return 'text-green-600 bg-green-50';\n      case 'rejected': return 'text-red-600 bg-red-50';\n      case 'submitted': return 'text-blue-600 bg-blue-50';\n      case 'under_review': return 'text-yellow-600 bg-yellow-50';\n      case 'draft': return 'text-gray-600 bg-gray-50';\n      default: return 'text-gray-600 bg-gray-50';\n    }\n  };\n\n  const getActivityIcon = (type: string) => {\n    switch (type) {\n      case 'application': return DocumentTextIcon;\n      case 'user': return UsersIcon;\n      default: return DocumentTextIcon;\n    }\n  };\n\n  if (statsError) {\n    return (\n      <ProtectedRoute>\n        <Layout>\n          <div className=\"flex items-center justify-center h-64\">\n            <div className=\"text-center\">\n              <h2 className=\"text-xl font-bold text-gray-900 mb-2\">Error loading dashboard</h2>\n              <p className=\"text-gray-600 mb-4\">Unable to load dashboard statistics</p>\n              <button\n                onClick={() => refetchStats()}\n                className=\"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700\"\n              >\n                <ArrowPathIcon className=\"h-4 w-4 mr-2\" />\n                Retry\n              </button>\n            </div>\n          </div>\n        </Layout>\n      </ProtectedRoute>\n    );\n  }\n\n  return (\n    <ProtectedRoute>\n      <Layout>\n        <div className=\"space-y-6\">\n          {/* Header */}\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h1 className=\"text-3xl font-bold text-gray-900\">Dashboard</h1>\n              <p className=\"text-gray-600\">\n                Welcome back, {user?.first_name}! Here's what's happening with your applications.\n              </p>\n            </div>\n            <div className=\"text-sm text-gray-500\">\n              Last updated: {new Date().toLocaleTimeString()}\n            </div>\n          </div>\n\n          {/* Main Stats Grid */}\n          <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4\">\n            {stats.map((stat) => (\n              <Link key={stat.name} href={stat.href}>\n                <div className=\"overflow-hidden rounded-lg bg-white px-4 py-5 shadow hover:shadow-md transition-shadow cursor-pointer sm:p-6\">\n                  <div className=\"flex items-center\">\n                    <div className={`flex-shrink-0 rounded-md p-3 ${stat.bgColor}`}>\n                      <stat.icon className={`h-6 w-6 ${stat.color}`} aria-hidden=\"true\" />\n                    </div>\n                    <div className=\"ml-5 w-0 flex-1\">\n                      <dl>\n                        <dt className=\"truncate text-sm font-medium text-gray-500\">{stat.name}</dt>\n                        <dd className=\"flex items-baseline\">\n                          <div className=\"text-2xl font-semibold text-gray-900\">\n                            {statsLoading ? (\n                              <div className=\"animate-pulse bg-gray-200 h-8 w-16 rounded\"></div>\n                            ) : (\n                              stat.value.toLocaleString()\n                            )}\n                          </div>\n                          {stat.change && (\n                            <div className={`ml-2 flex items-baseline text-sm font-semibold ${\n                              stat.changeType === 'positive' ? 'text-green-600' : \n                              stat.changeType === 'negative' ? 'text-red-600' : 'text-gray-600'\n                            }`}>\n                              {stat.change}\n                            </div>\n                          )}\n                        </dd>\n                      </dl>\n                    </div>\n                  </div>\n                </div>\n              </Link>\n            ))}\n          </div>\n\n          {/* Performance Metrics */}\n          {performanceMetrics && (\n            <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4\">\n              <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n                <div className=\"p-5\">\n                  <div className=\"flex items-center\">\n                    <div className=\"flex-shrink-0\">\n                      <ChartBarIcon className=\"h-6 w-6 text-gray-400\" />\n                    </div>\n                    <div className=\"ml-5 w-0 flex-1\">\n                      <dl>\n                        <dt className=\"text-sm font-medium text-gray-500 truncate\">Processed (30d)</dt>\n                        <dd className=\"text-lg font-medium text-gray-900\">\n                          {metricsLoading ? (\n                            <div className=\"animate-pulse bg-gray-200 h-6 w-12 rounded\"></div>\n                          ) : (\n                            performanceMetrics.applications_processed_30d\n                          )}\n                        </dd>\n                      </dl>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n                <div className=\"p-5\">\n                  <div className=\"flex items-center\">\n                    <div className=\"flex-shrink-0\">\n                      <ClockIcon className=\"h-6 w-6 text-gray-400\" />\n                    </div>\n                    <div className=\"ml-5 w-0 flex-1\">\n                      <dl>\n                        <dt className=\"text-sm font-medium text-gray-500 truncate\">Avg. Processing</dt>\n                        <dd className=\"text-lg font-medium text-gray-900\">\n                          {metricsLoading ? (\n                            <div className=\"animate-pulse bg-gray-200 h-6 w-16 rounded\"></div>\n                          ) : (\n                            `${performanceMetrics.average_processing_time_days} days`\n                          )}\n                        </dd>\n                      </dl>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n                <div className=\"p-5\">\n                  <div className=\"flex items-center\">\n                    <div className=\"flex-shrink-0\">\n                      <ArrowTrendingUpIcon className=\"h-6 w-6 text-gray-400\" />\n                    </div>\n                    <div className=\"ml-5 w-0 flex-1\">\n                      <dl>\n                        <dt className=\"text-sm font-medium text-gray-500 truncate\">Approval Rate</dt>\n                        <dd className=\"text-lg font-medium text-gray-900\">\n                          {metricsLoading ? (\n                            <div className=\"animate-pulse bg-gray-200 h-6 w-12 rounded\"></div>\n                          ) : (\n                            `${performanceMetrics.approval_rate_percentage}%`\n                          )}\n                        </dd>\n                      </dl>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n                <div className=\"p-5\">\n                  <div className=\"flex items-center\">\n                    <div className=\"flex-shrink-0\">\n                      <FolderIcon className=\"h-6 w-6 text-gray-400\" />\n                    </div>\n                    <div className=\"ml-5 w-0 flex-1\">\n                      <dl>\n                        <dt className=\"text-sm font-medium text-gray-500 truncate\">Storage Used</dt>\n                        <dd className=\"text-lg font-medium text-gray-900\">\n                          {statsLoading ? (\n                            <div className=\"animate-pulse bg-gray-200 h-6 w-16 rounded\"></div>\n                          ) : (\n                            formatBytes(dashboardStats?.files.total_size || 0)\n                          )}\n                        </dd>\n                      </dl>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* Content Grid */}\n          <div className=\"grid grid-cols-1 gap-6 lg:grid-cols-3\">\n            {/* Recent Applications */}\n            <div className=\"lg:col-span-2\">\n              <div className=\"bg-white shadow rounded-lg\">\n                <div className=\"px-4 py-5 sm:p-6\">\n                  <div className=\"flex items-center justify-between mb-4\">\n                    <h3 className=\"text-lg font-medium text-gray-900\">ពាក្យសុំកម្ចីថ្មីៗ</h3>\n                    <Link href=\"/applications\" className=\"text-sm text-blue-600 hover:text-blue-800\">\n                      មើលទាំងអស់\n                    </Link>\n                  </div>\n                  \n                  {appsLoading ? (\n                    <div className=\"space-y-3\">\n                      {[...Array(3)].map((_, i) => (\n                        <div key={i} className=\"animate-pulse flex items-center space-x-4\">\n                          <div className=\"rounded-full bg-gray-200 h-10 w-10\"></div>\n                          <div className=\"flex-1 space-y-2\">\n                            <div className=\"h-4 bg-gray-200 rounded w-3/4\"></div>\n                            <div className=\"h-3 bg-gray-200 rounded w-1/2\"></div>\n                          </div>\n                        </div>\n                      ))}\n                    </div>\n                  ) : recentApplications && recentApplications.length > 0 ? (\n                    <div className=\"space-y-4\">\n                      {recentApplications.map((app) => (\n                        <Link key={app.id} href={`/applications/${app.id}`}>\n                          <div className=\"flex items-center justify-between p-4 hover:bg-gray-50 rounded-lg border border-gray-100 hover:border-blue-200 transition-all cursor-pointer\">\n                            <div className=\"flex items-center space-x-4\">\n                              <div className=\"flex-shrink-0\">\n                                <div className=\"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center\">\n                                  <DocumentTextIcon className=\"h-6 w-6 text-blue-600\" />\n                                </div>\n                              </div>\n                              <div className=\"flex-1\">\n                                <div className=\"flex items-center space-x-2 mb-1\">\n                                  <p className=\"text-sm font-semibold text-gray-900\">\n                                    {app.full_name_khmer || app.full_name_latin || 'មិនបានបញ្ជាក់ឈ្មោះ'}\n                                  </p>\n                                  {app.full_name_khmer && app.full_name_latin && (\n                                    <span className=\"text-xs text-gray-500\">({app.full_name_latin})</span>\n                                  )}\n                                </div>\n                                <div className=\"flex items-center space-x-4 text-sm text-gray-600\">\n                                  <span className=\"flex items-center\">\n                                    <CurrencyDollarIcon className=\"w-4 h-4 mr-1\" />\n                                    {app.requested_amount ? formatCurrency(app.requested_amount) : 'មិនបានបញ្ជាក់'}\n                                  </span>\n                                  {app.phone && (\n                                    <span className=\"flex items-center\">\n                                      <PhoneIcon className=\"w-4 h-4 mr-1\" />\n                                      {app.phone}\n                                    </span>\n                                  )}\n                                  {app.portfolio_officer_name && (\n                                    <span className=\"flex items-center\">\n                                      <UserIcon className=\"w-4 h-4 mr-1\" />\n                                      {app.portfolio_officer_name}\n                                    </span>\n                                  )}\n                                </div>\n                              </div>\n                            </div>\n                            <div className=\"flex items-center space-x-3\">\n                              <div className=\"text-right\">\n                                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(app.status)}`}>\n                                  {app.status === 'draft' && 'ព្រាង'}\n                                  {app.status === 'submitted' && 'បានដាក់ស្នើ'}\n                                  {app.status === 'under_review' && 'កំពុងពិនិត្យ'}\n                                  {app.status === 'approved' && 'អនុម័ត'}\n                                  {app.status === 'rejected' && 'បដិសេធ'}\n                                </span>\n                                <p className=\"text-xs text-gray-500 mt-1\">\n                                  {formatDate(app.created_at)}\n                                </p>\n                              </div>\n                              <ArrowTrendingUpIcon className=\"w-4 h-4 text-gray-400\" />\n                            </div>\n                          </div>\n                        </Link>\n                      ))}\n                    </div>\n                  ) : (\n                    <div className=\"text-center py-8\">\n                      <DocumentTextIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n                      <h3 className=\"mt-2 text-sm font-medium text-gray-900\">មិនមានពាក្យសុំថ្មី</h3>\n                      <p className=\"mt-1 text-sm text-gray-500\">\n                        ពាក្យសុំនឹងបង្ហាញនៅទីនេះនៅពេលដែលត្រូវបានដាក់ស្នើ\n                      </p>\n                      <div className=\"mt-4\">\n                        <Link\n                          href=\"/applications/new\"\n                          className=\"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 text-sm\"\n                        >\n                          <PlusIcon className=\"w-4 h-4 mr-2\" />\n                          បង្កើតពាក្យសុំថ្មី\n                        </Link>\n                      </div>\n                    </div>\n                  )}\n                </div>\n              </div>\n            </div>\n\n            {/* System Overview & Activity */}\n            <div className=\"space-y-6\">\n              {/* System Overview */}\n              <div className=\"bg-white shadow rounded-lg\">\n                <div className=\"px-4 py-5 sm:p-6\">\n                  <h3 className=\"text-lg font-medium text-gray-900 mb-4\">System Overview</h3>\n                  <div className=\"space-y-4\">\n                    {systemStats.map((stat) => (\n                      <Link key={stat.name} href={stat.href}>\n                        <div className=\"flex items-center justify-between hover:bg-gray-50 p-2 rounded cursor-pointer\">\n                          <div className=\"flex items-center\">\n                            <stat.icon className=\"h-5 w-5 text-gray-400 mr-3\" />\n                            <span className=\"text-sm font-medium text-gray-900\">{stat.name}</span>\n                          </div>\n                          <span className=\"text-sm text-gray-600\">\n                            {statsLoading ? (\n                              <div className=\"animate-pulse bg-gray-200 h-4 w-8 rounded\"></div>\n                            ) : (\n                              stat.value.toLocaleString()\n                            )}\n                          </span>\n                        </div>\n                      </Link>\n                    ))}\n                  </div>\n                </div>\n              </div>\n\n              {/* Recent Activity */}\n              <div className=\"bg-white shadow rounded-lg\">\n                <div className=\"px-4 py-5 sm:p-6\">\n                  <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Recent Activity</h3>\n                  \n                  {activityLoading ? (\n                    <div className=\"space-y-3\">\n                      {[...Array(4)].map((_, i) => (\n                        <div key={i} className=\"animate-pulse flex items-start space-x-3\">\n                          <div className=\"rounded-full bg-gray-200 h-8 w-8\"></div>\n                          <div className=\"flex-1 space-y-2\">\n                            <div className=\"h-3 bg-gray-200 rounded w-3/4\"></div>\n                            <div className=\"h-3 bg-gray-200 rounded w-1/2\"></div>\n                          </div>\n                        </div>\n                      ))}\n                    </div>\n                  ) : activityTimeline && activityTimeline.length > 0 ? (\n                    <div className=\"space-y-4\">\n                      {activityTimeline.slice(0, 5).map((activity) => {\n                        const ActivityIcon = getActivityIcon(activity.type);\n                        return (\n                          <div key={activity.id} className=\"flex items-start space-x-3\">\n                            <div className=\"flex-shrink-0\">\n                              <ActivityIcon className=\"h-6 w-6 text-gray-400\" />\n                            </div>\n                            <div className=\"min-w-0 flex-1\">\n                              <p className=\"text-sm font-medium text-gray-900\">{activity.title}</p>\n                              <p className=\"text-sm text-gray-500\">{activity.description}</p>\n                              <p className=\"text-xs text-gray-400 mt-1\">\n                                {formatDate(activity.timestamp)}\n                              </p>\n                            </div>\n                          </div>\n                        );\n                      })}\n                    </div>\n                  ) : (\n                    <div className=\"text-center py-6\">\n                      <CalendarIcon className=\"mx-auto h-8 w-8 text-gray-400\" />\n                      <p className=\"mt-2 text-sm text-gray-500\">No recent activity</p>\n                    </div>\n                  )}\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </Layout>\n    </ProtectedRoute>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAMA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAkBA;AACA;;;AA9BA;;;;;;;;AAgCe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,EAAE,MAAM,cAAc,EAAE,WAAW,YAAY,EAAE,OAAO,UAAU,EAAE,SAAS,YAAY,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,oBAAiB,AAAD;IACpH,MAAM,EAAE,MAAM,kBAAkB,EAAE,WAAW,WAAW,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,wBAAqB,AAAD,EAAE;IACnF,MAAM,EAAE,MAAM,gBAAgB,EAAE,WAAW,eAAe,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,sBAAmB,AAAD,EAAE;IACnF,MAAM,EAAE,MAAM,kBAAkB,EAAE,WAAW,cAAc,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,wBAAqB,AAAD;IAEpF,MAAM,QAAQ;QACZ;YACE,MAAM;YACN,OAAO,CAAA,2BAAA,qCAAA,eAAgB,YAAY,CAAC,KAAK,KAAI;YAC7C,QAAQ;YACR,YAAY;YACZ,MAAM,kOAAA,CAAA,mBAAgB;YACtB,OAAO;YACP,SAAS;YACT,MAAM;QACR;QACA;YACE,MAAM;YACN,OAAO,CAAA,2BAAA,qCAAA,eAAgB,YAAY,CAAC,OAAO,KAAI;YAC/C,QAAQ;YACR,YAAY;YACZ,MAAM,oNAAA,CAAA,YAAS;YACf,OAAO;YACP,SAAS;YACT,MAAM;QACR;QACA;YACE,MAAM;YACN,OAAO,CAAA,2BAAA,qCAAA,eAAgB,YAAY,CAAC,QAAQ,KAAI;YAChD,QAAQ;YACR,YAAY;YACZ,MAAM,gOAAA,CAAA,kBAAe;YACrB,OAAO;YACP,SAAS;YACT,MAAM;QACR;QACA;YACE,MAAM;YACN,OAAO,CAAA,2BAAA,qCAAA,eAAgB,YAAY,CAAC,QAAQ,KAAI;YAChD,QAAQ;YACR,YAAY;YACZ,MAAM,wNAAA,CAAA,cAAW;YACjB,OAAO;YACP,SAAS;YACT,MAAM;QACR;KACD;IAED,MAAM,cAAc;QAClB;YACE,MAAM;YACN,OAAO,CAAA,2BAAA,qCAAA,eAAgB,KAAK,CAAC,KAAK,KAAI;YACtC,MAAM,oNAAA,CAAA,YAAS;YACf,MAAM;QACR;QACA;YACE,MAAM;YACN,OAAO,CAAA,2BAAA,qCAAA,eAAgB,WAAW,CAAC,KAAK,KAAI;YAC5C,MAAM,sOAAA,CAAA,qBAAkB;YACxB,MAAM;QACR;QACA;YACE,MAAM;YACN,OAAO,CAAA,2BAAA,qCAAA,eAAgB,QAAQ,CAAC,KAAK,KAAI;YACzC,MAAM,sNAAA,CAAA,aAAU;YAChB,MAAM;QACR;QACA;YACE,MAAM;YACN,OAAO,CAAA,2BAAA,qCAAA,eAAgB,KAAK,CAAC,KAAK,KAAI;YACtC,MAAM,sNAAA,CAAA,aAAU;YAChB,MAAM;QACR;KACD;IAED,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAgB,OAAO;YAC5B,KAAK;gBAAS,OAAO;YACrB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAQ;YACN,KAAK;gBAAe,OAAO,kOAAA,CAAA,mBAAgB;YAC3C,KAAK;gBAAQ,OAAO,oNAAA,CAAA,YAAS;YAC7B;gBAAS,OAAO,kOAAA,CAAA,mBAAgB;QAClC;IACF;IAEA,IAAI,YAAY;QACd,qBACE,6LAAC,+IAAA,CAAA,iBAAc;sBACb,cAAA,6LAAC,yIAAA,CAAA,SAAM;0BACL,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAuC;;;;;;0CACrD,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAClC,6LAAC;gCACC,SAAS,IAAM;gCACf,WAAU;;kDAEV,6LAAC,4NAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQxD;IAEA,qBACE,6LAAC,+IAAA,CAAA,iBAAc;kBACb,cAAA,6LAAC,yIAAA,CAAA,SAAM;sBACL,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAmC;;;;;;kDACjD,6LAAC;wCAAE,WAAU;;4CAAgB;4CACZ,iBAAA,2BAAA,KAAM,UAAU;4CAAC;;;;;;;;;;;;;0CAGpC,6LAAC;gCAAI,WAAU;;oCAAwB;oCACtB,IAAI,OAAO,kBAAkB;;;;;;;;;;;;;kCAKhD,6LAAC;wBAAI,WAAU;kCACZ,MAAM,GAAG,CAAC,CAAC,qBACV,6LAAC,+JAAA,CAAA,UAAI;gCAAiB,MAAM,KAAK,IAAI;0CACnC,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAW,AAAC,gCAA4C,OAAb,KAAK,OAAO;0DAC1D,cAAA,6LAAC,KAAK,IAAI;oDAAC,WAAW,AAAC,WAAqB,OAAX,KAAK,KAAK;oDAAI,eAAY;;;;;;;;;;;0DAE7D,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAA8C,KAAK,IAAI;;;;;;sEACrE,6LAAC;4DAAG,WAAU;;8EACZ,6LAAC;oEAAI,WAAU;8EACZ,6BACC,6LAAC;wEAAI,WAAU;;;;;+EAEf,KAAK,KAAK,CAAC,cAAc;;;;;;gEAG5B,KAAK,MAAM,kBACV,6LAAC;oEAAI,WAAW,AAAC,kDAGhB,OAFC,KAAK,UAAU,KAAK,aAAa,mBACjC,KAAK,UAAU,KAAK,aAAa,iBAAiB;8EAEjD,KAAK,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;+BAtBjB,KAAK,IAAI;;;;;;;;;;oBAmCvB,oCACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,0NAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;;;;;;0DAE1B,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAA6C;;;;;;sEAC3D,6LAAC;4DAAG,WAAU;sEACX,+BACC,6LAAC;gEAAI,WAAU;;;;;uEAEf,mBAAmB,0BAA0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAS3D,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,oNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;;;;;;0DAEvB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAA6C;;;;;;sEAC3D,6LAAC;4DAAG,WAAU;sEACX,+BACC,6LAAC;gEAAI,WAAU;;;;;uEAEf,AAAC,GAAkD,OAAhD,mBAAmB,4BAA4B,EAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CASjE,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,wOAAA,CAAA,sBAAmB;oDAAC,WAAU;;;;;;;;;;;0DAEjC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAA6C;;;;;;sEAC3D,6LAAC;4DAAG,WAAU;sEACX,+BACC,6LAAC;gEAAI,WAAU;;;;;uEAEf,AAAC,GAA8C,OAA5C,mBAAmB,wBAAwB,EAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAS7D,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,sNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;0DAExB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAA6C;;;;;;sEAC3D,6LAAC;4DAAG,WAAU;sEACX,6BACC,6LAAC;gEAAI,WAAU;;;;;uEAEf,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE,CAAA,2BAAA,qCAAA,eAAgB,KAAK,CAAC,UAAU,KAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAYlE,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAoC;;;;;;kEAClD,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAgB,WAAU;kEAA4C;;;;;;;;;;;;4CAKlF,4BACC,6LAAC;gDAAI,WAAU;0DACZ;uDAAI,MAAM;iDAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC;wDAAY,WAAU;;0EACrB,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;;;;;kFACf,6LAAC;wEAAI,WAAU;;;;;;;;;;;;;uDAJT;;;;;;;;;uDASZ,sBAAsB,mBAAmB,MAAM,GAAG,kBACpD,6LAAC;gDAAI,WAAU;0DACZ,mBAAmB,GAAG,CAAC,CAAC,oBACvB,6LAAC,+JAAA,CAAA,UAAI;wDAAc,MAAM,AAAC,iBAAuB,OAAP,IAAI,EAAE;kEAC9C,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;sFACb,cAAA,6LAAC;gFAAI,WAAU;0FACb,cAAA,6LAAC,kOAAA,CAAA,mBAAgB;oFAAC,WAAU;;;;;;;;;;;;;;;;sFAGhC,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAI,WAAU;;sGACb,6LAAC;4FAAE,WAAU;sGACV,IAAI,eAAe,IAAI,IAAI,eAAe,IAAI;;;;;;wFAEhD,IAAI,eAAe,IAAI,IAAI,eAAe,kBACzC,6LAAC;4FAAK,WAAU;;gGAAwB;gGAAE,IAAI,eAAe;gGAAC;;;;;;;;;;;;;8FAGlE,6LAAC;oFAAI,WAAU;;sGACb,6LAAC;4FAAK,WAAU;;8GACd,6LAAC,sOAAA,CAAA,qBAAkB;oGAAC,WAAU;;;;;;gGAC7B,IAAI,gBAAgB,GAAG,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,IAAI,gBAAgB,IAAI;;;;;;;wFAEhE,IAAI,KAAK,kBACR,6LAAC;4FAAK,WAAU;;8GACd,6LAAC,oNAAA,CAAA,YAAS;oGAAC,WAAU;;;;;;gGACpB,IAAI,KAAK;;;;;;;wFAGb,IAAI,sBAAsB,kBACzB,6LAAC;4FAAK,WAAU;;8GACd,6LAAC,kNAAA,CAAA,WAAQ;oGAAC,WAAU;;;;;;gGACnB,IAAI,sBAAsB;;;;;;;;;;;;;;;;;;;;;;;;;8EAMrC,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAK,WAAW,AAAC,2EAAqG,OAA3B,eAAe,IAAI,MAAM;;wFAClH,IAAI,MAAM,KAAK,WAAW;wFAC1B,IAAI,MAAM,KAAK,eAAe;wFAC9B,IAAI,MAAM,KAAK,kBAAkB;wFACjC,IAAI,MAAM,KAAK,cAAc;wFAC7B,IAAI,MAAM,KAAK,cAAc;;;;;;;8FAEhC,6LAAC;oFAAE,WAAU;8FACV,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,IAAI,UAAU;;;;;;;;;;;;sFAG9B,6LAAC,wOAAA,CAAA,sBAAmB;4EAAC,WAAU;;;;;;;;;;;;;;;;;;uDAlD1B,IAAI,EAAE;;;;;;;;;qEAyDrB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,kOAAA,CAAA,mBAAgB;wDAAC,WAAU;;;;;;kEAC5B,6LAAC;wDAAG,WAAU;kEAAyC;;;;;;kEACvD,6LAAC;wDAAE,WAAU;kEAA6B;;;;;;kEAG1C,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;4DACH,MAAK;4DACL,WAAU;;8EAEV,6LAAC,kNAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAWnD,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAyC;;;;;;8DACvD,6LAAC;oDAAI,WAAU;8DACZ,YAAY,GAAG,CAAC,CAAC,qBAChB,6LAAC,+JAAA,CAAA,UAAI;4DAAiB,MAAM,KAAK,IAAI;sEACnC,cAAA,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;0FACb,6LAAC,KAAK,IAAI;gFAAC,WAAU;;;;;;0FACrB,6LAAC;gFAAK,WAAU;0FAAqC,KAAK,IAAI;;;;;;;;;;;;kFAEhE,6LAAC;wEAAK,WAAU;kFACb,6BACC,6LAAC;4EAAI,WAAU;;;;;mFAEf,KAAK,KAAK,CAAC,cAAc;;;;;;;;;;;;2DAVtB,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;kDAqB5B,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAyC;;;;;;gDAEtD,gCACC,6LAAC;oDAAI,WAAU;8DACZ;2DAAI,MAAM;qDAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC;4DAAY,WAAU;;8EACrB,6LAAC;oEAAI,WAAU;;;;;;8EACf,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;;;;;;sFACf,6LAAC;4EAAI,WAAU;;;;;;;;;;;;;2DAJT;;;;;;;;;2DASZ,oBAAoB,iBAAiB,MAAM,GAAG,kBAChD,6LAAC;oDAAI,WAAU;8DACZ,iBAAiB,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;wDACjC,MAAM,eAAe,gBAAgB,SAAS,IAAI;wDAClD,qBACE,6LAAC;4DAAsB,WAAU;;8EAC/B,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAa,WAAU;;;;;;;;;;;8EAE1B,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAE,WAAU;sFAAqC,SAAS,KAAK;;;;;;sFAChE,6LAAC;4EAAE,WAAU;sFAAyB,SAAS,WAAW;;;;;;sFAC1D,6LAAC;4EAAE,WAAU;sFACV,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,SAAS,SAAS;;;;;;;;;;;;;2DAR1B,SAAS,EAAE;;;;;oDAazB;;;;;yEAGF,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,0NAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;sEACxB,6LAAC;4DAAE,WAAU;sEAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWhE;GAlcwB;;QACL,0HAAA,CAAA,UAAO;QAC4E,+HAAA,CAAA,oBAAiB;QACxD,+HAAA,CAAA,wBAAqB;QACnB,+HAAA,CAAA,sBAAmB;QAClB,+HAAA,CAAA,wBAAqB;;;KAL/D", "debugId": null}}]}