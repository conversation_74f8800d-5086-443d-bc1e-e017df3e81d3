{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/backend/lc-workflow-frontend/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\nexport function formatBytes(bytes: number, decimals = 2): string {\n  if (bytes === 0) return '0 Bytes';\n\n  const k = 1024;\n  const dm = decimals < 0 ? 0 : decimals;\n  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];\n\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];\n}\n\nexport function formatDate(dateString: string): string {\n  const date = new Date(dateString);\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  }).format(date);\n}\n\nexport function formatCurrency(amount: number, currency = 'USD'): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency,\n  }).format(amount);\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text;\n  return text.slice(0, maxLength) + '...';\n}\n\nexport function getFileIcon(mimeType: string): string {\n  if (mimeType.startsWith('image/')) return '🖼️';\n  if (mimeType === 'application/pdf') return '📄';\n  if (mimeType.includes('word')) return '📝';\n  if (mimeType.includes('excel') || mimeType.includes('spreadsheet')) return '📊';\n  if (mimeType.startsWith('text/')) return '📄';\n  return '📎';\n}\n\nexport function validateEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\nexport function validatePhone(phone: string): boolean {\n  const phoneRegex = /^\\+?[\\d\\s\\-\\(\\)]+$/;\n  return phoneRegex.test(phone) && phone.replace(/\\D/g, '').length >= 10;\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9);\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean;\n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args);\n      inThrottle = true;\n      setTimeout(() => (inThrottle = false), limit);\n    }\n  };\n}\n\nexport function isValidUUID(uuid: string): boolean {\n  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;\n  return uuidRegex.test(uuid);\n}\n\nexport function validateUUID(uuid: string): string {\n  if (!isValidUUID(uuid)) {\n    throw new Error('Invalid UUID format');\n  }\n  return uuid;\n}\n\nexport function sanitizeUUID(uuid: string): string {\n  return uuid.replace(/[^a-f0-9-]/gi, '');\n}"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,YAAY,KAAa;QAAE,WAAA,iEAAW;IACpD,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,KAAK,WAAW,IAAI,IAAI;IAC9B,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;KAAK;IAEvE,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,OAAO,MAAM,KAAK,CAAC,EAAE;AAC1E;AAEO,SAAS,WAAW,UAAkB;IAC3C,MAAM,OAAO,IAAI,KAAK;IACtB,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,eAAe,MAAc;QAAE,WAAA,iEAAW;IACxD,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP;IACF,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,aAAa;AACpC;AAEO,SAAS,YAAY,QAAgB;IAC1C,IAAI,SAAS,UAAU,CAAC,WAAW,OAAO;IAC1C,IAAI,aAAa,mBAAmB,OAAO;IAC3C,IAAI,SAAS,QAAQ,CAAC,SAAS,OAAO;IACtC,IAAI,SAAS,QAAQ,CAAC,YAAY,SAAS,QAAQ,CAAC,gBAAgB,OAAO;IAC3E,IAAI,SAAS,UAAU,CAAC,UAAU,OAAO;IACzC,OAAO;AACT;AAEO,SAAS,cAAc,KAAa;IACzC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,cAAc,KAAa;IACzC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC,UAAU,MAAM,OAAO,CAAC,OAAO,IAAI,MAAM,IAAI;AACtE;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO;yCAAI;YAAA;;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,SACd,IAAO,EACP,KAAa;IAEb,IAAI;IACJ,OAAO;yCAAI;YAAA;;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW,IAAO,aAAa,OAAQ;QACzC;IACF;AACF;AAEO,SAAS,YAAY,IAAY;IACtC,MAAM,YAAY;IAClB,OAAO,UAAU,IAAI,CAAC;AACxB;AAEO,SAAS,aAAa,IAAY;IACvC,IAAI,CAAC,YAAY,OAAO;QACtB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAEO,SAAS,aAAa,IAAY;IACvC,OAAO,KAAK,OAAO,CAAC,gBAAgB;AACtC", "debugId": null}}, {"offset": {"line": 137, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/backend/lc-workflow-frontend/src/hooks/useDepartments.ts"], "sourcesContent": ["import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';\nimport { apiClient } from '@/lib/api';\nimport { Department, DepartmentWithRelations, PaginatedResponse } from '@/types/models';\nimport toast from 'react-hot-toast';\nimport { isValidUUID, validateUUID } from '@/lib/utils';\n\n// Department query keys\nexport const departmentKeys = {\n  all: ['departments'] as const,\n  lists: () => [...departmentKeys.all, 'list'] as const,\n  list: (filters: any) => [...departmentKeys.lists(), filters] as const,\n  details: () => [...departmentKeys.all, 'detail'] as const,\n  detail: (id: string) => [...departmentKeys.details(), id] as const,\n  withRelations: (id: string) => [...departmentKeys.detail(id), 'relations'] as const,\n  stats: () => [...departmentKeys.all, 'stats'] as const,\n};\n\n// Department hooks\nexport const useDepartments = (filters: {\n  page?: number;\n  size?: number;\n  search?: string;\n  is_active?: boolean;\n} = {}) => {\n  return useQuery({\n    queryKey: departmentKeys.list(filters),\n    queryFn: () => apiClient.get<PaginatedResponse<Department>>('/departments', {\n      params: filters,\n    }),\n    staleTime: 5 * 60 * 1000, // 5 minutes\n  });\n};\n\nexport const useActiveDepartments = () => {\n  return useQuery({\n    queryKey: departmentKeys.list({ is_active: true }),\n    queryFn: () => apiClient.get<Department[]>('/departments/active'),\n    staleTime: 5 * 60 * 1000,\n  });\n};\n\nexport const useDepartment = (id: string) => {\n  return useQuery({\n    queryKey: departmentKeys.detail(id),\n    queryFn: () => apiClient.get<Department>(`/departments/${id}`),\n    staleTime: 5 * 60 * 1000,\n    enabled: !!id && isValidUUID(id),\n  });\n};\n\nexport const useCreateDepartment = () => {\n  const queryClient = useQueryClient();\n\n  return useMutation({\n    mutationFn: (data: { name: string; code: string; description?: string; manager_id?: string }) =>\n      apiClient.post<Department>('/departments', data),\n    onSuccess: () => {\n      queryClient.invalidateQueries({ queryKey: departmentKeys.lists() });\n      toast.success('Department created successfully!');\n    },\n    onError: (error: any) => {\n      const message = error.response?.data?.detail || 'Failed to create department';\n      toast.error(message);\n    },\n  });\n};\n\nexport const useUpdateDepartment = (id: string) => {\n  const queryClient = useQueryClient();\n\n  return useMutation({\n    mutationFn: (data: { name?: string; code?: string; description?: string; manager_id?: string; is_active?: boolean }) => {\n      validateUUID(id, 'Department');\n      return apiClient.patch<Department>(`/departments/${id}`, data);\n    },\n    onSuccess: (updatedDepartment) => {\n      queryClient.setQueryData(departmentKeys.detail(id), updatedDepartment);\n      queryClient.invalidateQueries({ queryKey: departmentKeys.lists() });\n      toast.success('Department updated successfully!');\n    },\n    onError: (error: any) => {\n      const message = error.response?.data?.detail || 'Failed to update department';\n      toast.error(message);\n    },\n  });\n};\n\nexport const useDeleteDepartment = () => {\n  const queryClient = useQueryClient();\n\n  return useMutation({\n    mutationFn: (id: string) => {\n      validateUUID(id, 'Department');\n      return apiClient.delete(`/departments/${id}`);\n    },\n    onSuccess: () => {\n      queryClient.invalidateQueries({ queryKey: departmentKeys.lists() });\n      toast.success('Department deleted successfully!');\n    },\n    onError: (error: any) => {\n      const message = error.response?.data?.detail || 'Failed to delete department';\n      toast.error(message);\n    },\n  });\n};\n\n// Enhanced hooks with relationship queries\n\n/**\n * Hook to fetch departments with counts optimized for list views\n * Uses the enhanced backend API with include_counts parameter\n */\nexport const useDepartmentsWithCounts = (filters: {\n  page?: number;\n  size?: number;\n  search?: string;\n  is_active?: boolean;\n} = {}) => {\n  return useQuery({\n    queryKey: departmentKeys.list({ ...filters, include_counts: true }),\n    queryFn: () => apiClient.get<PaginatedResponse<Department>>('/departments', {\n      params: { ...filters, include_counts: true },\n    }),\n    staleTime: 5 * 60 * 1000,\n  });\n};\n\n/**\n * Hook to fetch a single department with all its relationships\n * Includes manager, users, branches with full details\n */\nexport const useDepartmentWithRelations = (id: string) => {\n  return useQuery({\n    queryKey: departmentKeys.withRelations(id),\n    queryFn: async () => {\n      try {\n        // First try the enhanced endpoint with relations if it exists\n        // Note: apiClient.get already extracts response.data, so departmentData is the actual data\n        const departmentData = await apiClient.get<DepartmentWithRelations>(`/departments/${id}/with-relations`);\n        \n        return {\n          ...departmentData,\n          users: departmentData.users || [],\n          branches: departmentData.branches || [],\n          user_count: departmentData.user_count || 0,\n          branch_count: departmentData.branch_count || 0,\n          active_user_count: departmentData.active_user_count || 0,\n        };\n      } catch (error: any) {\n        console.error('API Error:', error);\n        // Fallback to multiple API calls if the enhanced endpoint doesn't exist\n        if (error.response?.status === 404) {\n          // Fetch department basic info\n          const departmentResponse = await apiClient.get<Department>(`/departments/${id}`);\n          \n          // Fetch department statistics (includes user count)\n          const statsResponse = await apiClient.get(`/departments/${id}/stats`);\n          \n          // Fetch users of department\n          const usersResponse = await apiClient.get(`/departments/${id}/users`);\n\n          // Create extended department object\n          const departmentWithRelations: DepartmentWithRelations = {\n            ...departmentResponse,\n            users: usersResponse || [],\n            branches: [], // We'll implement branches later if needed\n            user_count: (statsResponse as any)?.user_count || 0,\n            branch_count: (statsResponse as any)?.branch_count || 0,\n            active_user_count: (usersResponse as any)?.filter((user: any) => user.status === 'active')?.length || 0,\n          };\n\n          return departmentWithRelations;\n        }\n        throw error;\n      }\n    },\n    staleTime: 5 * 60 * 1000,\n    enabled: !!id && isValidUUID(id),\n  });\n};\n\n/**\n * Hook to fetch department statistics\n * Useful for dashboard views\n */\nexport const useDepartmentStats = () => {\n  return useQuery({\n    queryKey: departmentKeys.stats(),\n    queryFn: () => apiClient.get('/departments/stats'),\n    staleTime: 10 * 60 * 1000, // 10 minutes\n  });\n};\n\n/**\n * Hook to fetch department summary with key metrics\n * Optimized for dashboard cards and overview displays\n */\nexport const useDepartmentSummary = (id: string) => {\n  return useQuery({\n    queryKey: [...departmentKeys.detail(id), 'summary'],\n    queryFn: () => apiClient.get(`/departments/${id}/summary`),\n    staleTime: 5 * 60 * 1000,\n    enabled: !!id && isValidUUID(id),\n  });\n};\n\n// Mutation hook with relationship invalidation\nexport const useUpdateDepartmentWithRelations = () => {\n  const queryClient = useQueryClient();\n\n  return useMutation({\n    mutationFn: ({ departmentId, departmentData }: {\n      departmentId: string;\n      departmentData: { name?: string; code?: string; description?: string; manager_id?: string; is_active?: boolean };\n    }) => {\n      validateUUID(departmentId, 'Department');\n      return apiClient.patch<Department>(`/departments/${departmentId}`, departmentData);\n    },\n    onSuccess: (updatedDepartment, variables) => {\n      const { departmentId } = variables;\n      \n      // Update all related queries\n      queryClient.setQueryData(departmentKeys.detail(departmentId), updatedDepartment);\n      queryClient.invalidateQueries({ queryKey: departmentKeys.withRelations(departmentId) });\n      queryClient.invalidateQueries({ queryKey: departmentKeys.lists() });\n      queryClient.invalidateQueries({ queryKey: departmentKeys.stats() });\n      \n      toast.success('Department updated successfully!');\n    },\n    onError: (error: any) => {\n      const message = error.response?.data?.detail || 'Failed to update department';\n      toast.error(message);\n    },\n  });\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;AAAA;AACA;AAEA;AACA;;;;;;AAGO,MAAM,iBAAiB;IAC5B,KAAK;QAAC;KAAc;IACpB,OAAO,IAAM;eAAI,eAAe,GAAG;YAAE;SAAO;IAC5C,MAAM,CAAC,UAAiB;eAAI,eAAe,KAAK;YAAI;SAAQ;IAC5D,SAAS,IAAM;eAAI,eAAe,GAAG;YAAE;SAAS;IAChD,QAAQ,CAAC,KAAe;eAAI,eAAe,OAAO;YAAI;SAAG;IACzD,eAAe,CAAC,KAAe;eAAI,eAAe,MAAM,CAAC;YAAK;SAAY;IAC1E,OAAO,IAAM;eAAI,eAAe,GAAG;YAAE;SAAQ;AAC/C;AAGO,MAAM,iBAAiB;QAAC,2EAK3B,CAAC;;IACH,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,eAAe,IAAI,CAAC;QAC9B,OAAO;uCAAE,IAAM,oHAAA,CAAA,YAAS,CAAC,GAAG,CAAgC,gBAAgB;oBAC1E,QAAQ;gBACV;;QACA,WAAW,IAAI,KAAK;IACtB;AACF;GAba;;QAMJ,8KAAA,CAAA,WAAQ;;;AASV,MAAM,uBAAuB;;IAClC,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,eAAe,IAAI,CAAC;YAAE,WAAW;QAAK;QAChD,OAAO;6CAAE,IAAM,oHAAA,CAAA,YAAS,CAAC,GAAG,CAAe;;QAC3C,WAAW,IAAI,KAAK;IACtB;AACF;IANa;;QACJ,8KAAA,CAAA,WAAQ;;;AAOV,MAAM,gBAAgB,CAAC;;IAC5B,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,eAAe,MAAM,CAAC;QAChC,OAAO;sCAAE,IAAM,oHAAA,CAAA,YAAS,CAAC,GAAG,CAAa,AAAC,gBAAkB,OAAH;;QACzD,WAAW,IAAI,KAAK;QACpB,SAAS,CAAC,CAAC,MAAM,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE;IAC/B;AACF;IAPa;;QACJ,8KAAA,CAAA,WAAQ;;;AAQV,MAAM,sBAAsB;;IACjC,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;+CAAE,CAAC,OACX,oHAAA,CAAA,YAAS,CAAC,IAAI,CAAa,gBAAgB;;QAC7C,SAAS;+CAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU,eAAe,KAAK;gBAAG;gBACjE,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;YAChB;;QACA,OAAO;+CAAE,CAAC;oBACQ,sBAAA;gBAAhB,MAAM,UAAU,EAAA,kBAAA,MAAM,QAAQ,cAAd,uCAAA,uBAAA,gBAAgB,IAAI,cAApB,2CAAA,qBAAsB,MAAM,KAAI;gBAChD,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACd;;IACF;AACF;IAfa;;QACS,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAcb,MAAM,sBAAsB,CAAC;;IAClC,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;+CAAE,CAAC;gBACX,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,IAAI;gBACjB,OAAO,oHAAA,CAAA,YAAS,CAAC,KAAK,CAAa,AAAC,gBAAkB,OAAH,KAAM;YAC3D;;QACA,SAAS;+CAAE,CAAC;gBACV,YAAY,YAAY,CAAC,eAAe,MAAM,CAAC,KAAK;gBACpD,YAAY,iBAAiB,CAAC;oBAAE,UAAU,eAAe,KAAK;gBAAG;gBACjE,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;YAChB;;QACA,OAAO;+CAAE,CAAC;oBACQ,sBAAA;gBAAhB,MAAM,UAAU,EAAA,kBAAA,MAAM,QAAQ,cAAd,uCAAA,uBAAA,gBAAgB,IAAI,cAApB,2CAAA,qBAAsB,MAAM,KAAI;gBAChD,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACd;;IACF;AACF;IAlBa;;QACS,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAiBb,MAAM,sBAAsB;;IACjC,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;+CAAE,CAAC;gBACX,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,IAAI;gBACjB,OAAO,oHAAA,CAAA,YAAS,CAAC,MAAM,CAAC,AAAC,gBAAkB,OAAH;YAC1C;;QACA,SAAS;+CAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU,eAAe,KAAK;gBAAG;gBACjE,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;YAChB;;QACA,OAAO;+CAAE,CAAC;oBACQ,sBAAA;gBAAhB,MAAM,UAAU,EAAA,kBAAA,MAAM,QAAQ,cAAd,uCAAA,uBAAA,gBAAgB,IAAI,cAApB,2CAAA,qBAAsB,MAAM,KAAI;gBAChD,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACd;;IACF;AACF;IAjBa;;QACS,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAsBb,MAAM,2BAA2B;QAAC,2EAKrC,CAAC;;IACH,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,eAAe,IAAI,CAAC;YAAE,GAAG,OAAO;YAAE,gBAAgB;QAAK;QACjE,OAAO;iDAAE,IAAM,oHAAA,CAAA,YAAS,CAAC,GAAG,CAAgC,gBAAgB;oBAC1E,QAAQ;wBAAE,GAAG,OAAO;wBAAE,gBAAgB;oBAAK;gBAC7C;;QACA,WAAW,IAAI,KAAK;IACtB;AACF;IAba;;QAMJ,8KAAA,CAAA,WAAQ;;;AAaV,MAAM,6BAA6B,CAAC;;IACzC,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,eAAe,aAAa,CAAC;QACvC,OAAO;mDAAE;gBACP,IAAI;oBACF,8DAA8D;oBAC9D,2FAA2F;oBAC3F,MAAM,iBAAiB,MAAM,oHAAA,CAAA,YAAS,CAAC,GAAG,CAA0B,AAAC,gBAAkB,OAAH,IAAG;oBAEvF,OAAO;wBACL,GAAG,cAAc;wBACjB,OAAO,eAAe,KAAK,IAAI,EAAE;wBACjC,UAAU,eAAe,QAAQ,IAAI,EAAE;wBACvC,YAAY,eAAe,UAAU,IAAI;wBACzC,cAAc,eAAe,YAAY,IAAI;wBAC7C,mBAAmB,eAAe,iBAAiB,IAAI;oBACzD;gBACF,EAAE,OAAO,OAAY;wBAGf;oBAFJ,QAAQ,KAAK,CAAC,cAAc;oBAC5B,wEAAwE;oBACxE,IAAI,EAAA,kBAAA,MAAM,QAAQ,cAAd,sCAAA,gBAAgB,MAAM,MAAK,KAAK;4BAepB,OACE,QACK,SAAA;wBAhBrB,8BAA8B;wBAC9B,MAAM,qBAAqB,MAAM,oHAAA,CAAA,YAAS,CAAC,GAAG,CAAa,AAAC,gBAAkB,OAAH;wBAE3E,oDAAoD;wBACpD,MAAM,gBAAgB,MAAM,oHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,gBAAkB,OAAH,IAAG;wBAE7D,4BAA4B;wBAC5B,MAAM,gBAAgB,MAAM,oHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,gBAAkB,OAAH,IAAG;wBAE7D,oCAAoC;wBACpC,MAAM,0BAAmD;4BACvD,GAAG,kBAAkB;4BACrB,OAAO,iBAAiB,EAAE;4BAC1B,UAAU,EAAE;4BACZ,YAAY,EAAA,QAAC,2BAAD,4BAAA,MAAwB,UAAU,KAAI;4BAClD,cAAc,EAAA,SAAC,2BAAD,6BAAA,OAAwB,YAAY,KAAI;4BACtD,mBAAmB,EAAA,SAAC,2BAAD,8BAAA,UAAA,OAAwB,MAAM;uEAAC,CAAC,OAAc,KAAK,MAAM,KAAK;mFAA9D,8BAAA,QAAyE,MAAM,KAAI;wBACxG;wBAEA,OAAO;oBACT;oBACA,MAAM;gBACR;YACF;;QACA,WAAW,IAAI,KAAK;QACpB,SAAS,CAAC,CAAC,MAAM,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE;IAC/B;AACF;IAhDa;;QACJ,8KAAA,CAAA,WAAQ;;;AAqDV,MAAM,qBAAqB;;IAChC,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,eAAe,KAAK;QAC9B,OAAO;2CAAE,IAAM,oHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;;QAC7B,WAAW,KAAK,KAAK;IACvB;AACF;IANa;;QACJ,8KAAA,CAAA,WAAQ;;;AAWV,MAAM,uBAAuB,CAAC;;IACnC,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;eAAI,eAAe,MAAM,CAAC;YAAK;SAAU;QACnD,OAAO;6CAAE,IAAM,oHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,AAAC,gBAAkB,OAAH,IAAG;;QAChD,WAAW,IAAI,KAAK;QACpB,SAAS,CAAC,CAAC,MAAM,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE;IAC/B;AACF;IAPa;;QACJ,8KAAA,CAAA,WAAQ;;;AASV,MAAM,mCAAmC;;IAC9C,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;4DAAE;oBAAC,EAAE,YAAY,EAAE,cAAc,EAG1C;gBACC,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,cAAc;gBAC3B,OAAO,oHAAA,CAAA,YAAS,CAAC,KAAK,CAAa,AAAC,gBAA4B,OAAb,eAAgB;YACrE;;QACA,SAAS;4DAAE,CAAC,mBAAmB;gBAC7B,MAAM,EAAE,YAAY,EAAE,GAAG;gBAEzB,6BAA6B;gBAC7B,YAAY,YAAY,CAAC,eAAe,MAAM,CAAC,eAAe;gBAC9D,YAAY,iBAAiB,CAAC;oBAAE,UAAU,eAAe,aAAa,CAAC;gBAAc;gBACrF,YAAY,iBAAiB,CAAC;oBAAE,UAAU,eAAe,KAAK;gBAAG;gBACjE,YAAY,iBAAiB,CAAC;oBAAE,UAAU,eAAe,KAAK;gBAAG;gBAEjE,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;YAChB;;QACA,OAAO;4DAAE,CAAC;oBACQ,sBAAA;gBAAhB,MAAM,UAAU,EAAA,kBAAA,MAAM,QAAQ,cAAd,uCAAA,uBAAA,gBAAgB,IAAI,cAApB,2CAAA,qBAAsB,MAAM,KAAI;gBAChD,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACd;;IACF;AACF;KA3Ba;;QACS,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW", "debugId": null}}, {"offset": {"line": 505, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/backend/lc-workflow-frontend/src/components/layout/Sidebar.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { \n  HomeIcon, \n  DocumentTextIcon, \n  UsersIcon, \n  BuildingOfficeIcon, \n  MapPinIcon, \n  Cog6ToothIcon,\n  FolderIcon,\n  ChevronDownIcon,\n  ChevronRightIcon,\n  XMarkIcon,\n  Bars3Icon,\n} from '@heroicons/react/24/outline';\nimport { useAuthContext } from '@/providers/AuthProvider';\n\ninterface NavItem {\n  name: string;\n  href: string;\n  icon: React.ComponentType<{ className?: string }>;\n  requiredRoles?: string[];\n}\n\nconst navigation: NavItem[] = [\n  { name: 'Dashboard', href: '/dashboard', icon: HomeIcon },\n  { name: 'Applications', href: '/applications', icon: DocumentTextIcon },\n  { name: 'Files', href: '/files', icon: FolderIcon },\n];\n\nconst adminNavigation: NavItem[] = [\n  { name: 'Users', href: '/users', icon: UsersIcon, requiredRoles: ['admin', 'manager'] },\n  { name: 'Departments', href: '/departments', icon: BuildingOfficeIcon, requiredRoles: ['admin', 'manager'] },\n  { name: 'Branches', href: '/branches', icon: MapPinIcon, requiredRoles: ['admin', 'manager'] },\n  { name: 'Settings', href: '/settings', icon: Cog6ToothIcon, requiredRoles: ['admin'] },\n];\n\ninterface SidebarProps {\n  isOpen: boolean;\n  onClose: () => void;\n}\n\nexport function Sidebar({ isOpen, onClose }: SidebarProps) {\n  const pathname = usePathname();\n  const { user, isAdmin, isManager } = useAuthContext();\n\n  const hasRequiredRole = (requiredRoles?: string[]) => {\n    if (!requiredRoles) return true;\n    return isAdmin || (isManager && requiredRoles.includes('manager'));\n  };\n\n  const allNavigation = [...navigation, ...adminNavigation];\n\n  const NavLink = ({ item }: { item: NavItem }) => {\n    const isActive = pathname === item.href;\n    \n    if (!hasRequiredRole(item.requiredRoles)) return null;\n\n    return (\n      <Link\n        href={item.href}\n        className={`group flex items-center gap-x-3 rounded-md p-2 text-sm font-semibold leading-6 transition-all duration-200 ${\n          isActive\n            ? 'bg-blue-600 text-white'\n            : 'text-gray-700 hover:bg-gray-50 hover:text-blue-600'\n        }`}\n        onClick={onClose}\n      >\n        <item.icon className=\"h-5 w-5 shrink-0\" />\n        {item.name}\n      </Link>\n    );\n  };\n\n  return (\n    <>\n      {/* Mobile sidebar */}\n      <div\n        className={`fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:hidden ${\n          isOpen ? 'translate-x-0' : '-translate-x-full'\n        }`}\n      >\n        <div className=\"flex h-16 items-center justify-between px-4\">\n          <div className=\"text-xl font-bold text-gray-900\">LC Workflow</div>\n          <button\n            type=\"button\"\n            className=\"-m-2.5 p-2.5 text-gray-700\"\n            onClick={onClose}\n          >\n            <XMarkIcon className=\"h-6 w-6\" />\n          </button>\n        </div>\n        <nav className=\"flex flex-1 flex-col gap-y-2 p-4\">\n          {allNavigation.map((item) => (\n            <NavLink key={item.name} item={item} />\n          ))}\n        </nav>\n      </div>\n\n      {/* Desktop sidebar */}\n      <div className=\"hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-64 lg:flex-col\">\n        <div className=\"flex grow flex-col gap-y-5 overflow-y-auto bg-white px-6 ring-1 ring-gray-200\">\n          <div className=\"flex h-16 shrink-0 items-center\">\n            <div className=\"text-xl font-bold text-gray-900\">LC Workflow</div>\n          </div>\n          <nav className=\"flex flex-1 flex-col gap-y-2\">\n            {allNavigation.map((item) => (\n              <NavLink key={item.name} item={item} />\n            ))}\n          </nav>\n          \n          {/* User info */}\n          {user && (\n            <div className=\"mb-4\">\n              <div className=\"flex items-center gap-x-3 rounded-md p-2 text-sm font-medium text-gray-700\">\n                <div className=\"h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center\">\n                  <span className=\"text-sm font-medium text-gray-600\">\n                    {user.first_name.charAt(0)}{user.last_name.charAt(0)}\n                  </span>\n                </div>\n                <div>\n                  <div className=\"font-medium\">{user.first_name} {user.last_name}</div>\n                  <div className=\"text-xs text-gray-500\">{user.role}</div>\n                </div>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </>\n  );\n}"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;;;AAlBA;;;;;AA2BA,MAAM,aAAwB;IAC5B;QAAE,MAAM;QAAa,MAAM;QAAc,MAAM,kNAAA,CAAA,WAAQ;IAAC;IACxD;QAAE,MAAM;QAAgB,MAAM;QAAiB,MAAM,kOAAA,CAAA,mBAAgB;IAAC;IACtE;QAAE,MAAM;QAAS,MAAM;QAAU,MAAM,sNAAA,CAAA,aAAU;IAAC;CACnD;AAED,MAAM,kBAA6B;IACjC;QAAE,MAAM;QAAS,MAAM;QAAU,MAAM,oNAAA,CAAA,YAAS;QAAE,eAAe;YAAC;YAAS;SAAU;IAAC;IACtF;QAAE,MAAM;QAAe,MAAM;QAAgB,MAAM,sOAAA,CAAA,qBAAkB;QAAE,eAAe;YAAC;YAAS;SAAU;IAAC;IAC3G;QAAE,MAAM;QAAY,MAAM;QAAa,MAAM,sNAAA,CAAA,aAAU;QAAE,eAAe;YAAC;YAAS;SAAU;IAAC;IAC7F;QAAE,MAAM;QAAY,MAAM;QAAa,MAAM,4NAAA,CAAA,gBAAa;QAAE,eAAe;YAAC;SAAQ;IAAC;CACtF;AAOM,SAAS,QAAQ,KAAiC;QAAjC,EAAE,MAAM,EAAE,OAAO,EAAgB,GAAjC;;IACtB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,iBAAc,AAAD;IAElD,MAAM,kBAAkB,CAAC;QACvB,IAAI,CAAC,eAAe,OAAO;QAC3B,OAAO,WAAY,aAAa,cAAc,QAAQ,CAAC;IACzD;IAEA,MAAM,gBAAgB;WAAI;WAAe;KAAgB;IAEzD,MAAM,UAAU;YAAC,EAAE,IAAI,EAAqB;QAC1C,MAAM,WAAW,aAAa,KAAK,IAAI;QAEvC,IAAI,CAAC,gBAAgB,KAAK,aAAa,GAAG,OAAO;QAEjD,qBACE,6LAAC,+JAAA,CAAA,UAAI;YACH,MAAM,KAAK,IAAI;YACf,WAAW,AAAC,8GAIX,OAHC,WACI,2BACA;YAEN,SAAS;;8BAET,6LAAC,KAAK,IAAI;oBAAC,WAAU;;;;;;gBACpB,KAAK,IAAI;;;;;;;IAGhB;IAEA,qBACE;;0BAEE,6LAAC;gBACC,WAAW,AAAC,yHAEX,OADC,SAAS,kBAAkB;;kCAG7B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAAkC;;;;;;0CACjD,6LAAC;gCACC,MAAK;gCACL,WAAU;gCACV,SAAS;0CAET,cAAA,6LAAC,oNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAGzB,6LAAC;wBAAI,WAAU;kCACZ,cAAc,GAAG,CAAC,CAAC,qBAClB,6LAAC;gCAAwB,MAAM;+BAAjB,KAAK,IAAI;;;;;;;;;;;;;;;;0BAM7B,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CAAkC;;;;;;;;;;;sCAEnD,6LAAC;4BAAI,WAAU;sCACZ,cAAc,GAAG,CAAC,CAAC,qBAClB,6LAAC;oCAAwB,MAAM;mCAAjB,KAAK,IAAI;;;;;;;;;;wBAK1B,sBACC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;;gDACb,KAAK,UAAU,CAAC,MAAM,CAAC;gDAAI,KAAK,SAAS,CAAC,MAAM,CAAC;;;;;;;;;;;;kDAGtD,6LAAC;;0DACC,6LAAC;gDAAI,WAAU;;oDAAe,KAAK,UAAU;oDAAC;oDAAE,KAAK,SAAS;;;;;;;0DAC9D,6LAAC;gDAAI,WAAU;0DAAyB,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASnE;GAzFgB;;QACG,qIAAA,CAAA,cAAW;QACS,oIAAA,CAAA,iBAAc;;;KAFrC", "debugId": null}}, {"offset": {"line": 804, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/backend/lc-workflow-frontend/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useRef, useEffect } from 'react';\r\nimport { Bars3Icon, BellIcon, UserCircleIcon, ArrowRightOnRectangleIcon, Cog6ToothIcon } from '@heroicons/react/24/outline';\r\nimport { useLogout } from '@/hooks/useAuth';\r\nimport { useAuthContext } from '@/providers/AuthProvider';\r\nimport Link from 'next/link';\r\n\r\ninterface HeaderProps {\r\n  onMenuClick: () => void;\r\n}\r\n\r\nexport function Header({ onMenuClick }: HeaderProps) {\r\n  const logout = useLogout();\r\n  const { user } = useAuthContext();\r\n  const [profileDropdownOpen, setProfileDropdownOpen] = useState(false);\r\n  const dropdownRef = useRef<HTMLDivElement>(null);\r\n\r\n  // Close dropdown when clicking outside\r\n  useEffect(() => {\r\n    function handleClickOutside(event: MouseEvent) {\r\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\r\n        setProfileDropdownOpen(false);\r\n      }\r\n    }\r\n\r\n    document.addEventListener('mousedown', handleClickOutside);\r\n    return () => {\r\n      document.removeEventListener('mousedown', handleClickOutside);\r\n    };\r\n  }, []);\r\n\r\n  return (\r\n    <div className=\"sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 bg-white px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8\">\r\n      <button\r\n        type=\"button\"\r\n        className=\"-m-2.5 p-2.5 text-gray-700 lg:hidden\"\r\n        onClick={onMenuClick}\r\n      >\r\n        <span className=\"sr-only\">Open sidebar</span>\r\n        <Bars3Icon className=\"h-6 w-6\" aria-hidden=\"true\" />\r\n      </button>\r\n\r\n      {/* Separator */}\r\n      <div className=\"h-6 w-px bg-gray-900/10 lg:hidden\" aria-hidden=\"true\" />\r\n\r\n      <div className=\"flex flex-1 gap-x-4 self-stretch lg:gap-x-6\">\r\n        <div className=\"flex flex-1\" />\r\n\r\n        <div className=\"flex items-center gap-x-4 lg:gap-x-6\">\r\n          <button\r\n            type=\"button\"\r\n            className=\"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500\"\r\n          >\r\n            <span className=\"sr-only\">View notifications</span>\r\n            <BellIcon className=\"h-6 w-6\" aria-hidden=\"true\" />\r\n          </button>\r\n\r\n          {/* Separator */}\r\n          <div className=\"hidden lg:block lg:h-6 lg:w-px lg:bg-gray-900/10\" aria-hidden=\"true\" />\r\n\r\n          {/* Profile dropdown */}\r\n          <div className=\"relative\">\r\n            <button\r\n              type=\"button\"\r\n              className=\"-m-1.5 flex items-center p-1.5\"\r\n              id=\"user-menu-button\"\r\n              aria-expanded=\"false\"\r\n              aria-haspopup=\"true\"\r\n              onClick={() => setProfileDropdownOpen(!profileDropdownOpen)}\r\n            >\r\n              <span className=\"sr-only\">Open user menu</span>\r\n              <div className=\"h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center\">\r\n                <span className=\"text-sm font-medium text-gray-600\">\r\n                  {user?.first_name?.charAt(0)}{user?.last_name?.charAt(0)}\r\n                </span>\r\n              </div>\r\n              <span className=\"hidden lg:flex lg:items-center\">\r\n                <span className=\"ml-4 text-sm leading-6 text-gray-900\" aria-hidden=\"true\">\r\n                  <span className=\"font-semibold\">{user?.first_name} {user?.last_name}</span>\r\n                  {user?.position?.title ? (\r\n                    <span className=\"ml-2 text-gray-500 truncate max-w-[10rem]\">\r\n                      • {user?.position?.title}\r\n                    </span>\r\n                  ) : null}\r\n                </span>\r\n              </span>\r\n            </button>\r\n\r\n            {/* Dropdown menu */}\r\n            {profileDropdownOpen && (\r\n              <div\r\n                className=\"absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none\"\r\n                role=\"menu\"\r\n                aria-orientation=\"vertical\"\r\n                aria-labelledby=\"user-menu-button\"\r\n                ref={dropdownRef}\r\n              >\r\n                <div className=\"px-4 py-3\">\r\n                  <p className=\"text-sm\">Signed in as</p>\r\n                  <p className=\"truncate text-sm font-medium text-gray-900\">{user?.email}</p>\r\n                </div>\r\n                <div className=\"border-t border-gray-100\"></div>\r\n                <Link\r\n                  href=\"/profile\"\r\n                  className=\"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\r\n                  role=\"menuitem\"\r\n                  onClick={() => setProfileDropdownOpen(false)}\r\n                >\r\n                  <UserCircleIcon className=\"mr-3 h-5 w-5 text-gray-400\" aria-hidden=\"true\" />\r\n                  Your Profile\r\n                </Link>\r\n                <Link\r\n                  href=\"/settings\"\r\n                  className=\"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\r\n                  role=\"menuitem\"\r\n                  onClick={() => setProfileDropdownOpen(false)}\r\n                >\r\n                  <Cog6ToothIcon className=\"mr-3 h-5 w-5 text-gray-400\" aria-hidden=\"true\" />\r\n                  Settings\r\n                </Link>\r\n                <div className=\"border-t border-gray-100\"></div>\r\n                <button\r\n                  type=\"button\"\r\n                  className=\"flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\r\n                  role=\"menuitem\"\r\n                  onClick={() => {\r\n                    setProfileDropdownOpen(false);\r\n                    logout.mutate();\r\n                  }}\r\n                >\r\n                  <ArrowRightOnRectangleIcon className=\"mr-3 h-5 w-5 text-gray-400\" aria-hidden=\"true\" />\r\n                  Sign out\r\n                </button>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;AANA;;;;;;AAYO,SAAS,OAAO,KAA4B;QAA5B,EAAE,WAAW,EAAe,GAA5B;QA8DJ,kBAA6B,iBAM7B,gBAEM;;IArEvB,MAAM,SAAS,CAAA,GAAA,0HAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,iBAAc,AAAD;IAC9B,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAE3C,uCAAuC;IACvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,SAAS,mBAAmB,KAAiB;gBAC3C,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;oBAC9E,uBAAuB;gBACzB;YACF;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC;oCAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;gBAC5C;;QACF;2BAAG,EAAE;IAEL,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACC,MAAK;gBACL,WAAU;gBACV,SAAS;;kCAET,6LAAC;wBAAK,WAAU;kCAAU;;;;;;kCAC1B,6LAAC,oNAAA,CAAA,YAAS;wBAAC,WAAU;wBAAU,eAAY;;;;;;;;;;;;0BAI7C,6LAAC;gBAAI,WAAU;gBAAoC,eAAY;;;;;;0BAE/D,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCAEf,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,MAAK;gCACL,WAAU;;kDAEV,6LAAC;wCAAK,WAAU;kDAAU;;;;;;kDAC1B,6LAAC,kNAAA,CAAA,WAAQ;wCAAC,WAAU;wCAAU,eAAY;;;;;;;;;;;;0CAI5C,6LAAC;gCAAI,WAAU;gCAAmD,eAAY;;;;;;0CAG9E,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,WAAU;wCACV,IAAG;wCACH,iBAAc;wCACd,iBAAc;wCACd,SAAS,IAAM,uBAAuB,CAAC;;0DAEvC,6LAAC;gDAAK,WAAU;0DAAU;;;;;;0DAC1B,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;;wDACb,iBAAA,4BAAA,mBAAA,KAAM,UAAU,cAAhB,uCAAA,iBAAkB,MAAM,CAAC;wDAAI,iBAAA,4BAAA,kBAAA,KAAM,SAAS,cAAf,sCAAA,gBAAiB,MAAM,CAAC;;;;;;;;;;;;0DAG1D,6LAAC;gDAAK,WAAU;0DACd,cAAA,6LAAC;oDAAK,WAAU;oDAAuC,eAAY;;sEACjE,6LAAC;4DAAK,WAAU;;gEAAiB,iBAAA,2BAAA,KAAM,UAAU;gEAAC;gEAAE,iBAAA,2BAAA,KAAM,SAAS;;;;;;;wDAClE,CAAA,iBAAA,4BAAA,iBAAA,KAAM,QAAQ,cAAd,qCAAA,eAAgB,KAAK,kBACpB,6LAAC;4DAAK,WAAU;;gEAA4C;gEACvD,iBAAA,4BAAA,kBAAA,KAAM,QAAQ,cAAd,sCAAA,gBAAgB,KAAK;;;;;;mEAExB;;;;;;;;;;;;;;;;;;oCAMT,qCACC,6LAAC;wCACC,WAAU;wCACV,MAAK;wCACL,oBAAiB;wCACjB,mBAAgB;wCAChB,KAAK;;0DAEL,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAU;;;;;;kEACvB,6LAAC;wDAAE,WAAU;kEAA8C,iBAAA,2BAAA,KAAM,KAAK;;;;;;;;;;;;0DAExE,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,MAAK;gDACL,SAAS,IAAM,uBAAuB;;kEAEtC,6LAAC,8NAAA,CAAA,iBAAc;wDAAC,WAAU;wDAA6B,eAAY;;;;;;oDAAS;;;;;;;0DAG9E,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;gDACV,MAAK;gDACL,SAAS,IAAM,uBAAuB;;kEAEtC,6LAAC,4NAAA,CAAA,gBAAa;wDAAC,WAAU;wDAA6B,eAAY;;;;;;oDAAS;;;;;;;0DAG7E,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDACC,MAAK;gDACL,WAAU;gDACV,MAAK;gDACL,SAAS;oDACP,uBAAuB;oDACvB,OAAO,MAAM;gDACf;;kEAEA,6LAAC,oPAAA,CAAA,4BAAyB;wDAAC,WAAU;wDAA6B,eAAY;;;;;;oDAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUzG;GAjIgB;;QACC,0HAAA,CAAA,YAAS;QACP,oIAAA,CAAA,iBAAc;;;KAFjB", "debugId": null}}, {"offset": {"line": 1178, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/backend/lc-workflow-frontend/src/components/layout/Layout.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Sidebar } from './Sidebar';\nimport { Header } from './Header';\n\ninterface LayoutProps {\n  children: React.ReactNode;\n}\n\nexport function Layout({ children }: LayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n\n  return (\n    <>\n      <div className=\"min-h-full\">\n        <Sidebar \n          isOpen={sidebarOpen} \n          onClose={() => setSidebarOpen(false)} \n        />\n        \n        <div className=\"lg:pl-64\">\n          <Header onMenuClick={() => setSidebarOpen(true)} />\n          \n          <main className=\"py-10\">\n            <div className=\"px-4 sm:px-6 lg:px-8\">\n              {children}\n            </div>\n          </main>\n        </div>\n      </div>\n    </>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAUO,SAAS,OAAO,KAAyB;QAAzB,EAAE,QAAQ,EAAe,GAAzB;;IACrB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,qBACE;kBACE,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,0IAAA,CAAA,UAAO;oBACN,QAAQ;oBACR,SAAS,IAAM,eAAe;;;;;;8BAGhC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,yIAAA,CAAA,SAAM;4BAAC,aAAa,IAAM,eAAe;;;;;;sCAE1C,6LAAC;4BAAK,WAAU;sCACd,cAAA,6LAAC;gCAAI,WAAU;0CACZ;;;;;;;;;;;;;;;;;;;;;;;;AAOf;GAvBgB;KAAA", "debugId": null}}, {"offset": {"line": 1259, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/backend/lc-workflow-frontend/src/components/auth/ProtectedRoute.tsx"], "sourcesContent": ["'use client';\n\nimport { useRouter } from 'next/navigation';\nimport { useAuthContext } from '@/providers/AuthProvider';\nimport { useEffect } from 'react';\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode;\n  requiredRoles?: string[];\n}\n\nexport function ProtectedRoute({ children, requiredRoles }: ProtectedRouteProps) {\n  const router = useRouter();\n  const { isAuthenticated, isLoading, user, role } = useAuthContext();\n\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      if (!isLoading) {\n        if (!isAuthenticated) {\n          router.push('/login');\n        } else if (requiredRoles && requiredRoles.length > 0 && !requiredRoles.includes(role || '')) {\n          router.push('/unauthorized');\n        }\n      }\n    }, 1000); // Wait 1 second for auth state to stabilize\n\n    return () => clearTimeout(timer);\n  }, [isAuthenticated, isLoading, requiredRoles, role, router]);\n\n  if (isLoading || !isAuthenticated) {\n    return (\n      <div className=\"flex min-h-screen items-center justify-center\">\n        <div className=\"h-8 w-8 animate-spin rounded-full border-4 border-blue-600 border-t-transparent\"></div>\n      </div>\n    );\n  }\n\n  if (requiredRoles && requiredRoles.length > 0 && !requiredRoles.includes(role || '')) {\n    return null;\n  }\n\n  return <>{children}</>;\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAWO,SAAS,eAAe,KAAgD;QAAhD,EAAE,QAAQ,EAAE,aAAa,EAAuB,GAAhD;;IAC7B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,iBAAc,AAAD;IAEhE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM,QAAQ;kDAAW;oBACvB,IAAI,CAAC,WAAW;wBACd,IAAI,CAAC,iBAAiB;4BACpB,OAAO,IAAI,CAAC;wBACd,OAAO,IAAI,iBAAiB,cAAc,MAAM,GAAG,KAAK,CAAC,cAAc,QAAQ,CAAC,QAAQ,KAAK;4BAC3F,OAAO,IAAI,CAAC;wBACd;oBACF;gBACF;iDAAG,OAAO,4CAA4C;YAEtD;4CAAO,IAAM,aAAa;;QAC5B;mCAAG;QAAC;QAAiB;QAAW;QAAe;QAAM;KAAO;IAE5D,IAAI,aAAa,CAAC,iBAAiB;QACjC,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,iBAAiB,cAAc,MAAM,GAAG,KAAK,CAAC,cAAc,QAAQ,CAAC,QAAQ,KAAK;QACpF,OAAO;IACT;IAEA,qBAAO;kBAAG;;AACZ;GA/BgB;;QACC,qIAAA,CAAA,YAAS;QAC2B,oIAAA,CAAA,iBAAc;;;KAFnD", "debugId": null}}, {"offset": {"line": 1342, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/backend/lc-workflow-frontend/src/app/departments/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useDepartmentsWithCounts, useDeleteDepartment } from '@/hooks/useDepartments';\nimport { Department } from '@/types/models';\nimport { \n  Search, \n  Plus, \n  Edit, \n  Trash2, \n  Building, \n  Users,\n  MoreVertical,\n  Eye\n} from 'lucide-react';\nimport Link from 'next/link';\nimport { Layout } from '@/components/layout/Layout';\nimport { ProtectedRoute } from '@/components/auth/ProtectedRoute';\n\nexport default function DepartmentsPage() {\n  const [searchTerm, setSearchTerm] = useState('');\n  const [currentPage, setCurrentPage] = useState(1);\n  const [showDeleteModal, setShowDeleteModal] = useState<Department | null>(null);\n  const [dropdownOpen, setDropdownOpen] = useState<string | null>(null);\n\n  const pageSize = 10;\n\n  const { \n    data: departmentsData, \n    isLoading, \n    error \n  } = useDepartmentsWithCounts({\n    page: currentPage,\n    size: pageSize,\n    search: searchTerm || undefined,\n  });\n\n  const deleteDepartment = useDeleteDepartment();\n\n  const handleSearch = (e: React.FormEvent) => {\n    e.preventDefault();\n    setCurrentPage(1);\n  };\n\n  const handleDelete = async (department: Department) => {\n    deleteDepartment.mutate(department.id, {\n      onSuccess: () => {\n        setShowDeleteModal(null);\n      },\n    });\n  };\n\n  const totalPages = departmentsData ? Math.ceil(departmentsData.total / pageSize) : 0;\n\n  if (error) {\n    return (\n      <ProtectedRoute>\n        <Layout>\n          <div className=\"flex items-center justify-center h-full\">\n            <div className=\"text-center\">\n              <h2 className=\"text-2xl font-bold text-gray-900\">Error loading departments</h2>\n              <p className=\"text-gray-600 mt-2\">Please try again later.</p>\n            </div>\n          </div>\n        </Layout>\n      </ProtectedRoute>\n    );\n  }\n\n  return (\n    <ProtectedRoute>\n      <Layout>\n        {/* Header */}\n        <div className=\"mb-8\">\n          <div className=\"flex justify-between items-center\">\n            <div>\n              <h1 className=\"text-3xl font-bold text-gray-900\">Departments</h1>\n              <p className=\"text-gray-600\">Manage organizational departments</p>\n            </div>\n            <Link\n              href=\"/departments/new\"\n              className=\"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n            >\n              <Plus className=\"h-4 w-4 mr-2\" />\n              New Department\n            </Link>\n          </div>\n        </div>\n\n        {/* Search and Filters */}\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6\">\n          <form onSubmit={handleSearch} className=\"flex gap-4\">\n            <div className=\"flex-1\">\n              <div className=\"relative\">\n                <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\" />\n                <input\n                  type=\"text\"\n                  placeholder=\"Search departments...\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                />\n              </div>\n            </div>\n            <button\n              type=\"submit\"\n              className=\"px-6 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors\"\n            >\n              Search\n            </button>\n          </form>\n        </div>\n\n        {/* Departments List */}\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200\">\n          {isLoading ? (\n            <div className=\"flex items-center justify-center py-12\">\n              <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n            </div>\n          ) : departmentsData?.items?.length === 0 ? (\n            <div className=\"text-center py-12\">\n              <Building className=\"mx-auto h-12 w-12 text-gray-400\" />\n              <h3 className=\"mt-2 text-sm font-medium text-gray-900\">No departments found</h3>\n              <p className=\"mt-1 text-sm text-gray-500\">\n                {searchTerm ? 'Try adjusting your search criteria.' : 'Get started by creating a new department.'}\n              </p>\n              {!searchTerm && (\n                <div className=\"mt-6\">\n                  <Link\n                    href=\"/departments/new\"\n                    className=\"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n                  >\n                    <Plus className=\"h-4 w-4 mr-2\" />\n                    New Department\n                  </Link>\n                </div>\n              )}\n            </div>\n          ) : (\n            <>\n              {/* Table Header */}\n              <div className=\"px-6 py-3 border-b border-gray-200 bg-gray-50\">\n                <div className=\"grid grid-cols-12 gap-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  <div className=\"col-span-3\">Department</div>\n                  <div className=\"col-span-2\">Code</div>\n                  <div className=\"col-span-3\">Description</div>\n                  <div className=\"col-span-2\">Users</div>\n                  <div className=\"col-span-1\">Created</div>\n                  <div className=\"col-span-1\">Actions</div>\n                </div>\n              </div>\n\n              {/* Table Body */}\n              <div className=\"divide-y divide-gray-200\">\n                {departmentsData?.items?.map((department) => (\n                  <div key={department.id} className=\"px-6 py-4 hover:bg-gray-50\">\n                    <div className=\"grid grid-cols-12 gap-4 items-center\">\n                      <div className=\"col-span-3\">\n                        <div className=\"flex items-center\">\n                          <div className=\"h-10 w-10 bg-blue-100 rounded-lg flex items-center justify-center\">\n                            <Building className=\"h-5 w-5 text-blue-600\" />\n                          </div>\n                          <div className=\"ml-4\">\n                            <div className=\"text-sm font-medium text-gray-900\">\n                              {department.name}\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"col-span-2\">\n                        <div className=\"text-sm font-medium text-gray-900\">\n                          {department.code}\n                        </div>\n                      </div>\n                      <div className=\"col-span-3\">\n                        <div className=\"text-sm text-gray-900\">\n                          {department.description || 'No description'}\n                        </div>\n                      </div>\n                      <div className=\"col-span-2\">\n                        <div className=\"flex items-center text-sm text-gray-900\">\n                          <Users className=\"h-4 w-4 mr-1 text-gray-400\" />\n                          {department?.user_count || 0}\n                        </div>\n                      </div>\n                      <div className=\"col-span-1\">\n                        <div className=\"text-sm text-gray-900\">\n                          {new Date(department.created_at).toLocaleDateString()}\n                        </div>\n                      </div>\n                      <div className=\"col-span-1\">\n                        <div className=\"relative\">\n                          <button\n                            onClick={() => setDropdownOpen(dropdownOpen === department.id ? null : department.id)}\n                            className=\"p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100\"\n                          >\n                            <MoreVertical className=\"h-4 w-4\" />\n                          </button>\n                          {dropdownOpen === department.id && (\n                            <div className=\"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-10\">\n                              <div className=\"py-1\">\n                                <Link\n                                  href={`/departments/${department.id}`}\n                                  className=\"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                                  onClick={() => setDropdownOpen(null)}\n                                >\n                                  <Eye className=\"h-4 w-4 mr-2\" />\n                                  View Details\n                                </Link>\n                                <Link\n                                  href={`/departments/${department.id}/edit`}\n                                  className=\"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                                  onClick={() => setDropdownOpen(null)}\n                                >\n                                  <Edit className=\"h-4 w-4 mr-2\" />\n                                  Edit\n                                </Link>\n                                <button\n                                  onClick={() => {\n                                    setShowDeleteModal(department);\n                                    setDropdownOpen(null);\n                                  }}\n                                  className=\"flex items-center w-full px-4 py-2 text-sm text-red-700 hover:bg-red-50\"\n                                >\n                                  <Trash2 className=\"h-4 w-4 mr-2\" />\n                                  Delete\n                                </button>\n                              </div>\n                            </div>\n                          )}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n\n              {/* Pagination */}\n              {totalPages > 1 && (\n                <div className=\"px-6 py-4 border-t border-gray-200\">\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"text-sm text-gray-700\">\n                      Showing {((currentPage - 1) * pageSize) + 1} to {Math.min(currentPage * pageSize, departmentsData?.total || 0)} of {departmentsData?.total || 0} results\n                    </div>\n                    <div className=\"flex space-x-2\">\n                      <button\n                        onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}\n                        disabled={currentPage === 1}\n                        className=\"px-3 py-1 text-sm border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50\"\n                      >\n                        Previous\n                      </button>\n                      <span className=\"px-3 py-1 text-sm text-gray-700\">\n                        Page {currentPage} of {totalPages}\n                      </span>\n                      <button\n                        onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}\n                        disabled={currentPage === totalPages}\n                        className=\"px-3 py-1 text-sm border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50\"\n                      >\n                        Next\n                      </button>\n                    </div>\n                  </div>\n                </div>\n              )}\n            </>\n          )}\n        </div>\n      \n      {/* Delete Confirmation Modal */}\n      {showDeleteModal && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n          <div className=\"bg-white rounded-lg p-6 max-w-md w-full mx-4\">\n            <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Delete Department</h3>\n            <p className=\"text-gray-600 mb-6\">\n              Are you sure you want to delete \"{showDeleteModal.name}\"? This action cannot be undone.\n            </p>\n            <div className=\"flex justify-end space-x-4\">\n              <button\n                onClick={() => setShowDeleteModal(null)}\n                className=\"px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50\"\n              >\n                Cancel\n              </button>\n              <button\n                onClick={() => handleDelete(showDeleteModal)}\n                disabled={deleteDepartment.isPending}\n                className=\"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50\"\n              >\n                {deleteDepartment.isPending ? 'Deleting...' : 'Delete'}\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Click outside to close dropdown */}\n      {dropdownOpen && (\n        <div\n          className=\"fixed inset-0 z-0\"\n          onClick={() => setDropdownOpen(null)}\n        />\n      )}\n      </Layout>\n    </ProtectedRoute>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;AACA;;;AAjBA;;;;;;;AAmBe,SAAS;QAoGV,wBAmCG;;IAtIf,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB;IAC1E,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAEhE,MAAM,WAAW;IAEjB,MAAM,EACJ,MAAM,eAAe,EACrB,SAAS,EACT,KAAK,EACN,GAAG,CAAA,GAAA,iIAAA,CAAA,2BAAwB,AAAD,EAAE;QAC3B,MAAM;QACN,MAAM;QACN,QAAQ,cAAc;IACxB;IAEA,MAAM,mBAAmB,CAAA,GAAA,iIAAA,CAAA,sBAAmB,AAAD;IAE3C,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,eAAe;IACjB;IAEA,MAAM,eAAe,OAAO;QAC1B,iBAAiB,MAAM,CAAC,WAAW,EAAE,EAAE;YACrC,WAAW;gBACT,mBAAmB;YACrB;QACF;IACF;IAEA,MAAM,aAAa,kBAAkB,KAAK,IAAI,CAAC,gBAAgB,KAAK,GAAG,YAAY;IAEnF,IAAI,OAAO;QACT,qBACE,6LAAC,+IAAA,CAAA,iBAAc;sBACb,cAAA,6LAAC,yIAAA,CAAA,SAAM;0BACL,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;IAM9C;IAEA,qBACE,6LAAC,+IAAA,CAAA,iBAAc;kBACb,cAAA,6LAAC,yIAAA,CAAA,SAAM;;8BAEL,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAmC;;;;;;kDACjD,6LAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;0CAE/B,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;8BAOvC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAK,UAAU;wBAAc,WAAU;;0CACtC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,WAAU;;;;;;;;;;;;;;;;;0CAIhB,6LAAC;gCACC,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;8BAOL,6LAAC;oBAAI,WAAU;8BACZ,0BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;;;;;;;;;+BAEf,CAAA,4BAAA,uCAAA,yBAAA,gBAAiB,KAAK,cAAtB,6CAAA,uBAAwB,MAAM,MAAK,kBACrC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,6MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,6LAAC;gCAAE,WAAU;0CACV,aAAa,wCAAwC;;;;;;4BAEvD,CAAC,4BACA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;6CAOzC;;0CAEE,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAa;;;;;;sDAC5B,6LAAC;4CAAI,WAAU;sDAAa;;;;;;sDAC5B,6LAAC;4CAAI,WAAU;sDAAa;;;;;;sDAC5B,6LAAC;4CAAI,WAAU;sDAAa;;;;;;sDAC5B,6LAAC;4CAAI,WAAU;sDAAa;;;;;;sDAC5B,6LAAC;4CAAI,WAAU;sDAAa;;;;;;;;;;;;;;;;;0CAKhC,6LAAC;gCAAI,WAAU;0CACZ,4BAAA,uCAAA,0BAAA,gBAAiB,KAAK,cAAtB,8CAAA,wBAAwB,GAAG,CAAC,CAAC,2BAC5B,6LAAC;wCAAwB,WAAU;kDACjC,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;;;;;;0EAEtB,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAI,WAAU;8EACZ,WAAW,IAAI;;;;;;;;;;;;;;;;;;;;;;8DAKxB,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;kEACZ,WAAW,IAAI;;;;;;;;;;;8DAGpB,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;kEACZ,WAAW,WAAW,IAAI;;;;;;;;;;;8DAG/B,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;4DAChB,CAAA,uBAAA,iCAAA,WAAY,UAAU,KAAI;;;;;;;;;;;;8DAG/B,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;kEACZ,IAAI,KAAK,WAAW,UAAU,EAAE,kBAAkB;;;;;;;;;;;8DAGvD,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEACC,SAAS,IAAM,gBAAgB,iBAAiB,WAAW,EAAE,GAAG,OAAO,WAAW,EAAE;gEACpF,WAAU;0EAEV,cAAA,6LAAC,6NAAA,CAAA,eAAY;oEAAC,WAAU;;;;;;;;;;;4DAEzB,iBAAiB,WAAW,EAAE,kBAC7B,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,+JAAA,CAAA,UAAI;4EACH,MAAM,AAAC,gBAA6B,OAAd,WAAW,EAAE;4EACnC,WAAU;4EACV,SAAS,IAAM,gBAAgB;;8FAE/B,6LAAC,mMAAA,CAAA,MAAG;oFAAC,WAAU;;;;;;gFAAiB;;;;;;;sFAGlC,6LAAC,+JAAA,CAAA,UAAI;4EACH,MAAM,AAAC,gBAA6B,OAAd,WAAW,EAAE,EAAC;4EACpC,WAAU;4EACV,SAAS,IAAM,gBAAgB;;8FAE/B,6LAAC,8MAAA,CAAA,OAAI;oFAAC,WAAU;;;;;;gFAAiB;;;;;;;sFAGnC,6LAAC;4EACC,SAAS;gFACP,mBAAmB;gFACnB,gBAAgB;4EAClB;4EACA,WAAU;;8FAEV,6LAAC,6MAAA,CAAA,SAAM;oFAAC,WAAU;;;;;;gFAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uCArEzC,WAAW,EAAE;;;;;;;;;;4BAmF1B,aAAa,mBACZ,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;gDAAwB;gDAC3B,CAAC,cAAc,CAAC,IAAI,WAAY;gDAAE;gDAAK,KAAK,GAAG,CAAC,cAAc,UAAU,CAAA,4BAAA,sCAAA,gBAAiB,KAAK,KAAI;gDAAG;gDAAK,CAAA,4BAAA,sCAAA,gBAAiB,KAAK,KAAI;gDAAE;;;;;;;sDAElJ,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,SAAS,IAAM,eAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,OAAO,GAAG;oDACzD,UAAU,gBAAgB;oDAC1B,WAAU;8DACX;;;;;;8DAGD,6LAAC;oDAAK,WAAU;;wDAAkC;wDAC1C;wDAAY;wDAAK;;;;;;;8DAEzB,6LAAC;oDACC,SAAS,IAAM,eAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,OAAO,GAAG;oDACzD,UAAU,gBAAgB;oDAC1B,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAYhB,iCACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,6LAAC;gCAAE,WAAU;;oCAAqB;oCACE,gBAAgB,IAAI;oCAAC;;;;;;;0CAEzD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS,IAAM,mBAAmB;wCAClC,WAAU;kDACX;;;;;;kDAGD,6LAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,UAAU,iBAAiB,SAAS;wCACpC,WAAU;kDAET,iBAAiB,SAAS,GAAG,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;gBAQvD,8BACC,6LAAC;oBACC,WAAU;oBACV,SAAS,IAAM,gBAAgB;;;;;;;;;;;;;;;;;AAMzC;GAhSwB;;QAYlB,iIAAA,CAAA,2BAAwB;QAMH,iIAAA,CAAA,sBAAmB;;;KAlBtB", "debugId": null}}]}